{"name": "mongoose", "description": "Mongoose MongoDB ODM", "version": "5.10.15", "author": "<PERSON> <<EMAIL>>", "keywords": ["mongodb", "document", "model", "schema", "database", "odm", "data", "datastore", "query", "nosql", "orm", "db"], "license": "MIT", "dependencies": {"bson": "^1.1.4", "kareem": "2.3.1", "mongodb": "3.6.3", "mongoose-legacy-pluralize": "1.0.2", "mpath": "0.7.0", "mquery": "3.2.2", "ms": "2.1.2", "regexp-clone": "1.0.0", "safe-buffer": "5.2.1", "sliced": "1.0.1", "sift": "7.0.1"}, "devDependencies": {"@babel/core": "7.10.5", "@babel/preset-env": "7.10.4", "acquit": "1.x", "acquit-ignore": "0.1.x", "acquit-require": "0.1.x", "async": "2.6.2", "babel-loader": "8.1.0", "benchmark": "2.1.4", "bluebird": "3.5.5", "chalk": "2.4.2", "cheerio": "1.0.0-rc.2", "co": "4.6.0", "dox": "0.3.1", "eslint": "7.1.0", "eslint-plugin-mocha-no-only": "1.1.0", "highlight.js": "9.1.0", "lodash.isequal": "4.5.0", "lodash.isequalwith": "4.4.0", "marked": "1.1.1", "mocha": "5.x", "moment": "2.x", "mongodb-topology-manager": "1.0.11", "mongoose-long": "0.2.1", "node-static": "0.7.11", "object-sizeof": "1.3.0", "pug": "2.0.3", "q": "1.5.1", "semver": "5.5.0", "uuid": "2.0.3", "uuid-parse": "1.0.0", "validator": "10.8.0", "webpack": "4.44.0"}, "directories": {"lib": "./lib/mongoose"}, "scripts": {"lint": "eslint .", "build-browser": "node build-browser.js", "prepublishOnly": "npm run build-browser", "release": "git pull && git push origin master --tags && npm publish", "release-legacy": "git pull origin 4.x && git push origin 4.x --tags && npm publish --tag legacy", "test": "mocha --exit", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "main": "./index.js", "engines": {"node": ">=4.0.0"}, "bugs": {"url": "https://github.com/Automattic/mongoose/issues/new"}, "repository": {"type": "git", "url": "git://github.com/Automattic/mongoose.git"}, "homepage": "https://mongoosejs.com", "browser": "./dist/browser.umd.js", "mocha": {"extension": ["test.js"], "watch-files": ["test/**/*.js"]}, "eslintConfig": {"extends": ["eslint:recommended"], "plugins": ["mocha-no-only"], "parserOptions": {"ecmaVersion": 2015}, "env": {"node": true, "es6": true}, "rules": {"comma-style": "error", "indent": ["error", 2, {"SwitchCase": 1, "VariableDeclarator": 2}], "keyword-spacing": "error", "no-whitespace-before-property": "error", "no-buffer-constructor": "warn", "no-console": "off", "no-multi-spaces": "error", "no-constant-condition": "off", "func-call-spacing": "error", "no-trailing-spaces": "error", "no-undef": "error", "no-unneeded-ternary": "error", "no-const-assign": "error", "no-useless-rename": "error", "no-dupe-keys": "error", "space-in-parens": ["error", "never"], "spaced-comment": ["error", "always", {"block": {"markers": ["!"], "balanced": true}}], "key-spacing": ["error", {"beforeColon": false, "afterColon": true}], "comma-spacing": ["error", {"before": false, "after": true}], "array-bracket-spacing": 1, "arrow-spacing": ["error", {"before": true, "after": true}], "object-curly-spacing": ["error", "always"], "comma-dangle": ["error", "never"], "no-unreachable": "error", "quotes": ["error", "single"], "quote-props": ["error", "as-needed"], "semi": "error", "no-extra-semi": "error", "semi-spacing": "error", "no-spaced-func": "error", "no-throw-literal": "error", "space-before-blocks": "error", "space-before-function-paren": ["error", "never"], "space-infix-ops": "error", "space-unary-ops": "error", "no-var": "warn", "prefer-const": "warn", "strict": ["error", "global"], "no-restricted-globals": ["error", {"name": "context", "message": "Don't use <PERSON><PERSON>'s global context"}], "no-prototype-builtins": "off", "mocha-no-only/mocha-no-only": ["error"]}}, "funding": {"type": "opencollective", "url": "https://opencollective.com/mongoose"}}