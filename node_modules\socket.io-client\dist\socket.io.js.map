{"version": 3, "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///socket.io.js", "webpack:///webpack/bootstrap 9e45fccfabd75c66dd47", "webpack:///./lib/index.js", "webpack:///./lib/url.js", "webpack:///./~/parseuri/index.js", "webpack:///./~/debug/src/browser.js", "webpack:///./~/process/browser.js", "webpack:///./~/debug/src/common.js", "webpack:///./~/ms/index.js", "webpack:///./~/socket.io-parser/index.js", "webpack:///./~/socket.io-parser/~/debug/src/browser.js", "webpack:///./~/socket.io-parser/~/debug/src/debug.js", "webpack:///./~/socket.io-parser/~/ms/index.js", "webpack:///./~/component-emitter/index.js", "webpack:///./~/socket.io-parser/binary.js", "webpack:///./~/isarray/index.js", "webpack:///./~/socket.io-parser/is-buffer.js", "webpack:///./lib/manager.js", "webpack:///./~/engine.io-client/lib/index.js", "webpack:///./~/engine.io-client/lib/socket.js", "webpack:///./~/engine.io-client/lib/transports/index.js", "webpack:///./~/engine.io-client/lib/xmlhttprequest.js", "webpack:///./~/has-cors/index.js", "webpack:///./~/engine.io-client/lib/transports/polling-xhr.js", "webpack:///./~/engine.io-client/lib/transports/polling.js", "webpack:///./~/engine.io-client/lib/transport.js", "webpack:///./~/engine.io-parser/lib/browser.js", "webpack:///./~/engine.io-parser/lib/keys.js", "webpack:///./~/has-binary2/index.js", "webpack:///./~/arraybuffer.slice/index.js", "webpack:///./~/after/index.js", "webpack:///./~/engine.io-parser/lib/utf8.js", "webpack:///./~/base64-arraybuffer/lib/base64-arraybuffer.js", "webpack:///./~/blob/index.js", "webpack:///./~/parseqs/index.js", "webpack:///./~/component-inherit/index.js", "webpack:///./~/yeast/index.js", "webpack:///./~/engine.io-client/lib/transports/polling-jsonp.js", "webpack:///./~/engine.io-client/lib/transports/websocket.js", "webpack:///./~/indexof/index.js", "webpack:///./lib/socket.js", "webpack:///./~/to-array/index.js", "webpack:///./lib/on.js", "webpack:///./~/component-bind/index.js", "webpack:///./~/backo2/index.js"], "names": ["root", "factory", "exports", "module", "define", "amd", "this", "modules", "__webpack_require__", "moduleId", "installedModules", "id", "loaded", "call", "m", "c", "p", "lookup", "uri", "opts", "undefined", "io", "parsed", "url", "source", "path", "sameNamespace", "cache", "nsps", "newConnection", "forceNew", "multiplex", "debug", "Manager", "query", "socket", "parser", "managers", "protocol", "connect", "Socket", "loc", "obj", "location", "host", "char<PERSON>t", "test", "parseuri", "port", "ipv6", "indexOf", "href", "re", "parts", "str", "src", "b", "e", "substring", "replace", "length", "exec", "i", "authority", "ipv6uri", "process", "useColors", "window", "type", "__nwjs", "navigator", "userAgent", "toLowerCase", "match", "document", "documentElement", "style", "WebkitAppearance", "console", "firebug", "exception", "table", "parseInt", "RegExp", "$1", "formatArgs", "args", "namespace", "humanize", "diff", "color", "splice", "index", "lastC", "log", "_console", "_typeof", "apply", "arguments", "save", "namespaces", "storage", "setItem", "removeItem", "error", "load", "r", "getItem", "env", "DEBUG", "localstorage", "localStorage", "Symbol", "iterator", "constructor", "prototype", "colors", "formatters", "j", "v", "JSON", "stringify", "message", "defaultSetTimout", "Error", "defaultClearTimeout", "runTimeout", "fun", "cachedSetTimeout", "setTimeout", "runClearTimeout", "marker", "cachedClearTimeout", "clearTimeout", "cleanUpNextTick", "draining", "currentQueue", "queue", "concat", "queueIndex", "drainQueue", "timeout", "len", "run", "<PERSON><PERSON>", "array", "noop", "nextTick", "Array", "push", "title", "browser", "argv", "version", "versions", "on", "addListener", "once", "off", "removeListener", "removeAllListeners", "emit", "prependListener", "prependOnceListener", "listeners", "name", "binding", "cwd", "chdir", "dir", "umask", "_toConsumableArray", "arr", "isArray", "arr2", "from", "setup", "selectColor", "hash", "charCodeAt", "createDebug", "Math", "abs", "_len", "_key", "enabled", "self", "curr", "Number", "Date", "ms", "prevTime", "prev", "coerce", "unshift", "format", "formatter", "val", "logFn", "destroy", "extend", "init", "instances", "delimiter", "newDebug", "enable", "names", "skips", "split", "substr", "instance", "disable", "map", "toNamespace", "join", "regexp", "toString", "stack", "Object", "keys", "for<PERSON>ach", "key", "parse", "String", "n", "parseFloat", "y", "w", "d", "h", "s", "fmtShort", "msAbs", "round", "fmtLong", "plural", "isPlural", "options", "isFinite", "Encoder", "encodeAsString", "BINARY_EVENT", "BINARY_ACK", "attachments", "nsp", "data", "payload", "tryStringify", "ERROR_PACKET", "encodeAsBinary", "callback", "writeEncoding", "bloblessData", "deconstruction", "binary", "deconstructPacket", "pack", "packet", "buffers", "removeBlobs", "Decoder", "reconstructor", "decodeString", "types", "buf", "next", "try<PERSON><PERSON><PERSON>", "isPayloadValid", "ERROR", "BinaryReconstructor", "reconPack", "msg", "Emitter", "isBuf", "CONNECT", "DISCONNECT", "EVENT", "ACK", "encode", "encoding", "add", "base64", "takeBinaryData", "finishedReconstruction", "binData", "reconstructPacket", "Function", "chrome", "local", "err", "bind", "floor", "ceil", "isNaN", "mixin", "addEventListener", "event", "fn", "_callbacks", "removeEventListener", "callbacks", "cb", "slice", "hasListeners", "_deconstructPacket", "placeholder", "_placeholder", "num", "newData", "_reconstructPacket", "withNativeBlob", "Blob", "withNativeFile", "File", "packetData", "_removeBlobs", "cur<PERSON><PERSON>", "containingObject", "pendingBlobs", "fileReader", "FileReader", "onload", "result", "readAsA<PERSON>y<PERSON><PERSON>er", "with<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "buffer", "subs", "reconnection", "reconnectionAttempts", "Infinity", "reconnectionDelay", "reconnectionDelayMax", "randomizationFactor", "backoff", "Backoff", "min", "max", "jitter", "readyState", "connecting", "lastPing", "packetBuffer", "_parser", "encoder", "decoder", "autoConnect", "open", "eio", "has", "hasOwnProperty", "emitAll", "updateSocketIds", "generateId", "engine", "_reconnection", "_reconnectionAttempts", "_reconnectionDelay", "setMin", "_randomizationFactor", "setJitter", "_reconnectionDelayMax", "setMax", "_timeout", "maybeReconnectOnOpen", "reconnecting", "attempts", "reconnect", "skipReconnect", "openSub", "onopen", "errorSub", "cleanup", "timer", "close", "onping", "onpong", "ondata", "ondecoded", "onerror", "onConnecting", "encodedPackets", "write", "processPacketQueue", "shift", "subsLength", "sub", "disconnect", "reset", "onclose", "reason", "delay", "duration", "onreconnect", "attempt", "hostname", "secure", "agent", "parseqs", "decode", "upgrade", "forceJSONP", "jsonp", "forceBase64", "enablesXDR", "withCredentials", "timestampParam", "timestampRequests", "transports", "transportOptions", "writeBuffer", "prevBufferLen", "policyPort", "rememberUpgrade", "binaryType", "onlyBinaryUpgrades", "perMessageDeflate", "threshold", "pfx", "passphrase", "cert", "ca", "ciphers", "rejectUnauthorized", "forceNode", "isReactNative", "product", "extraHeaders", "localAddress", "upgrades", "pingInterval", "pingTimeout", "pingIntervalTimer", "pingTimeoutTimer", "clone", "o", "priorWebsocketSuccess", "Transport", "createTransport", "EIO", "transport", "sid", "requestTimeout", "protocols", "setTransport", "onDrain", "onPacket", "onError", "onClose", "probe", "onTransportOpen", "upgradeLosesBinary", "supportsBinary", "failed", "send", "upgrading", "pause", "flush", "freezeTransport", "onTransportClose", "onupgrade", "to", "onOpen", "l", "onHandshake", "setPing", "code", "filterUpgrades", "onHeartbeat", "ping", "sendPacket", "writable", "compress", "cleanupAndClose", "waitForUpgrade", "desc", "filteredUpgrades", "polling", "xhr", "xd", "xs", "isSSL", "xdomain", "xscheme", "XMLHttpRequest", "XHR", "JSONP", "websocket", "hasCORS", "XDomainRequest", "empty", "Polling", "Request", "method", "async", "isBinary", "create", "unload<PERSON><PERSON><PERSON>", "requests", "abort", "inherit", "request", "doWrite", "req", "sendXhr", "doPoll", "onData", "pollXhr", "setDisableHeaderCheck", "setRequestHeader", "hasXDR", "onLoad", "responseText", "onreadystatechange", "contentType", "getResponseHeader", "responseType", "status", "requestsCount", "onSuccess", "fromError", "response", "attachEvent", "terminationEvent", "hasXHR2", "yeast", "doOpen", "poll", "onPause", "total", "decodePayload", "doClose", "packets", "callbackfn", "encodePayload", "schema", "b64", "description", "decodePacket", "encodeBase64Object", "encode<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "encodeBase64Packet", "contentArray", "Uint8Array", "result<PERSON><PERSON><PERSON>", "byteLength", "encodeBlobAsArrayBuffer", "fr", "encodePacket", "encodeBlob", "dontSendBlobs", "blob", "tryDecode", "utf8", "strict", "ary", "each", "done", "after", "eachWithIndex", "el", "base64encoder", "hasBinary", "sliceBuffer", "isAndroid", "isPhantomJS", "pong", "packetslist", "utf8encode", "encoded", "readAsDataURL", "b64data", "fromCharCode", "typed", "basic", "btoa", "utf8decode", "decodeBase64Packet", "asArray", "rest", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "encodeOne", "doneCallback", "encodePayloadAsBlob", "encodePayloadAsArrayBuffer", "results", "decodePayloadAsBinary", "chr", "ret", "totalLength", "reduce", "acc", "resultArray", "bufferIndex", "isString", "ab", "view", "lenStr", "binaryIdentifier", "size", "lengthAry", "bufferTail", "tailArray", "msg<PERSON><PERSON>th", "toJSON", "arraybuffer", "start", "end", "bytes", "abv", "ii", "count", "err_cb", "proxy", "bail", "ucs2decode", "string", "value", "extra", "output", "counter", "ucs2encode", "stringFromCharCode", "checkScalarValue", "codePoint", "toUpperCase", "createByte", "encodeCodePoint", "symbol", "codePoints", "byteString", "readContinuationByte", "byteIndex", "byteCount", "continuationByte", "byteArray", "decodeSymbol", "byte1", "byte2", "byte3", "byte4", "tmp", "chars", "encoded1", "encoded2", "encoded3", "encoded4", "bufferLength", "mapArrayBufferViews", "chunk", "copy", "set", "byteOffset", "BlobBuilderConstructor", "bb", "BlobBuilder", "part", "append", "getBlob", "BlobConstructor", "WebKitBlobBuilder", "MSBlobBuilder", "MozBlobBuilder", "blobSupported", "a", "blobSupportsArrayBufferView", "blobBuilderSupported", "encodeURIComponent", "qs", "qry", "pairs", "pair", "decodeURIComponent", "alphabet", "decoded", "now", "seed", "global", "glob", "JSONPPolling", "___eio", "script", "rNewline", "rEscapedNewline", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "form", "iframe", "createElement", "insertAt", "getElementsByTagName", "insertBefore", "head", "body", "append<PERSON><PERSON><PERSON>", "isUAgecko", "complete", "initIframe", "html", "iframeId", "area", "className", "position", "top", "left", "target", "setAttribute", "action", "submit", "WS", "usingBrowserWebSocket", "BrowserWebSocket", "WebSocketImpl", "NodeWebSocket", "WebSocket", "MozWebSocket", "check", "headers", "ws", "supports", "addEventListeners", "onmessage", "ev", "json", "ids", "acks", "<PERSON><PERSON><PERSON><PERSON>", "send<PERSON><PERSON><PERSON>", "connected", "disconnected", "flags", "toArray", "hasBin", "events", "connect_error", "connect_timeout", "reconnect_attempt", "reconnect_failed", "reconnect_error", "subEvents", "pop", "onpacket", "rootNamespaceError", "onconnect", "onevent", "onack", "ondisconnect", "ack", "sent", "emitBuffered", "list", "factor", "pow", "rand", "random", "deviation"], "mappings": "CAAA,SAAAA,EAAAC,GACA,gBAAAC,UAAA,gBAAAC,QACAA,OAAAD,QAAAD,IACA,kBAAAG,gBAAAC,IACAD,UAAAH,GACA,gBAAAC,SACAA,QAAA,GAAAD,IAEAD,EAAA,GAAAC,KACCK,KAAA,WACD,MCAgB,UAAUC,GCN1B,QAAAC,GAAAC,GAGA,GAAAC,EAAAD,GACA,MAAAC,GAAAD,GAAAP,OAGA,IAAAC,GAAAO,EAAAD,IACAP,WACAS,GAAAF,EACAG,QAAA,EAUA,OANAL,GAAAE,GAAAI,KAAAV,EAAAD,QAAAC,IAAAD,QAAAM,GAGAL,EAAAS,QAAA,EAGAT,EAAAD,QAvBA,GAAAQ,KAqCA,OATAF,GAAAM,EAAAP,EAGAC,EAAAO,EAAAL,EAGAF,EAAAQ,EAAA,GAGAR,EAAA,KDgBM,SAAUL,EAAQD,EAASM,GEnBjC,QAAAS,GAAAC,EAAAC,GACA,gBAAAD,KACAC,EAAAD,EACAA,EAAAE,QAGAD,OAEA,IAQAE,GARAC,EAAAC,EAAAL,GACAM,EAAAF,EAAAE,OACAb,EAAAW,EAAAX,GACAc,EAAAH,EAAAG,KACAC,EAAAC,EAAAhB,IAAAc,IAAAE,GAAAhB,GAAAiB,KACAC,EAAAV,EAAAW,UAAAX,EAAA,0BACA,IAAAA,EAAAY,WAAAL,CAiBA,OAbAG,IACAG,EAAA,+BAAAR,GACAH,EAAAY,EAAAT,EAAAL,KAEAQ,EAAAhB,KACAqB,EAAA,yBAAAR,GACAG,EAAAhB,GAAAsB,EAAAT,EAAAL,IAEAE,EAAAM,EAAAhB,IAEAW,EAAAY,QAAAf,EAAAe,QACAf,EAAAe,MAAAZ,EAAAY,OAEAb,EAAAc,OAAAb,EAAAG,KAAAN,GA7DA,GAAAI,GAAAf,EAAA,GACA4B,EAAA5B,EAAA,GACAyB,EAAAzB,EAAA,IACAwB,EAAAxB,EAAA,sBAMAL,GAAAD,UAAAe,CAMA,IAAAU,GAAAzB,EAAAmC,WAuDAnC,GAAAoC,SAAAF,EAAAE,SASApC,EAAAqC,QAAAtB,EAQAf,EAAA+B,QAAAzB,EAAA,IACAN,EAAAsC,OAAAhC,EAAA,KF6DM,SAAUL,EAAQD,EAASM,GGnIjC,QAAAe,GAAAL,EAAAuB,GACA,GAAAC,GAAAxB,CAGAuB,MAAA,mBAAAE,oBACA,MAAAzB,MAAAuB,EAAAH,SAAA,KAAAG,EAAAG,MAGA,gBAAA1B,KACA,MAAAA,EAAA2B,OAAA,KAEA3B,EADA,MAAAA,EAAA2B,OAAA,GACAJ,EAAAH,SAAApB,EAEAuB,EAAAG,KAAA1B,GAIA,sBAAA4B,KAAA5B,KACAc,EAAA,uBAAAd,GAEAA,EADA,mBAAAuB,GACAA,EAAAH,SAAA,KAAApB,EAEA,WAAAA,GAKAc,EAAA,WAAAd,GACAwB,EAAAK,EAAA7B,IAIAwB,EAAAM,OACA,cAAAF,KAAAJ,EAAAJ,UACAI,EAAAM,KAAA,KACK,eAAAF,KAAAJ,EAAAJ,YACLI,EAAAM,KAAA,QAIAN,EAAAjB,KAAAiB,EAAAjB,MAAA,GAEA,IAAAwB,GAAAP,EAAAE,KAAAM,QAAA,UACAN,EAAAK,EAAA,IAAAP,EAAAE,KAAA,IAAAF,EAAAE,IAOA,OAJAF,GAAA/B,GAAA+B,EAAAJ,SAAA,MAAAM,EAAA,IAAAF,EAAAM,KAEAN,EAAAS,KAAAT,EAAAJ,SAAA,MAAAM,GAAAH,KAAAO,OAAAN,EAAAM,KAAA,OAAAN,EAAAM,MAEAN,EApEA,GAAAK,GAAAvC,EAAA,GACAwB,EAAAxB,EAAA,0BAMAL,GAAAD,QAAAqB,GH+NM,SAAUpB,EAAQD,GIpOxB,GAAAkD,GAAA,0OAEAC,GACA,iIAGAlD,GAAAD,QAAA,SAAAoD,GACA,GAAAC,GAAAD,EACAE,EAAAF,EAAAJ,QAAA,KACAO,EAAAH,EAAAJ,QAAA,IAEAM,KAAA,GAAAC,IAAA,IACAH,IAAAI,UAAA,EAAAF,GAAAF,EAAAI,UAAAF,EAAAC,GAAAE,QAAA,UAAwEL,EAAAI,UAAAD,EAAAH,EAAAM,QAOxE,KAJA,GAAA9C,GAAAsC,EAAAS,KAAAP,GAAA,IACApC,KACA4C,EAAA,GAEAA,KACA5C,EAAAmC,EAAAS,IAAAhD,EAAAgD,IAAA,EAUA,OAPAN,KAAA,GAAAC,IAAA,IACAvC,EAAAM,OAAA+B,EACArC,EAAA0B,KAAA1B,EAAA0B,KAAAc,UAAA,EAAAxC,EAAA0B,KAAAgB,OAAA,GAAAD,QAAA,KAAwE,KACxEzC,EAAA6C,UAAA7C,EAAA6C,UAAAJ,QAAA,QAAAA,QAAA,QAAAA,QAAA,KAAkF,KAClFzC,EAAA8C,SAAA,GAGA9C,IJmPM,SAAUf,EAAQD,EAASM,IAEJ,SAASyD,GAAU,YKjLhD,SAASC,KAIR,QAAsB,mBAAXC,UAA0BA,OAAOF,SAAoC,aAAxBE,OAAOF,QAAQG,OAAuBD,OAAOF,QAAQI,UAKpF,mBAAdC,aAA6BA,UAAUC,YAAaD,UAAUC,UAAUC,cAAcC,MAAM,4BAM3E,mBAAbC,WAA4BA,SAASC,iBAAmBD,SAASC,gBAAgBC,OAASF,SAASC,gBAAgBC,MAAMC,kBAEpH,mBAAXV,SAA0BA,OAAOW,UAAYX,OAAOW,QAAQC,SAAYZ,OAAOW,QAAQE,WAAab,OAAOW,QAAQG,QAGrG,mBAAdX,YAA6BA,UAAUC,WAAaD,UAAUC,UAAUC,cAAcC,MAAM,mBAAqBS,SAASC,OAAOC,GAAI,KAAO,IAE9H,mBAAdd,YAA6BA,UAAUC,WAAaD,UAAUC,UAAUC,cAAcC,MAAM,uBAStG,QAASY,GAAWC,GAQnB,GAPAA,EAAK,IAAMhF,KAAK4D,UAAY,KAAO,IAClC5D,KAAKiF,WACJjF,KAAK4D,UAAY,MAAQ,KAC1BoB,EAAK,IACJhF,KAAK4D,UAAY,MAAQ,KAC1B,IAAM/D,EAAOD,QAAQsF,SAASlF,KAAKmF,MAE/BnF,KAAK4D,UAAV,CAIA,GAAMnD,GAAI,UAAYT,KAAKoF,KAC3BJ,GAAKK,OAAO,EAAG,EAAG5E,EAAG,iBAKrB,IAAI6E,GAAQ,EACRC,EAAQ,CACZP,GAAK,GAAG3B,QAAQ,cAAe,SAAAc,GAChB,OAAVA,IAGJmB,IACc,OAAVnB,IAGHoB,EAAQD,MAIVN,EAAKK,OAAOE,EAAO,EAAG9E,IASvB,QAAS+E,KAAa,GAAAC,EAGrB,OAA0B,YAAnB,mBAAOjB,SAAP,YAAAkB,EAAOlB,WACbA,QAAQgB,MACRC,EAAAjB,SAAQgB,IAARG,MAAAF,EAAAG,WASF,QAASC,GAAKC,GACb,IACKA,EACHlG,EAAQmG,QAAQC,QAAQ,QAASF,GAEjClG,EAAQmG,QAAQE,WAAW,SAE3B,MAAOC,KAYV,QAASC,KACR,GAAIC,SACJ,KACCA,EAAIxG,EAAQmG,QAAQM,QAAQ,SAC3B,MAAOH,IAUT,OAJKE,GAAwB,mBAAZzC,IAA2B,OAASA,KACpDyC,EAAIzC,EAAQ2C,IAAIC,OAGVH,EAcR,QAASI,KACR,IAGC,MAAOC,cACN,MAAOP,KLyCT,GAAIR,GAA4B,kBAAXgB,SAAoD,gBAApBA,QAAOC,SAAwB,SAAUvE,GAAO,aAAcA,IAAS,SAAUA,GAAO,MAAOA,IAAyB,kBAAXsE,SAAyBtE,EAAIwE,cAAgBF,QAAUtE,IAAQsE,OAAOG,UAAY,eAAkBzE,GKtRvQxC,GAAQ4F,IAAMA,EACd5F,EAAQmF,WAAaA,EACrBnF,EAAQiG,KAAOA,EACfjG,EAAQuG,KAAOA,EACfvG,EAAQgE,UAAYA,EACpBhE,EAAQmG,QAAUS,IAMlB5G,EAAQkH,QACP,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,WA4JDjH,EAAOD,QAAUM,EAAQ,GAAYN,EL8MpC,IK5MMmH,GAAclH,EAAOD,QAArBmH,UAMPA,GAAWC,EAAI,SAAUC,GACxB,IACC,MAAOC,MAAKC,UAAUF,GACrB,MAAOf,GACR,MAAO,+BAAiCA,EAAMkB,YL+MlB7G,KAAKX,EAASM,EAAoB,KAI1D,SAAUL,EAAQD,GM7cxB,QAAAyH,KACA,SAAAC,OAAA,mCAEA,QAAAC,KACA,SAAAD,OAAA,qCAsBA,QAAAE,GAAAC,GACA,GAAAC,IAAAC,WAEA,MAAAA,YAAAF,EAAA,EAGA,KAAAC,IAAAL,IAAAK,IAAAC,WAEA,MADAD,GAAAC,WACAA,WAAAF,EAAA,EAEA,KAEA,MAAAC,GAAAD,EAAA,GACK,MAAAtE,GACL,IAEA,MAAAuE,GAAAnH,KAAA,KAAAkH,EAAA,GACS,MAAAtE,GAET,MAAAuE,GAAAnH,KAAAP,KAAAyH,EAAA,KAMA,QAAAG,GAAAC,GACA,GAAAC,IAAAC,aAEA,MAAAA,cAAAF,EAGA,KAAAC,IAAAP,IAAAO,IAAAC,aAEA,MADAD,GAAAC,aACAA,aAAAF,EAEA,KAEA,MAAAC,GAAAD,GACK,MAAA1E,GACL,IAEA,MAAA2E,GAAAvH,KAAA,KAAAsH,GACS,MAAA1E,GAGT,MAAA2E,GAAAvH,KAAAP,KAAA6H,KAYA,QAAAG,KACAC,GAAAC,IAGAD,GAAA,EACAC,EAAA5E,OACA6E,EAAAD,EAAAE,OAAAD,GAEAE,GAAA,EAEAF,EAAA7E,QACAgF,KAIA,QAAAA,KACA,IAAAL,EAAA,CAGA,GAAAM,GAAAf,EAAAQ,EACAC,IAAA,CAGA,KADA,GAAAO,GAAAL,EAAA7E,OACAkF,GAAA,CAGA,IAFAN,EAAAC,EACAA,OACAE,EAAAG,GACAN,GACAA,EAAAG,GAAAI,KAGAJ,IAAA,EACAG,EAAAL,EAAA7E,OAEA4E,EAAA,KACAD,GAAA,EACAL,EAAAW,IAiBA,QAAAG,GAAAjB,EAAAkB,GACA3I,KAAAyH,MACAzH,KAAA2I,QAYA,QAAAC,MAhKA,GAOAlB,GACAI,EARAnE,EAAA9D,EAAAD,YAgBA,WACA,IAEA8H,EADA,kBAAAC,YACAA,WAEAN,EAEK,MAAAlE,GACLuE,EAAAL,EAEA,IAEAS,EADA,kBAAAC,cACAA,aAEAR,EAEK,MAAApE,GACL2E,EAAAP,KAuDA,IAEAW,GAFAC,KACAF,GAAA,EAEAI,GAAA,CAyCA1E,GAAAkF,SAAA,SAAApB,GACA,GAAAzC,GAAA,GAAA8D,OAAAlD,UAAAtC,OAAA,EACA,IAAAsC,UAAAtC,OAAA,EACA,OAAAE,GAAA,EAAuBA,EAAAoC,UAAAtC,OAAsBE,IAC7CwB,EAAAxB,EAAA,GAAAoC,UAAApC,EAGA2E,GAAAY,KAAA,GAAAL,GAAAjB,EAAAzC,IACA,IAAAmD,EAAA7E,QAAA2E,GACAT,EAAAc,IASAI,EAAA7B,UAAA4B,IAAA,WACAzI,KAAAyH,IAAA9B,MAAA,KAAA3F,KAAA2I,QAEAhF,EAAAqF,MAAA,UACArF,EAAAsF,SAAA,EACAtF,EAAA2C,OACA3C,EAAAuF,QACAvF,EAAAwF,QAAA,GACAxF,EAAAyF,YAIAzF,EAAA0F,GAAAT,EACAjF,EAAA2F,YAAAV,EACAjF,EAAA4F,KAAAX,EACAjF,EAAA6F,IAAAZ,EACAjF,EAAA8F,eAAAb,EACAjF,EAAA+F,mBAAAd,EACAjF,EAAAgG,KAAAf,EACAjF,EAAAiG,gBAAAhB,EACAjF,EAAAkG,oBAAAjB,EAEAjF,EAAAmG,UAAA,SAAAC,GAAqC,UAErCpG,EAAAqG,QAAA,SAAAD,GACA,SAAAzC,OAAA,qCAGA3D,EAAAsG,IAAA,WAA2B,WAC3BtG,EAAAuG,MAAA,SAAAC,GACA,SAAA7C,OAAA,mCAEA3D,EAAAyG,MAAA,WAA4B,WN+dtB,SAAUvK,EAAQD,EAASM,GAEhC,YAEA,SAASmK,GAAmBC,GAAO,GAAIxB,MAAMyB,QAAQD,GAAM,CAAE,IAAK,GAAI9G,GAAI,EAAGgH,EAAO1B,MAAMwB,EAAIhH,QAASE,EAAI8G,EAAIhH,OAAQE,IAAOgH,EAAKhH,GAAK8G,EAAI9G,EAAM,OAAOgH,GAAe,MAAO1B,OAAM2B,KAAKH,GOppB3L,QAASI,GAAMpE,GAsCd,QAASqE,GAAY1F,GAGpB,IAAK,GAFD2F,GAAO,EAEFpH,EAAI,EAAGA,EAAIyB,EAAU3B,OAAQE,IACrCoH,GAASA,GAAQ,GAAKA,EAAQ3F,EAAU4F,WAAWrH,GACnDoH,GAAQ,CAGT,OAAOE,GAAYhE,OAAOiE,KAAKC,IAAIJ,GAAQE,EAAYhE,OAAOxD,QAW/D,QAASwH,GAAY7F,GAGpB,QAASvD,KAAe,OAAAuJ,GAAArF,UAAAtC,OAAN0B,EAAM8D,MAAAmC,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAANlG,EAAMkG,GAAAtF,UAAAsF,EAEvB,IAAKxJ,EAAMyJ,QAAX,CAIA,GAAMC,GAAO1J,EAGP2J,EAAOC,OAAO,GAAIC,OAClBC,EAAKH,GAAQI,GAAYJ,EAC/BD,GAAKjG,KAAOqG,EACZJ,EAAKM,KAAOD,EACZL,EAAKC,KAAOA,EACZI,EAAWJ,EAEXrG,EAAK,GAAK8F,EAAYa,OAAO3G,EAAK,IAEX,gBAAZA,GAAK,IAEfA,EAAK4G,QAAQ,KAId,IAAItG,GAAQ,CACZN,GAAK,GAAKA,EAAK,GAAG3B,QAAQ,gBAAiB,SAACc,EAAO0H,GAElD,GAAc,OAAV1H,EACH,MAAOA,EAERmB,IACA,IAAMwG,GAAYhB,EAAY/D,WAAW8E,EACzC,IAAyB,kBAAdC,GAA0B,CACpC,GAAMC,GAAM/G,EAAKM,EACjBnB,GAAQ2H,EAAUvL,KAAK6K,EAAMW,GAG7B/G,EAAKK,OAAOC,EAAO,GACnBA,IAED,MAAOnB,KAIR2G,EAAY/F,WAAWxE,KAAK6K,EAAMpG,EAElC,IAAMgH,GAAQZ,EAAK5F,KAAOsF,EAAYtF,GACtCwG,GAAMrG,MAAMyF,EAAMpG,IAjDnB,GAAIyG,SAoEJ,OAhBA/J,GAAMuD,UAAYA,EAClBvD,EAAMyJ,QAAUL,EAAYK,QAAQlG,GACpCvD,EAAMkC,UAAYkH,EAAYlH,YAC9BlC,EAAM0D,MAAQuF,EAAY1F,GAC1BvD,EAAMuK,QAAUA,EAChBvK,EAAMwK,OAASA,EAKiB,kBAArBpB,GAAYqB,MACtBrB,EAAYqB,KAAKzK,GAGlBoJ,EAAYsB,UAAUrD,KAAKrH,GAEpBA,EAGR,QAASuK,KACR,GAAM3G,GAAQwF,EAAYsB,UAAUxJ,QAAQ5C,KAC5C,OAAIsF,MAAU,IACbwF,EAAYsB,UAAU/G,OAAOC,EAAO,IAC7B,GAKT,QAAS4G,GAAOjH,EAAWoH,GAC1B,GAAMC,GAAWxB,EAAY9K,KAAKiF,WAAkC,mBAAdoH,GAA4B,IAAMA,GAAapH,EAErG,OADAqH,GAAS9G,IAAMxF,KAAKwF,IACb8G,EAUR,QAASC,GAAOzG,GACfgF,EAAYjF,KAAKC,GAEjBgF,EAAY0B,SACZ1B,EAAY2B,QAEZ,IAAIjJ,UACEkJ,GAA+B,gBAAf5G,GAA0BA,EAAa,IAAI4G,MAAM,UACjElE,EAAMkE,EAAMpJ,MAElB,KAAKE,EAAI,EAAGA,EAAIgF,EAAKhF,IACfkJ,EAAMlJ,KAKXsC,EAAa4G,EAAMlJ,GAAGH,QAAQ,MAAO,OAEf,MAAlByC,EAAW,GACdgF,EAAY2B,MAAM1D,KAAK,GAAIlE,QAAO,IAAMiB,EAAW6G,OAAO,GAAK,MAE/D7B,EAAY0B,MAAMzD,KAAK,GAAIlE,QAAO,IAAMiB,EAAa,MAIvD,KAAKtC,EAAI,EAAGA,EAAIsH,EAAYsB,UAAU9I,OAAQE,IAAK,CAClD,GAAMoJ,GAAW9B,EAAYsB,UAAU5I,EACvCoJ,GAASzB,QAAUL,EAAYK,QAAQyB,EAAS3H,YAUlD,QAAS4H,KACR,GAAM/G,MAAasC,OAAAiC,EACfS,EAAY0B,MAAMM,IAAIC,IADP1C,EAEfS,EAAY2B,MAAMK,IAAIC,GAAaD,IAAI,SAAA7H,GAAA,MAAa,IAAMA,MAC5D+H,KAAK,IAEP,OADAlC,GAAYyB,OAAO,IACZzG,EAUR,QAASqF,GAAQpB,GAChB,GAA8B,MAA1BA,EAAKA,EAAKzG,OAAS,GACtB,OAAO,CAGR,IAAIE,UACAgF,QAEJ,KAAKhF,EAAI,EAAGgF,EAAMsC,EAAY2B,MAAMnJ,OAAQE,EAAIgF,EAAKhF,IACpD,GAAIsH,EAAY2B,MAAMjJ,GAAGhB,KAAKuH,GAC7B,OAAO,CAIT,KAAKvG,EAAI,EAAGgF,EAAMsC,EAAY0B,MAAMlJ,OAAQE,EAAIgF,EAAKhF,IACpD,GAAIsH,EAAY0B,MAAMhJ,GAAGhB,KAAKuH,GAC7B,OAAO,CAIT,QAAO,EAUR,QAASgD,GAAYE,GACpB,MAAOA,GAAOC,WACZ9J,UAAU,EAAG6J,EAAOC,WAAW5J,OAAS,GACxCD,QAAQ,UAAW,KAUtB,QAASsI,GAAOI,GACf,MAAIA,aAAezE,OACXyE,EAAIoB,OAASpB,EAAI3E,QAElB2E,EAKR,MA/PAjB,GAAYpJ,MAAQoJ,EACpBA,aAAsBA,EACtBA,EAAYa,OAASA,EACrBb,EAAY+B,QAAUA,EACtB/B,EAAYyB,OAASA,EACrBzB,EAAYK,QAAUA,EACtBL,EAAY5F,SAAWhF,EAAQ,GAE/BkN,OAAOC,KAAK/G,GAAKgH,QAAQ,SAAAC,GACxBzC,EAAYyC,GAAOjH,EAAIiH,KAMxBzC,EAAYsB,aAMZtB,EAAY0B,SACZ1B,EAAY2B,SAOZ3B,EAAY/D,cAkBZ+D,EAAYH,YAAcA,EA8M1BG,EAAYyB,OAAOzB,EAAY3E,QAExB2E,EAGRjL,EAAOD,QAAU8K,GPgqBX,SAAU7K,EAAQD,GQ13BxB,QAAA4N,GAAAxK,GAEA,GADAA,EAAAyK,OAAAzK,KACAA,EAAAM,OAAA,MAGA,GAAAa,GAAA,mIAAAZ,KACAP,EAEA,IAAAmB,EAAA,CAGA,GAAAuJ,GAAAC,WAAAxJ,EAAA,IACAL,GAAAK,EAAA,UAAAD,aACA,QAAAJ,GACA,YACA,WACA,UACA,SACA,QACA,MAAA4J,GAAAE,CACA,aACA,WACA,QACA,MAAAF,GAAAG,CACA,YACA,UACA,QACA,MAAAH,GAAAI,CACA,aACA,WACA,UACA,SACA,QACA,MAAAJ,GAAAK,CACA,eACA,aACA,WACA,UACA,QACA,MAAAL,GAAAlN,CACA,eACA,aACA,WACA,UACA,QACA,MAAAkN,GAAAM,CACA,oBACA,kBACA,YACA,WACA,SACA,MAAAN,EACA,SACA,UAYA,QAAAO,GAAAzC,GACA,GAAA0C,GAAAnD,KAAAC,IAAAQ,EACA,OAAA0C,IAAAJ,EACA/C,KAAAoD,MAAA3C,EAAAsC,GAAA,IAEAI,GAAAH,EACAhD,KAAAoD,MAAA3C,EAAAuC,GAAA,IAEAG,GAAA1N,EACAuK,KAAAoD,MAAA3C,EAAAhL,GAAA,IAEA0N,GAAAF,EACAjD,KAAAoD,MAAA3C,EAAAwC,GAAA,IAEAxC,EAAA,KAWA,QAAA4C,GAAA5C,GACA,GAAA0C,GAAAnD,KAAAC,IAAAQ,EACA,OAAA0C,IAAAJ,EACAO,EAAA7C,EAAA0C,EAAAJ,EAAA,OAEAI,GAAAH,EACAM,EAAA7C,EAAA0C,EAAAH,EAAA,QAEAG,GAAA1N,EACA6N,EAAA7C,EAAA0C,EAAA1N,EAAA,UAEA0N,GAAAF,EACAK,EAAA7C,EAAA0C,EAAAF,EAAA,UAEAxC,EAAA,MAOA,QAAA6C,GAAA7C,EAAA0C,EAAAR,EAAA3D,GACA,GAAAuE,GAAAJ,GAAA,IAAAR,CACA,OAAA3C,MAAAoD,MAAA3C,EAAAkC,GAAA,IAAA3D,GAAAuE,EAAA,QA5JA,GAAAN,GAAA,IACAxN,EAAA,GAAAwN,EACAD,EAAA,GAAAvN,EACAsN,EAAA,GAAAC,EACAF,EAAA,EAAAC,EACAF,EAAA,OAAAE,CAgBAjO,GAAAD,QAAA,SAAAmM,EAAAwC,GACAA,OACA,IAAAzK,SAAAiI,EACA,eAAAjI,GAAAiI,EAAAzI,OAAA,EACA,MAAAkK,GAAAzB,EACG,eAAAjI,GAAA0K,SAAAzC,GACH,MAAAwC,WAAAH,EAAArC,GAAAkC,EAAAlC,EAEA,UAAAzE,OACA,wDACAJ,KAAAC,UAAA4E,MR8iCM,SAAUlM,EAAQD,EAASM,GSh+BjC,QAAAuO,MAiCA,QAAAC,GAAAtM,GAGA,GAAAY,GAAA,GAAAZ,EAAA0B,IAmBA,IAhBAlE,EAAA+O,eAAAvM,EAAA0B,MAAAlE,EAAAgP,aAAAxM,EAAA0B,OACAd,GAAAZ,EAAAyM,YAAA,KAKAzM,EAAA0M,KAAA,MAAA1M,EAAA0M,MACA9L,GAAAZ,EAAA0M,IAAA,KAIA,MAAA1M,EAAA/B,KACA2C,GAAAZ,EAAA/B,IAIA,MAAA+B,EAAA2M,KAAA,CACA,GAAAC,GAAAC,EAAA7M,EAAA2M,KACA,IAAAC,KAAA,EAGA,MAAAE,EAFAlM,IAAAgM,EAOA,MADAtN,GAAA,mBAAAU,EAAAY,GACAA,EAGA,QAAAiM,GAAAjM,GACA,IACA,MAAAkE,MAAAC,UAAAnE,GACG,MAAAG,GACH,UAcA,QAAAgM,GAAA/M,EAAAgN,GAEA,QAAAC,GAAAC,GACA,GAAAC,GAAAC,EAAAC,kBAAAH,GACAI,EAAAhB,EAAAa,EAAAI,QACAC,EAAAL,EAAAK,OAEAA,GAAAhE,QAAA8D,GACAN,EAAAQ,GAGAJ,EAAAK,YAAAzN,EAAAiN,GAUA,QAAAS,KACA9P,KAAA+P,cAAA,KAsDA,QAAAC,GAAAhN,GACA,GAAAQ,GAAA,EAEA9C,GACAoD,KAAAwH,OAAAtI,EAAAT,OAAA,IAGA,UAAA3C,EAAAqQ,MAAAvP,EAAAoD,MACA,MAAAoC,GAAA,uBAAAxF,EAAAoD,KAIA,IAAAlE,EAAA+O,eAAAjO,EAAAoD,MAAAlE,EAAAgP,aAAAlO,EAAAoD,KAAA,CAEA,IADA,GAAAoM,GAAA,GACA,MAAAlN,EAAAT,SAAAiB,KACA0M,GAAAlN,EAAAT,OAAAiB,GACAA,GAAAR,EAAAM,UAEA,GAAA4M,GAAA5E,OAAA4E,IAAA,MAAAlN,EAAAT,OAAAiB,GACA,SAAA8D,OAAA,sBAEA5G,GAAAmO,YAAAvD,OAAA4E,GAIA,SAAAlN,EAAAT,OAAAiB,EAAA,GAEA,IADA9C,EAAAoO,IAAA,KACAtL,GAAA,CACA,GAAA/C,GAAAuC,EAAAT,OAAAiB,EACA,UAAA/C,EAAA,KAEA,IADAC,EAAAoO,KAAArO,EACA+C,IAAAR,EAAAM,OAAA,UAGA5C,GAAAoO,IAAA,GAIA,IAAAqB,GAAAnN,EAAAT,OAAAiB,EAAA,EACA,SAAA2M,GAAA7E,OAAA6E,MAAA,CAEA,IADAzP,EAAAL,GAAA,KACAmD,GAAA,CACA,GAAA/C,GAAAuC,EAAAT,OAAAiB,EACA,UAAA/C,GAAA6K,OAAA7K,MAAA,GACA+C,CACA,OAGA,GADA9C,EAAAL,IAAA2C,EAAAT,OAAAiB,GACAA,IAAAR,EAAAM,OAAA,MAEA5C,EAAAL,GAAAiL,OAAA5K,EAAAL,IAIA,GAAA2C,EAAAT,SAAAiB,GAAA,CACA,GAAAwL,GAAAoB,EAAApN,EAAA2J,OAAAnJ,IACA6M,EAAArB,KAAA,IAAAtO,EAAAoD,OAAAlE,EAAA0Q,OAAA/F,EAAAyE,GACA,KAAAqB,EAGA,MAAAnK,GAAA,kBAFAxF,GAAAqO,KAAAC,EAOA,MADAtN,GAAA,mBAAAsB,EAAAtC,GACAA,EAGA,QAAA0P,GAAApN,GACA,IACA,MAAAkE,MAAAsG,MAAAxK,GACG,MAAAG,GACH,UA0BA,QAAAoN,GAAAZ,GACA3P,KAAAwQ,UAAAb,EACA3P,KAAA4P,WAkCA,QAAA1J,GAAAuK,GACA,OACA3M,KAAAlE,EAAA0Q,MACAvB,KAAA,iBAAA0B,GAvZA,GAAA/O,GAAAxB,EAAA,uBACAwQ,EAAAxQ,EAAA,IACAsP,EAAAtP,EAAA,IACAqK,EAAArK,EAAA,IACAyQ,EAAAzQ,EAAA,GAQAN,GAAAoC,SAAA,EAQApC,EAAAqQ,OACA,UACA,aACA,QACA,MACA,QACA,eACA,cASArQ,EAAAgR,QAAA,EAQAhR,EAAAiR,WAAA,EAQAjR,EAAAkR,MAAA,EAQAlR,EAAAmR,IAAA,EAQAnR,EAAA0Q,MAAA,EAQA1Q,EAAA+O,aAAA,EAQA/O,EAAAgP,WAAA,EAQAhP,EAAA6O,UAQA7O,EAAAkQ,SAUA,IAAAZ,GAAAtP,EAAA0Q,MAAA,gBAYA7B,GAAA5H,UAAAmK,OAAA,SAAA5O,EAAAgN,GAGA,GAFA1N,EAAA,qBAAAU,GAEAxC,EAAA+O,eAAAvM,EAAA0B,MAAAlE,EAAAgP,aAAAxM,EAAA0B,KACAqL,EAAA/M,EAAAgN,OACG,CACH,GAAA6B,GAAAvC,EAAAtM,EACAgN,IAAA6B,MA8FAP,EAAAZ,EAAAjJ,WAUAiJ,EAAAjJ,UAAAqK,IAAA,SAAA9O,GACA,GAAAuN,EACA,oBAAAvN,GACAuN,EAAAK,EAAA5N,GACAxC,EAAA+O,eAAAgB,EAAA7L,MAAAlE,EAAAgP,aAAAe,EAAA7L,MACA9D,KAAA+P,cAAA,GAAAQ,GAAAZ,GAGA,IAAA3P,KAAA+P,cAAAS,UAAA3B,aACA7O,KAAA2J,KAAA,UAAAgG,IAGA3P,KAAA2J,KAAA,UAAAgG,OAEG,KAAAgB,EAAAvO,OAAA+O,OAWH,SAAA7J,OAAA,iBAAAlF,EAVA,KAAApC,KAAA+P,cACA,SAAAzI,OAAA,mDAEAqI,GAAA3P,KAAA+P,cAAAqB,eAAAhP,GACAuN,IACA3P,KAAA+P,cAAA,KACA/P,KAAA2J,KAAA,UAAAgG,MAkGAG,EAAAjJ,UAAAoF,QAAA,WACAjM,KAAA+P,eACA/P,KAAA+P,cAAAsB,0BA6BAd,EAAA1J,UAAAuK,eAAA,SAAAE,GAEA,GADAtR,KAAA4P,QAAA7G,KAAAuI,GACAtR,KAAA4P,QAAAtM,SAAAtD,KAAAwQ,UAAA3B,YAAA,CACA,GAAAc,GAAAH,EAAA+B,kBAAAvR,KAAAwQ,UAAAxQ,KAAA4P,QAEA,OADA5P,MAAAqR,yBACA1B,EAEA,aASAY,EAAA1J,UAAAwK,uBAAA,WACArR,KAAAwQ,UAAA,KACAxQ,KAAA4P,aTgmCM,SAAU/P,EAAQD,EAASM,IAEJ,SAASyD,GAAU,YU78ChD,SAASC,KAIP,QAAsB,mBAAXC,UAA0BA,OAAOF,SAAmC,aAAxBE,OAAOF,QAAQG,QAK7C,mBAAdE,aAA6BA,UAAUC,YAAaD,UAAUC,UAAUC,cAAcC,MAAM,4BAM3E,mBAAbC,WAA4BA,SAASC,iBAAmBD,SAASC,gBAAgBC,OAASF,SAASC,gBAAgBC,MAAMC,kBAEnH,mBAAXV,SAA0BA,OAAOW,UAAYX,OAAOW,QAAQC,SAAYZ,OAAOW,QAAQE,WAAab,OAAOW,QAAQG,QAGrG,mBAAdX,YAA6BA,UAAUC,WAAaD,UAAUC,UAAUC,cAAcC,MAAM,mBAAqBS,SAASC,OAAOC,GAAI,KAAO,IAE9H,mBAAdd,YAA6BA,UAAUC,WAAaD,UAAUC,UAAUC,cAAcC,MAAM,uBAsBxG,QAASY,GAAWC,GAClB,GAAIpB,GAAY5D,KAAK4D,SASrB,IAPAoB,EAAK,IAAMpB,EAAY,KAAO,IAC1B5D,KAAKiF,WACJrB,EAAY,MAAQ,KACrBoB,EAAK,IACJpB,EAAY,MAAQ,KACrB,IAAMhE,EAAQsF,SAASlF,KAAKmF,MAE3BvB,EAAL,CAEA,GAAInD,GAAI,UAAYT,KAAKoF,KACzBJ,GAAKK,OAAO,EAAG,EAAG5E,EAAG,iBAKrB,IAAI6E,GAAQ,EACRC,EAAQ,CACZP,GAAK,GAAG3B,QAAQ,cAAe,SAASc,GAClC,OAASA,IACbmB,IACI,OAASnB,IAGXoB,EAAQD,MAIZN,EAAKK,OAAOE,EAAO,EAAG9E,IAUxB,QAAS+E,KAGP,MAAO,+BAAoBhB,SAApB,YAAAkB,EAAoBlB,WACtBA,QAAQgB,KACRgM,SAAS3K,UAAUlB,MAAMpF,KAAKiE,QAAQgB,IAAKhB,QAASoB,WAU3D,QAASC,GAAKC,GACZ,IACM,MAAQA,EACVlG,EAAQmG,QAAQE,WAAW,SAE3BrG,EAAQmG,QAAQrE,MAAQoE,EAE1B,MAAM3C,KAUV,QAASgD,KACP,GAAIC,EACJ,KACEA,EAAIxG,EAAQmG,QAAQrE,MACpB,MAAMyB,IAOR,OAJKiD,GAAwB,mBAAZzC,IAA2B,OAASA,KACnDyC,EAAIzC,EAAQ2C,IAAIC,OAGXH,EAoBT,QAASI,KACP,IACE,MAAO3C,QAAO4C,aACd,MAAOtD,KVyzCV,GAAIuC,GAA4B,kBAAXgB,SAAoD,gBAApBA,QAAOC,SAAwB,SAAUvE,GAAO,aAAcA,IAAS,SAAUA,GAAO,MAAOA,IAAyB,kBAAXsE,SAAyBtE,EAAIwE,cAAgBF,QAAUtE,IAAQsE,OAAOG,UAAY,eAAkBzE,GUp/CvQxC,GAAUC,EAAOD,QAAUM,EAAQ,GACnCN,EAAQ4F,IAAMA,EACd5F,EAAQmF,WAAaA,EACrBnF,EAAQiG,KAAOA,EACfjG,EAAQuG,KAAOA,EACfvG,EAAQgE,UAAYA,EACpBhE,EAAQmG,QAAU,mBAAsB0L,SACtB,mBAAsBA,QAAO1L,QAC3B0L,OAAO1L,QAAQ2L,MACflL,IAMpB5G,EAAQkH,QACN,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAClE,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAClE,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAClE,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAClE,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAClE,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAClE,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAClE,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAClE,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAClE,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAClE,UAAW,UAAW,UAAW,UAAW,UAAW,WAwCzDlH,EAAQmH,WAAWC,EAAI,SAASC,GAC9B,IACE,MAAOC,MAAKC,UAAUF,GACtB,MAAO0K,GACP,MAAO,+BAAiCA,EAAIvK,UAqGhDxH,EAAQ2M,OAAOpG,OVu/Ce5F,KAAKX,EAASM,EAAoB,KAI1D,SAAUL,EAAQD,EAASM,GAEhC,YWpoDD,SAASyK,GAAY1F,GACnB,GAAczB,GAAVoH,EAAO,CAEX,KAAKpH,IAAKyB,GACR2F,GAAUA,GAAQ,GAAKA,EAAQ3F,EAAU4F,WAAWrH,GACpDoH,GAAQ,CAGV,OAAOhL,GAAQkH,OAAOiE,KAAKC,IAAIJ,GAAQhL,EAAQkH,OAAOxD,QAWxD,QAASwH,GAAY7F,GAInB,QAASvD,KAEP,GAAKA,EAAMyJ,QAAX,CAEA,GAAIC,GAAO1J,EAGP2J,GAAQ,GAAIE,MACZC,EAAKH,GAAQI,GAAYJ,EAC7BD,GAAKjG,KAAOqG,EACZJ,EAAKM,KAAOD,EACZL,EAAKC,KAAOA,EACZI,EAAWJ,CAIX,KAAK,GADDrG,GAAO,GAAI8D,OAAMlD,UAAUtC,QACtBE,EAAI,EAAGA,EAAIwB,EAAK1B,OAAQE,IAC/BwB,EAAKxB,GAAKoC,UAAUpC,EAGtBwB,GAAK,GAAKpF,EAAQ+L,OAAO3G,EAAK,IAE1B,gBAAoBA,GAAK,IAE3BA,EAAK4G,QAAQ,KAIf,IAAItG,GAAQ,CACZN,GAAK,GAAKA,EAAK,GAAG3B,QAAQ,gBAAiB,SAASc,EAAO0H,GAEzD,GAAc,OAAV1H,EAAgB,MAAOA,EAC3BmB,IACA,IAAIwG,GAAYlM,EAAQmH,WAAW8E,EACnC,IAAI,kBAAsBC,GAAW,CACnC,GAAIC,GAAM/G,EAAKM,EACfnB,GAAQ2H,EAAUvL,KAAK6K,EAAMW,GAG7B/G,EAAKK,OAAOC,EAAO,GACnBA,IAEF,MAAOnB,KAITvE,EAAQmF,WAAWxE,KAAK6K,EAAMpG,EAE9B,IAAIgH,GAAQtK,EAAM8D,KAAO5F,EAAQ4F,KAAOhB,QAAQgB,IAAIoM,KAAKpN,QACzDwH,GAAMrG,MAAMyF,EAAMpG,IAnDpB,GAAIyG,EAmEJ,OAbA/J,GAAMuD,UAAYA,EAClBvD,EAAMyJ,QAAUvL,EAAQuL,QAAQlG,GAChCvD,EAAMkC,UAAYhE,EAAQgE,YAC1BlC,EAAM0D,MAAQuF,EAAY1F,GAC1BvD,EAAMuK,QAAUA,EAGZ,kBAAsBrM,GAAQuM,MAChCvM,EAAQuM,KAAKzK,GAGf9B,EAAQwM,UAAUrD,KAAKrH,GAEhBA,EAGT,QAASuK,KACP,GAAI3G,GAAQ1F,EAAQwM,UAAUxJ,QAAQ5C,KACtC,OAAIsF,MAAU,IACZ1F,EAAQwM,UAAU/G,OAAOC,EAAO,IACzB,GAcX,QAASiH,GAAOzG,GACdlG,EAAQiG,KAAKC,GAEblG,EAAQ4M,SACR5M,EAAQ6M,QAER,IAAIjJ,GACAkJ,GAA+B,gBAAf5G,GAA0BA,EAAa,IAAI4G,MAAM,UACjElE,EAAMkE,EAAMpJ,MAEhB,KAAKE,EAAI,EAAGA,EAAIgF,EAAKhF,IACdkJ,EAAMlJ,KACXsC,EAAa4G,EAAMlJ,GAAGH,QAAQ,MAAO,OACf,MAAlByC,EAAW,GACblG,EAAQ6M,MAAM1D,KAAK,GAAIlE,QAAO,IAAMiB,EAAW6G,OAAO,GAAK,MAE3D/M,EAAQ4M,MAAMzD,KAAK,GAAIlE,QAAO,IAAMiB,EAAa,MAIrD,KAAKtC,EAAI,EAAGA,EAAI5D,EAAQwM,UAAU9I,OAAQE,IAAK,CAC7C,GAAIoJ,GAAWhN,EAAQwM,UAAU5I,EACjCoJ,GAASzB,QAAUvL,EAAQuL,QAAQyB,EAAS3H,YAUhD,QAAS4H,KACPjN,EAAQ2M,OAAO,IAWjB,QAASpB,GAAQpB,GACf,GAA8B,MAA1BA,EAAKA,EAAKzG,OAAS,GACrB,OAAO,CAET,IAAIE,GAAGgF,CACP,KAAKhF,EAAI,EAAGgF,EAAM5I,EAAQ6M,MAAMnJ,OAAQE,EAAIgF,EAAKhF,IAC/C,GAAI5D,EAAQ6M,MAAMjJ,GAAGhB,KAAKuH,GACxB,OAAO,CAGX,KAAKvG,EAAI,EAAGgF,EAAM5I,EAAQ4M,MAAMlJ,OAAQE,EAAIgF,EAAKhF,IAC/C,GAAI5D,EAAQ4M,MAAMhJ,GAAGhB,KAAKuH,GACxB,OAAO,CAGX,QAAO,EAWT,QAAS4B,GAAOI,GACd,MAAIA,aAAezE,OAAcyE,EAAIoB,OAASpB,EAAI3E,QAC3C2E,EAvNTnM,EAAUC,EAAOD,QAAUkL,EAAYpJ,MAAQoJ,EAAY,WAAaA,EACxElL,EAAQ+L,OAASA,EACjB/L,EAAQiN,QAAUA,EAClBjN,EAAQ2M,OAASA,EACjB3M,EAAQuL,QAAUA,EAClBvL,EAAQsF,SAAWhF,EAAQ,IAK3BN,EAAQwM,aAMRxM,EAAQ4M,SACR5M,EAAQ6M,SAQR7M,EAAQmH,eXm3DF,SAAUlH,EAAQD,GYt2DxB,QAAA4N,GAAAxK,GAEA,GADAA,EAAAyK,OAAAzK,KACAA,EAAAM,OAAA,MAGA,GAAAa,GAAA,wHAAAZ,KACAP,EAEA,IAAAmB,EAAA,CAGA,GAAAuJ,GAAAC,WAAAxJ,EAAA,IACAL,GAAAK,EAAA,UAAAD,aACA,QAAAJ,GACA,YACA,WACA,UACA,SACA,QACA,MAAA4J,GAAAE,CACA,YACA,UACA,QACA,MAAAF,GAAAI,CACA,aACA,WACA,UACA,SACA,QACA,MAAAJ,GAAAK,CACA,eACA,aACA,WACA,UACA,QACA,MAAAL,GAAAlN,CACA,eACA,aACA,WACA,UACA,QACA,MAAAkN,GAAAM,CACA,oBACA,kBACA,YACA,WACA,SACA,MAAAN,EACA,SACA,UAYA,QAAAO,GAAAzC,GACA,MAAAA,IAAAsC,EACA/C,KAAAoD,MAAA3C,EAAAsC,GAAA,IAEAtC,GAAAuC,EACAhD,KAAAoD,MAAA3C,EAAAuC,GAAA,IAEAvC,GAAAhL,EACAuK,KAAAoD,MAAA3C,EAAAhL,GAAA,IAEAgL,GAAAwC,EACAjD,KAAAoD,MAAA3C,EAAAwC,GAAA,IAEAxC,EAAA,KAWA,QAAA4C,GAAA5C,GACA,MAAA6C,GAAA7C,EAAAsC,EAAA,QACAO,EAAA7C,EAAAuC,EAAA,SACAM,EAAA7C,EAAAhL,EAAA,WACA6N,EAAA7C,EAAAwC,EAAA,WACAxC,EAAA,MAOA,QAAA6C,GAAA7C,EAAAkC,EAAA3D,GACA,KAAAyB,EAAAkC,GAGA,MAAAlC,GAAA,IAAAkC,EACA3C,KAAA8G,MAAArG,EAAAkC,GAAA,IAAA3D,EAEAgB,KAAA+G,KAAAtG,EAAAkC,GAAA,IAAA3D,EAAA,IAlJA,GAAAiE,GAAA,IACAxN,EAAA,GAAAwN,EACAD,EAAA,GAAAvN,EACAsN,EAAA,GAAAC,EACAH,EAAA,OAAAE,CAgBAjO,GAAAD,QAAA,SAAAmM,EAAAwC,GACAA,OACA,IAAAzK,SAAAiI,EACA,eAAAjI,GAAAiI,EAAAzI,OAAA,EACA,MAAAkK,GAAAzB,EACG,eAAAjI,GAAAiO,MAAAhG,MAAA,EACH,MAAAwC,WAAAH,EAAArC,GAAAkC,EAAAlC,EAEA,UAAAzE,OACA,wDACAJ,KAAAC,UAAA4E,MZghEM,SAAUlM,EAAQD,EAASM,GaniEjC,QAAAwQ,GAAAtO,GACA,GAAAA,EAAA,MAAA4P,GAAA5P,GAWA,QAAA4P,GAAA5P,GACA,OAAAmL,KAAAmD,GAAA7J,UACAzE,EAAAmL,GAAAmD,EAAA7J,UAAA0G,EAEA,OAAAnL,GAzBAvC,EAAAD,QAAA8Q,EAqCAA,EAAA7J,UAAAwC,GACAqH,EAAA7J,UAAAoL,iBAAA,SAAAC,EAAAC,GAIA,MAHAnS,MAAAoS,WAAApS,KAAAoS,gBACApS,KAAAoS,WAAA,IAAAF,GAAAlS,KAAAoS,WAAA,IAAAF,QACAnJ,KAAAoJ,GACAnS,MAaA0Q,EAAA7J,UAAA0C,KAAA,SAAA2I,EAAAC,GACA,QAAA9I,KACArJ,KAAAwJ,IAAA0I,EAAA7I,GACA8I,EAAAxM,MAAA3F,KAAA4F,WAKA,MAFAyD,GAAA8I,KACAnS,KAAAqJ,GAAA6I,EAAA7I,GACArJ,MAaA0Q,EAAA7J,UAAA2C,IACAkH,EAAA7J,UAAA4C,eACAiH,EAAA7J,UAAA6C,mBACAgH,EAAA7J,UAAAwL,oBAAA,SAAAH,EAAAC,GAIA,GAHAnS,KAAAoS,WAAApS,KAAAoS,eAGA,GAAAxM,UAAAtC,OAEA,MADAtD,MAAAoS,cACApS,IAIA,IAAAsS,GAAAtS,KAAAoS,WAAA,IAAAF,EACA,KAAAI,EAAA,MAAAtS,KAGA,OAAA4F,UAAAtC,OAEA,aADAtD,MAAAoS,WAAA,IAAAF,GACAlS,IAKA,QADAuS,GACA/O,EAAA,EAAiBA,EAAA8O,EAAAhP,OAAsBE,IAEvC,GADA+O,EAAAD,EAAA9O,GACA+O,IAAAJ,GAAAI,EAAAJ,OAAA,CACAG,EAAAjN,OAAA7B,EAAA,EACA,OAGA,MAAAxD,OAWA0Q,EAAA7J,UAAA8C,KAAA,SAAAuI,GACAlS,KAAAoS,WAAApS,KAAAoS,cACA,IAAApN,MAAAwN,MAAAjS,KAAAqF,UAAA,GACA0M,EAAAtS,KAAAoS,WAAA,IAAAF,EAEA,IAAAI,EAAA,CACAA,IAAAE,MAAA,EACA,QAAAhP,GAAA,EAAAgF,EAAA8J,EAAAhP,OAA2CE,EAAAgF,IAAShF,EACpD8O,EAAA9O,GAAAmC,MAAA3F,KAAAgF,GAIA,MAAAhF,OAWA0Q,EAAA7J,UAAAiD,UAAA,SAAAoI,GAEA,MADAlS,MAAAoS,WAAApS,KAAAoS,eACApS,KAAAoS,WAAA,IAAAF,QAWAxB,EAAA7J,UAAA4L,aAAA,SAAAP,GACA,QAAAlS,KAAA8J,UAAAoI,GAAA5O,Sb0jEM,SAAUzD,EAAQD,EAASM,Gc5rEjC,QAAAwS,GAAA3D,EAAAa,GACA,IAAAb,EAAA,MAAAA,EAEA,IAAA4B,EAAA5B,GAAA,CACA,GAAA4D,IAAuBC,cAAA,EAAAC,IAAAjD,EAAAtM,OAEvB,OADAsM,GAAA7G,KAAAgG,GACA4D,EACG,GAAApI,EAAAwE,GAAA,CAEH,OADA+D,GAAA,GAAAhK,OAAAiG,EAAAzL,QACAE,EAAA,EAAmBA,EAAAuL,EAAAzL,OAAiBE,IACpCsP,EAAAtP,GAAAkP,EAAA3D,EAAAvL,GAAAoM,EAEA,OAAAkD,GACG,mBAAA/D,kBAAAxD,OAAA,CACH,GAAAuH,KACA,QAAAvF,KAAAwB,GACA+D,EAAAvF,GAAAmF,EAAA3D,EAAAxB,GAAAqC,EAEA,OAAAkD,GAEA,MAAA/D,GAkBA,QAAAgE,GAAAhE,EAAAa,GACA,IAAAb,EAAA,MAAAA,EAEA,IAAAA,KAAA6D,aACA,MAAAhD,GAAAb,EAAA8D,IACG,IAAAtI,EAAAwE,GACH,OAAAvL,GAAA,EAAmBA,EAAAuL,EAAAzL,OAAiBE,IACpCuL,EAAAvL,GAAAuP,EAAAhE,EAAAvL,GAAAoM,OAEG,oBAAAb,GACH,OAAAxB,KAAAwB,GACAA,EAAAxB,GAAAwF,EAAAhE,EAAAxB,GAAAqC,EAIA,OAAAb,GA9EA,GAAAxE,GAAArK,EAAA,IACAyQ,EAAAzQ,EAAA,IACAgN,EAAAE,OAAAvG,UAAAqG,SACA8F,EAAA,kBAAAC,OAAA,mBAAAA,OAAA,6BAAA/F,EAAA3M,KAAA0S,MACAC,EAAA,kBAAAC,OAAA,mBAAAA,OAAA,6BAAAjG,EAAA3M,KAAA4S,KAYAvT,GAAA6P,kBAAA,SAAAE,GACA,GAAAC,MACAwD,EAAAzD,EAAAZ,KACAW,EAAAC,CAGA,OAFAD,GAAAX,KAAA2D,EAAAU,EAAAxD,GACAF,EAAAb,YAAAe,EAAAtM,QACUqM,OAAAD,EAAAE,YAmCVhQ,EAAA2R,kBAAA,SAAA5B,EAAAC,GAGA,MAFAD,GAAAZ,KAAAgE,EAAApD,EAAAZ,KAAAa,GACAD,EAAAd,YAAA/N,OACA6O,GA+BA/P,EAAAiQ,YAAA,SAAAd,EAAAK,GACA,QAAAiE,GAAAjR,EAAAkR,EAAAC,GACA,IAAAnR,EAAA,MAAAA,EAGA,IAAA4Q,GAAA5Q,YAAA6Q,OACAC,GAAA9Q,YAAA+Q,MAAA,CACAK,GAGA,IAAAC,GAAA,GAAAC,WACAD,GAAAE,OAAA,WACAJ,EACAA,EAAAD,GAAAtT,KAAA4T,OAGAtE,EAAAtP,KAAA4T,SAIAJ,GACApE,EAAAE,IAIAmE,EAAAI,kBAAAzR,OACK,IAAAmI,EAAAnI,GACL,OAAAoB,GAAA,EAAqBA,EAAApB,EAAAkB,OAAgBE,IACrC6P,EAAAjR,EAAAoB,KAAApB,OAEK,oBAAAA,KAAAuO,EAAAvO,GACL,OAAAmL,KAAAnL,GACAiR,EAAAjR,EAAAmL,KAAAnL,GAKA,GAAAoR,GAAA,EACAlE,EAAAP,CACAsE,GAAA/D,GACAkE,GACApE,EAAAE,KdouEM,SAAUzP,EAAQD,Ge92ExB,GAAAsN,MAAiBA,QAEjBrN,GAAAD,QAAAkJ,MAAAyB,SAAA,SAAAD,GACA,wBAAA4C,EAAA3M,KAAA+J,Kfs3EM,SAAUzK,EAAQD,GgBz2ExB,QAAA+Q,GAAAvO,GACA,MAAA0R,IAAAC,OAAAC,SAAA5R,IACA6R,IAAA7R,YAAA8R,cAAAC,EAAA/R,IAjBAvC,EAAAD,QAAA+Q,CAEA,IAAAmD,GAAA,kBAAAC,SAAA,kBAAAA,QAAAC,SACAC,EAAA,kBAAAC,aAEAC,EAAA,SAAA/R,GACA,wBAAA8R,aAAAC,OAAAD,YAAAC,OAAA/R,KAAAgS,iBAAAF,ehB44EM,SAAUrU,EAAQD,EAASM,GiBh3EjC,QAAAyB,GAAAf,EAAAC,GACA,KAAAb,eAAA2B,IAAA,UAAAA,GAAAf,EAAAC,EACAD,IAAA,gBAAAA,KACAC,EAAAD,EACAA,EAAAE,QAEAD,QAEAA,EAAAM,KAAAN,EAAAM,MAAA,aACAnB,KAAAsB,QACAtB,KAAAqU,QACArU,KAAAa,OACAb,KAAAsU,aAAAzT,EAAAyT,gBAAA,GACAtU,KAAAuU,qBAAA1T,EAAA0T,sBAAAC,KACAxU,KAAAyU,kBAAA5T,EAAA4T,mBAAA,KACAzU,KAAA0U,qBAAA7T,EAAA6T,sBAAA,KACA1U,KAAA2U,oBAAA9T,EAAA8T,qBAAA,IACA3U,KAAA4U,QAAA,GAAAC,IACAC,IAAA9U,KAAAyU,oBACAM,IAAA/U,KAAA0U,uBACAM,OAAAhV,KAAA2U,wBAEA3U,KAAAuI,QAAA,MAAA1H,EAAA0H,QAAA,IAAA1H,EAAA0H,SACAvI,KAAAiV,WAAA,SACAjV,KAAAY,MACAZ,KAAAkV,cACAlV,KAAAmV,SAAA,KACAnV,KAAAiR,UAAA,EACAjR,KAAAoV,eACA,IAAAC,GAAAxU,EAAAiB,SACA9B,MAAAsV,QAAA,GAAAD,GAAA5G,QACAzO,KAAAuV,QAAA,GAAAF,GAAAvF,QACA9P,KAAAwV,YAAA3U,EAAA2U,eAAA,EACAxV,KAAAwV,aAAAxV,KAAAyV,OA/DA,GAAAC,GAAAxV,EAAA,IACAgC,EAAAhC,EAAA,IACAwQ,EAAAxQ,EAAA,IACA4B,EAAA5B,EAAA,GACAmJ,EAAAnJ,EAAA,IACA0R,EAAA1R,EAAA,IACAwB,EAAAxB,EAAA,+BACA0C,EAAA1C,EAAA,IACA2U,EAAA3U,EAAA,IAMAyV,EAAAvI,OAAAvG,UAAA+O,cAMA/V,GAAAD,QAAA+B,EAoDAA,EAAAkF,UAAAgP,QAAA,WACA7V,KAAA2J,KAAAhE,MAAA3F,KAAA4F,UACA,QAAAkJ,KAAA9O,MAAAsB,KACAqU,EAAApV,KAAAP,KAAAsB,KAAAwN,IACA9O,KAAAsB,KAAAwN,GAAAnF,KAAAhE,MAAA3F,KAAAsB,KAAAwN,GAAAlJ,YAWAjE,EAAAkF,UAAAiP,gBAAA,WACA,OAAAhH,KAAA9O,MAAAsB,KACAqU,EAAApV,KAAAP,KAAAsB,KAAAwN,KACA9O,KAAAsB,KAAAwN,GAAAzO,GAAAL,KAAA+V,WAAAjH,KAaAnN,EAAAkF,UAAAkP,WAAA,SAAAjH,GACA,aAAAA,EAAA,GAAAA,EAAA,KAAA9O,KAAAgW,OAAA3V,IAOAqQ,EAAA/O,EAAAkF,WAUAlF,EAAAkF,UAAAyN,aAAA,SAAArN,GACA,MAAArB,WAAAtC,QACAtD,KAAAiW,gBAAAhP,EACAjH,MAFAA,KAAAiW,eAaAtU,EAAAkF,UAAA0N,qBAAA,SAAAtN,GACA,MAAArB,WAAAtC,QACAtD,KAAAkW,sBAAAjP,EACAjH,MAFAA,KAAAkW,uBAaAvU,EAAAkF,UAAA4N,kBAAA,SAAAxN,GACA,MAAArB,WAAAtC,QACAtD,KAAAmW,mBAAAlP,EACAjH,KAAA4U,SAAA5U,KAAA4U,QAAAwB,OAAAnP,GACAjH,MAHAA,KAAAmW,oBAMAxU,EAAAkF,UAAA8N,oBAAA,SAAA1N,GACA,MAAArB,WAAAtC,QACAtD,KAAAqW,qBAAApP,EACAjH,KAAA4U,SAAA5U,KAAA4U,QAAA0B,UAAArP,GACAjH,MAHAA,KAAAqW,sBAcA1U,EAAAkF,UAAA6N,qBAAA,SAAAzN,GACA,MAAArB,WAAAtC,QACAtD,KAAAuW,sBAAAtP,EACAjH,KAAA4U,SAAA5U,KAAA4U,QAAA4B,OAAAvP,GACAjH,MAHAA,KAAAuW,uBAaA5U,EAAAkF,UAAA0B,QAAA,SAAAtB,GACA,MAAArB,WAAAtC,QACAtD,KAAAyW,SAAAxP,EACAjH,MAFAA,KAAAyW,UAYA9U,EAAAkF,UAAA6P,qBAAA,YAEA1W,KAAA2W,cAAA3W,KAAAiW,eAAA,IAAAjW,KAAA4U,QAAAgC,UAEA5W,KAAA6W,aAYAlV,EAAAkF,UAAA4O,KACA9T,EAAAkF,UAAA5E,QAAA,SAAAkQ,EAAAtR,GAEA,GADAa,EAAA,gBAAA1B,KAAAiV,aACAjV,KAAAiV,WAAArS,QAAA,cAAA5C,KAEA0B,GAAA,aAAA1B,KAAAY,KACAZ,KAAAgW,OAAAN,EAAA1V,KAAAY,IAAAZ,KAAAa,KACA,IAAAgB,GAAA7B,KAAAgW,OACA5K,EAAApL,IACAA,MAAAiV,WAAA,UACAjV,KAAA8W,eAAA,CAGA,IAAAC,GAAA1N,EAAAxH,EAAA,kBACAuJ,EAAA4L,SACA7E,SAIA8E,EAAA5N,EAAAxH,EAAA,iBAAAkN,GAKA,GAJArN,EAAA,iBACA0J,EAAA8L,UACA9L,EAAA6J,WAAA,SACA7J,EAAAyK,QAAA,gBAAA9G,GACAoD,EAAA,CACA,GAAAR,GAAA,GAAArK,OAAA,mBACAqK,GAAA5C,OACAoD,EAAAR,OAGAvG,GAAAsL,wBAKA,SAAA1W,KAAAyW,SAAA,CACA,GAAAlO,GAAAvI,KAAAyW,QACA/U,GAAA,wCAAA6G,EAGA,IAAA4O,GAAAxP,WAAA,WACAjG,EAAA,qCAAA6G,GACAwO,EAAA9K,UACApK,EAAAuV,QACAvV,EAAA8H,KAAA,mBACAyB,EAAAyK,QAAA,kBAAAtN,IACKA,EAELvI,MAAAqU,KAAAtL,MACAkD,QAAA,WACAlE,aAAAoP,MAQA,MAHAnX,MAAAqU,KAAAtL,KAAAgO,GACA/W,KAAAqU,KAAAtL,KAAAkO,GAEAjX,MASA2B,EAAAkF,UAAAmQ,OAAA,WACAtV,EAAA,QAGA1B,KAAAkX,UAGAlX,KAAAiV,WAAA,OACAjV,KAAA2J,KAAA,OAGA,IAAA9H,GAAA7B,KAAAgW,MACAhW,MAAAqU,KAAAtL,KAAAM,EAAAxH,EAAA,OAAA+P,EAAA5R,KAAA,YACAA,KAAAqU,KAAAtL,KAAAM,EAAAxH,EAAA,OAAA+P,EAAA5R,KAAA,YACAA,KAAAqU,KAAAtL,KAAAM,EAAAxH,EAAA,OAAA+P,EAAA5R,KAAA,YACAA,KAAAqU,KAAAtL,KAAAM,EAAAxH,EAAA,QAAA+P,EAAA5R,KAAA,aACAA,KAAAqU,KAAAtL,KAAAM,EAAAxH,EAAA,QAAA+P,EAAA5R,KAAA,aACAA,KAAAqU,KAAAtL,KAAAM,EAAArJ,KAAAuV,QAAA,UAAA3D,EAAA5R,KAAA,gBASA2B,EAAAkF,UAAAwQ,OAAA,WACArX,KAAAmV,SAAA,GAAA5J,MACAvL,KAAA6V,QAAA,SASAlU,EAAAkF,UAAAyQ,OAAA,WACAtX,KAAA6V,QAAA,UAAAtK,MAAAvL,KAAAmV,WASAxT,EAAAkF,UAAA0Q,OAAA,SAAAxI,GACA/O,KAAAuV,QAAArE,IAAAnC,IASApN,EAAAkF,UAAA2Q,UAAA,SAAA7H,GACA3P,KAAA2J,KAAA,SAAAgG,IASAhO,EAAAkF,UAAA4Q,QAAA,SAAA9F,GACAjQ,EAAA,QAAAiQ,GACA3R,KAAA6V,QAAA,QAAAlE,IAUAhQ,EAAAkF,UAAAhF,OAAA,SAAAiN,EAAAjO,GAiBA,QAAA6W,MACA9U,EAAAwI,EAAA8J,WAAArT,IACAuJ,EAAA8J,WAAAnM,KAAAlH,GAlBA,GAAAA,GAAA7B,KAAAsB,KAAAwN,EACA,KAAAjN,EAAA,CACAA,EAAA,GAAAK,GAAAlC,KAAA8O,EAAAjO,GACAb,KAAAsB,KAAAwN,GAAAjN,CACA,IAAAuJ,GAAApL,IACA6B,GAAAwH,GAAA,aAAAqO,GACA7V,EAAAwH,GAAA,qBACAxH,EAAAxB,GAAA+K,EAAA2K,WAAAjH,KAGA9O,KAAAwV,aAEAkC,IAUA,MAAA7V,IASAF,EAAAkF,UAAAoF,QAAA,SAAApK,GACA,GAAAyD,GAAA1C,EAAA5C,KAAAkV,WAAArT,IACAyD,GAAAtF,KAAAkV,WAAA7P,OAAAC,EAAA,GACAtF,KAAAkV,WAAA5R,QAEAtD,KAAAoX,SAUAzV,EAAAkF,UAAA8I,OAAA,SAAAA,GACAjO,EAAA,oBAAAiO,EACA,IAAAvE,GAAApL,IACA2P,GAAA/N,OAAA,IAAA+N,EAAA7L,OAAA6L,EAAAb,KAAA,IAAAa,EAAA/N,OAEAwJ,EAAA6F,SAWA7F,EAAAgK,aAAArM,KAAA4G,IATAvE,EAAA6F,UAAA,EACAjR,KAAAsV,QAAAtE,OAAArB,EAAA,SAAAgI,GACA,OAAAnU,GAAA,EAAqBA,EAAAmU,EAAArU,OAA2BE,IAChD4H,EAAA4K,OAAA4B,MAAAD,EAAAnU,GAAAmM,EAAApB,QAEAnD,GAAA6F,UAAA,EACA7F,EAAAyM,yBAcAlW,EAAAkF,UAAAgR,mBAAA,WACA,GAAA7X,KAAAoV,aAAA9R,OAAA,IAAAtD,KAAAiR,SAAA,CACA,GAAAvB,GAAA1P,KAAAoV,aAAA0C,OACA9X,MAAA2P,OAAAD,KAUA/N,EAAAkF,UAAAqQ,QAAA,WACAxV,EAAA,UAGA,QADAqW,GAAA/X,KAAAqU,KAAA/Q,OACAE,EAAA,EAAiBA,EAAAuU,EAAgBvU,IAAA,CACjC,GAAAwU,GAAAhY,KAAAqU,KAAAyD,OACAE,GAAA/L,UAGAjM,KAAAoV,gBACApV,KAAAiR,UAAA,EACAjR,KAAAmV,SAAA,KAEAnV,KAAAuV,QAAAtJ,WASAtK,EAAAkF,UAAAuQ,MACAzV,EAAAkF,UAAAoR,WAAA,WACAvW,EAAA,cACA1B,KAAA8W,eAAA,EACA9W,KAAA2W,cAAA,EACA,YAAA3W,KAAAiV,YAGAjV,KAAAkX,UAEAlX,KAAA4U,QAAAsD,QACAlY,KAAAiV,WAAA,SACAjV,KAAAgW,QAAAhW,KAAAgW,OAAAoB,SASAzV,EAAAkF,UAAAsR,QAAA,SAAAC,GACA1W,EAAA,WAEA1B,KAAAkX,UACAlX,KAAA4U,QAAAsD,QACAlY,KAAAiV,WAAA,SACAjV,KAAA2J,KAAA,QAAAyO,GAEApY,KAAAiW,gBAAAjW,KAAA8W,eACA9W,KAAA6W,aAUAlV,EAAAkF,UAAAgQ,UAAA,WACA,GAAA7W,KAAA2W,cAAA3W,KAAA8W,cAAA,MAAA9W,KAEA,IAAAoL,GAAApL,IAEA,IAAAA,KAAA4U,QAAAgC,UAAA5W,KAAAkW,sBACAxU,EAAA,oBACA1B,KAAA4U,QAAAsD,QACAlY,KAAA6V,QAAA,oBACA7V,KAAA2W,cAAA,MACG,CACH,GAAA0B,GAAArY,KAAA4U,QAAA0D,UACA5W,GAAA,0CAAA2W,GAEArY,KAAA2W,cAAA,CACA,IAAAQ,GAAAxP,WAAA,WACAyD,EAAA0L,gBAEApV,EAAA,wBACA0J,EAAAyK,QAAA,oBAAAzK,EAAAwJ,QAAAgC,UACAxL,EAAAyK,QAAA,eAAAzK,EAAAwJ,QAAAgC,UAGAxL,EAAA0L,eAEA1L,EAAAqK,KAAA,SAAA9D,GACAA,GACAjQ,EAAA,2BACA0J,EAAAuL,cAAA,EACAvL,EAAAyL,YACAzL,EAAAyK,QAAA,kBAAAlE,EAAA5C,QAEArN,EAAA,qBACA0J,EAAAmN,mBAGKF,EAELrY,MAAAqU,KAAAtL,MACAkD,QAAA,WACAlE,aAAAoP,QAYAxV,EAAAkF,UAAA0R,YAAA,WACA,GAAAC,GAAAxY,KAAA4U,QAAAgC,QACA5W,MAAA2W,cAAA,EACA3W,KAAA4U,QAAAsD,QACAlY,KAAA8V,kBACA9V,KAAA6V,QAAA,YAAA2C,KjB25EM,SAAU3Y,EAAQD,EAASM,GkBr9FjCL,EAAAD,QAAAM,EAAA,IAQAL,EAAAD,QAAAkC,OAAA5B,EAAA,KlB69FM,SAAUL,EAAQD,EAASM,GmB58FjC,QAAAgC,GAAAtB,EAAAC,GACA,MAAAb,gBAAAkC,IAEArB,QAEAD,GAAA,gBAAAA,KACAC,EAAAD,EACAA,EAAA,MAGAA,GACAA,EAAA6B,EAAA7B,GACAC,EAAA4X,SAAA7X,EAAA0B,KACAzB,EAAA6X,OAAA,UAAA9X,EAAAoB,UAAA,QAAApB,EAAAoB,SACAnB,EAAA6B,KAAA9B,EAAA8B,KACA9B,EAAAgB,QAAAf,EAAAe,MAAAhB,EAAAgB,QACGf,EAAAyB,OACHzB,EAAA4X,SAAAhW,EAAA5B,EAAAyB,YAGAtC,KAAA0Y,OAAA,MAAA7X,EAAA6X,OAAA7X,EAAA6X,OACA,mBAAArW,WAAA,WAAAA,SAAAL,SAEAnB,EAAA4X,WAAA5X,EAAA6B,OAEA7B,EAAA6B,KAAA1C,KAAA0Y,OAAA,YAGA1Y,KAAA2Y,MAAA9X,EAAA8X,QAAA,EACA3Y,KAAAyY,SAAA5X,EAAA4X,WACA,mBAAApW,mBAAAoW,SAAA,aACAzY,KAAA0C,KAAA7B,EAAA6B,OAAA,mBAAAL,oBAAAK,KACAL,SAAAK,KACA1C,KAAA0Y,OAAA,QACA1Y,KAAA4B,MAAAf,EAAAe,UACA,gBAAA5B,MAAA4B,QAAA5B,KAAA4B,MAAAgX,EAAAC,OAAA7Y,KAAA4B,QACA5B,KAAA8Y,SAAA,IAAAjY,EAAAiY,QACA9Y,KAAAmB,MAAAN,EAAAM,MAAA,cAAAkC,QAAA,cACArD,KAAA+Y,aAAAlY,EAAAkY,WACA/Y,KAAAgZ,OAAA,IAAAnY,EAAAmY,MACAhZ,KAAAiZ,cAAApY,EAAAoY,YACAjZ,KAAAkZ,aAAArY,EAAAqY,WACAlZ,KAAAmZ,iBAAA,IAAAtY,EAAAsY,gBACAnZ,KAAAoZ,eAAAvY,EAAAuY,gBAAA,IACApZ,KAAAqZ,kBAAAxY,EAAAwY,kBACArZ,KAAAsZ,WAAAzY,EAAAyY,aAAA,uBACAtZ,KAAAuZ,iBAAA1Y,EAAA0Y,qBACAvZ,KAAAiV,WAAA,GACAjV,KAAAwZ,eACAxZ,KAAAyZ,cAAA,EACAzZ,KAAA0Z,WAAA7Y,EAAA6Y,YAAA,IACA1Z,KAAA2Z,gBAAA9Y,EAAA8Y,kBAAA,EACA3Z,KAAA4Z,WAAA,KACA5Z,KAAA6Z,mBAAAhZ,EAAAgZ,mBACA7Z,KAAA8Z,mBAAA,IAAAjZ,EAAAiZ,oBAAAjZ,EAAAiZ,wBAEA,IAAA9Z,KAAA8Z,oBAAA9Z,KAAA8Z,sBACA9Z,KAAA8Z,mBAAA,MAAA9Z,KAAA8Z,kBAAAC,YACA/Z,KAAA8Z,kBAAAC,UAAA,MAIA/Z,KAAAga,IAAAnZ,EAAAmZ,KAAA,KACAha,KAAAuN,IAAA1M,EAAA0M,KAAA,KACAvN,KAAAia,WAAApZ,EAAAoZ,YAAA,KACAja,KAAAka,KAAArZ,EAAAqZ,MAAA,KACAla,KAAAma,GAAAtZ,EAAAsZ,IAAA,KACAna,KAAAoa,QAAAvZ,EAAAuZ,SAAA,KACApa,KAAAqa,mBAAAvZ,SAAAD,EAAAwZ,oBAAAxZ,EAAAwZ,mBACAra,KAAAsa,YAAAzZ,EAAAyZ,UAGAta,KAAAua,cAAA,mBAAAvW,YAAA,gBAAAA,WAAAwW,SAAA,gBAAAxW,UAAAwW,QAAAtW,eAGA,mBAAAkH,OAAApL,KAAAua,iBACA1Z,EAAA4Z,cAAArN,OAAAC,KAAAxM,EAAA4Z,cAAAnX,OAAA,IACAtD,KAAAya,aAAA5Z,EAAA4Z,cAGA5Z,EAAA6Z,eACA1a,KAAA0a,aAAA7Z,EAAA6Z,eAKA1a,KAAAK,GAAA,KACAL,KAAA2a,SAAA,KACA3a,KAAA4a,aAAA,KACA5a,KAAA6a,YAAA,KAGA7a,KAAA8a,kBAAA,KACA9a,KAAA+a,iBAAA,SAEA/a,MAAAyV,QA9FA,GAAAvT,GAAAtB,EAAAC,GAsLA,QAAAma,GAAA5Y,GACA,GAAA6Y,KACA,QAAAzX,KAAApB,GACAA,EAAAwT,eAAApS,KACAyX,EAAAzX,GAAApB,EAAAoB,GAGA,OAAAyX,GApNA,GAAA3B,GAAApZ,EAAA,IACAwQ,EAAAxQ,EAAA,IACAwB,EAAAxB,EAAA,8BACAoF,EAAApF,EAAA,IACA4B,EAAA5B,EAAA,IACAuC,EAAAvC,EAAA,GACA0Y,EAAA1Y,EAAA,GAMAL,GAAAD,QAAAsC,EA4GAA,EAAAgZ,uBAAA,EAMAxK,EAAAxO,EAAA2E,WAQA3E,EAAAF,SAAAF,EAAAE,SAOAE,WACAA,EAAAiZ,UAAAjb,EAAA,IACAgC,EAAAoX,WAAApZ,EAAA,IACAgC,EAAAJ,OAAA5B,EAAA,IAUAgC,EAAA2E,UAAAuU,gBAAA,SAAArR,GACArI,EAAA,0BAAAqI,EACA,IAAAnI,GAAAoZ,EAAAhb,KAAA4B,MAGAA,GAAAyZ,IAAAvZ,EAAAE,SAGAJ,EAAA0Z,UAAAvR,CAGA,IAAAwE,GAAAvO,KAAAuZ,iBAAAxP,MAGA/J,MAAAK,KAAAuB,EAAA2Z,IAAAvb,KAAAK,GAEA,IAAAib,GAAA,GAAAhC,GAAAvP,IACAnI,QACAC,OAAA7B,KACA2Y,MAAApK,EAAAoK,OAAA3Y,KAAA2Y,MACAF,SAAAlK,EAAAkK,UAAAzY,KAAAyY,SACA/V,KAAA6L,EAAA7L,MAAA1C,KAAA0C,KACAgW,OAAAnK,EAAAmK,QAAA1Y,KAAA0Y,OACAvX,KAAAoN,EAAApN,MAAAnB,KAAAmB,KACA4X,WAAAxK,EAAAwK,YAAA/Y,KAAA+Y,WACAC,MAAAzK,EAAAyK,OAAAhZ,KAAAgZ,MACAC,YAAA1K,EAAA0K,aAAAjZ,KAAAiZ,YACAC,WAAA3K,EAAA2K,YAAAlZ,KAAAkZ,WACAC,gBAAA5K,EAAA4K,iBAAAnZ,KAAAmZ,gBACAE,kBAAA9K,EAAA8K,mBAAArZ,KAAAqZ,kBACAD,eAAA7K,EAAA6K,gBAAApZ,KAAAoZ,eACAM,WAAAnL,EAAAmL,YAAA1Z,KAAA0Z,WACAM,IAAAzL,EAAAyL,KAAAha,KAAAga,IACAzM,IAAAgB,EAAAhB,KAAAvN,KAAAuN,IACA0M,WAAA1L,EAAA0L,YAAAja,KAAAia,WACAC,KAAA3L,EAAA2L,MAAAla,KAAAka,KACAC,GAAA5L,EAAA4L,IAAAna,KAAAma,GACAC,QAAA7L,EAAA6L,SAAApa,KAAAoa,QACAC,mBAAA9L,EAAA8L,oBAAAra,KAAAqa,mBACAP,kBAAAvL,EAAAuL,mBAAA9Z,KAAA8Z,kBACAW,aAAAlM,EAAAkM,cAAAza,KAAAya,aACAH,UAAA/L,EAAA+L,WAAAta,KAAAsa,UACAI,aAAAnM,EAAAmM,cAAA1a,KAAA0a,aACAc,eAAAjN,EAAAiN,gBAAAxb,KAAAwb,eACAC,UAAAlN,EAAAkN,WAAA,OACAlB,cAAAva,KAAAua,eAGA,OAAAe,IAkBApZ,EAAA2E,UAAA4O,KAAA,WACA,GAAA6F,EACA,IAAAtb,KAAA2Z,iBAAAzX,EAAAgZ,uBAAAlb,KAAAsZ,WAAA1W,QAAA,kBACA0Y,EAAA,gBACG;AAAA,OAAAtb,KAAAsZ,WAAAhW,OAAA,CAEH,GAAA8H,GAAApL,IAIA,YAHA2H,YAAA,WACAyD,EAAAzB,KAAA,oCACK,GAGL2R,EAAAtb,KAAAsZ,WAAA,GAEAtZ,KAAAiV,WAAA,SAGA,KACAqG,EAAAtb,KAAAob,gBAAAE,GACG,MAAAnY,GAGH,MAFAnD,MAAAsZ,WAAAxB,YACA9X,MAAAyV,OAIA6F,EAAA7F,OACAzV,KAAA0b,aAAAJ,IASApZ,EAAA2E,UAAA6U,aAAA,SAAAJ,GACA5Z,EAAA,uBAAA4Z,EAAAvR,KACA,IAAAqB,GAAApL,IAEAA,MAAAsb,YACA5Z,EAAA,iCAAA1B,KAAAsb,UAAAvR,MACA/J,KAAAsb,UAAA5R,sBAIA1J,KAAAsb,YAGAA,EACAjS,GAAA,mBACA+B,EAAAuQ,YAEAtS,GAAA,kBAAAsG,GACAvE,EAAAwQ,SAAAjM,KAEAtG,GAAA,iBAAAlG,GACAiI,EAAAyQ,QAAA1Y,KAEAkG,GAAA,mBACA+B,EAAA0Q,QAAA,sBAWA5Z,EAAA2E,UAAAkV,MAAA,SAAAhS,GAQA,QAAAiS,KACA,GAAA5Q,EAAAyO,mBAAA,CACA,GAAAoC,IAAAjc,KAAAkc,gBAAA9Q,EAAAkQ,UAAAY,cACAC,MAAAF,EAEAE,IAEAza,EAAA,8BAAAqI,GACAuR,EAAAc,OAAqBtY,KAAA,OAAAiL,KAAA,WACrBuM,EAAA/R,KAAA,kBAAAkH,GACA,IAAA0L,EACA,YAAA1L,EAAA3M,MAAA,UAAA2M,EAAA1B,KAAA,CAIA,GAHArN,EAAA,4BAAAqI,GACAqB,EAAAiR,WAAA,EACAjR,EAAAzB,KAAA,YAAA2R,IACAA,EAAA,MACApZ,GAAAgZ,sBAAA,cAAAI,EAAAvR,KAEArI,EAAA,iCAAA0J,EAAAkQ,UAAAvR,MACAqB,EAAAkQ,UAAAgB,MAAA,WACAH,GACA,WAAA/Q,EAAA6J,aACAvT,EAAA,iDAEAwV,IAEA9L,EAAAsQ,aAAAJ,GACAA,EAAAc,OAA2BtY,KAAA,aAC3BsH,EAAAzB,KAAA,UAAA2R,GACAA,EAAA,KACAlQ,EAAAiR,WAAA,EACAjR,EAAAmR,eAEO,CACP7a,EAAA,8BAAAqI,EACA,IAAA4H,GAAA,GAAArK,OAAA,cACAqK,GAAA2J,YAAAvR,KACAqB,EAAAzB,KAAA,eAAAgI,OAKA,QAAA6K,KACAL,IAGAA,GAAA,EAEAjF,IAEAoE,EAAAlE,QACAkE,EAAA,MAIA,QAAA7D,GAAA9F,GACA,GAAAzL,GAAA,GAAAoB,OAAA,gBAAAqK,EACAzL,GAAAoV,YAAAvR,KAEAyS,IAEA9a,EAAA,mDAAAqI,EAAA4H,GAEAvG,EAAAzB,KAAA,eAAAzD,GAGA,QAAAuW,KACAhF,EAAA,oBAIA,QAAAU,KACAV,EAAA,iBAIA,QAAAiF,GAAAC,GACArB,GAAAqB,EAAA5S,OAAAuR,EAAAvR,OACArI,EAAA,6BAAAib,EAAA5S,KAAAuR,EAAAvR,MACAyS,KAKA,QAAAtF,KACAoE,EAAA7R,eAAA,OAAAuS,GACAV,EAAA7R,eAAA,QAAAgO,GACA6D,EAAA7R,eAAA,QAAAgT,GACArR,EAAA3B,eAAA,QAAA0O,GACA/M,EAAA3B,eAAA,YAAAiT,GAhGAhb,EAAA,yBAAAqI,EACA,IAAAuR,GAAAtb,KAAAob,gBAAArR,GAA8CgS,MAAA,IAC9CI,GAAA,EACA/Q,EAAApL,IAEAkC,GAAAgZ,uBAAA,EA8FAI,EAAA/R,KAAA,OAAAyS,GACAV,EAAA/R,KAAA,QAAAkO,GACA6D,EAAA/R,KAAA,QAAAkT,GAEAzc,KAAAuJ,KAAA,QAAA4O,GACAnY,KAAAuJ,KAAA,YAAAmT,GAEApB,EAAA7F,QASAvT,EAAA2E,UAAA+V,OAAA,WASA,GARAlb,EAAA,eACA1B,KAAAiV,WAAA,OACA/S,EAAAgZ,sBAAA,cAAAlb,KAAAsb,UAAAvR,KACA/J,KAAA2J,KAAA,QACA3J,KAAAuc,QAIA,SAAAvc,KAAAiV,YAAAjV,KAAA8Y,SAAA9Y,KAAAsb,UAAAgB,MAAA,CACA5a,EAAA,0BACA,QAAA8B,GAAA,EAAAqZ,EAAA7c,KAAA2a,SAAArX,OAA6CE,EAAAqZ,EAAOrZ,IACpDxD,KAAA+b,MAAA/b,KAAA2a,SAAAnX,MAWAtB,EAAA2E,UAAA+U,SAAA,SAAAjM,GACA,eAAA3P,KAAAiV,YAAA,SAAAjV,KAAAiV,YACA,YAAAjV,KAAAiV,WAQA,OAPAvT,EAAA,uCAAAiO,EAAA7L,KAAA6L,EAAAZ,MAEA/O,KAAA2J,KAAA,SAAAgG,GAGA3P,KAAA2J,KAAA,aAEAgG,EAAA7L,MACA,WACA9D,KAAA8c,YAAA5V,KAAAsG,MAAAmC,EAAAZ,MACA,MAEA,YACA/O,KAAA+c,UACA/c,KAAA2J,KAAA,OACA,MAEA,aACA,GAAAgI,GAAA,GAAArK,OAAA,eACAqK,GAAAqL,KAAArN,EAAAZ,KACA/O,KAAA6b,QAAAlK,EACA,MAEA,eACA3R,KAAA2J,KAAA,OAAAgG,EAAAZ,MACA/O,KAAA2J,KAAA,UAAAgG,EAAAZ,UAIArN,GAAA,8CAAA1B,KAAAiV,aAWA/S,EAAA2E,UAAAiW,YAAA,SAAA/N,GACA/O,KAAA2J,KAAA,YAAAoF,GACA/O,KAAAK,GAAA0O,EAAAwM,IACAvb,KAAAsb,UAAA1Z,MAAA2Z,IAAAxM,EAAAwM,IACAvb,KAAA2a,SAAA3a,KAAAid,eAAAlO,EAAA4L,UACA3a,KAAA4a,aAAA7L,EAAA6L,aACA5a,KAAA6a,YAAA9L,EAAA8L,YACA7a,KAAA4c,SAEA,WAAA5c,KAAAiV,aACAjV,KAAA+c,UAGA/c,KAAAyJ,eAAA,YAAAzJ,KAAAkd,aACAld,KAAAqJ,GAAA,YAAArJ,KAAAkd,eASAhb,EAAA2E,UAAAqW,YAAA,SAAA3U,GACAR,aAAA/H,KAAA+a,iBACA,IAAA3P,GAAApL,IACAoL,GAAA2P,iBAAApT,WAAA,WACA,WAAAyD,EAAA6J,YACA7J,EAAA0Q,QAAA,iBACGvT,GAAA6C,EAAAwP,aAAAxP,EAAAyP,cAUH3Y,EAAA2E,UAAAkW,QAAA,WACA,GAAA3R,GAAApL,IACA+H,cAAAqD,EAAA0P,mBACA1P,EAAA0P,kBAAAnT,WAAA,WACAjG,EAAA,mDAAA0J,EAAAyP,aACAzP,EAAA+R,OACA/R,EAAA8R,YAAA9R,EAAAyP,cACGzP,EAAAwP,eASH1Y,EAAA2E,UAAAsW,KAAA,WACA,GAAA/R,GAAApL,IACAA,MAAAod,WAAA,kBACAhS,EAAAzB,KAAA,WAUAzH,EAAA2E,UAAA8U,QAAA,WACA3b,KAAAwZ,YAAAnU,OAAA,EAAArF,KAAAyZ,eAKAzZ,KAAAyZ,cAAA,EAEA,IAAAzZ,KAAAwZ,YAAAlW,OACAtD,KAAA2J,KAAA,SAEA3J,KAAAuc,SAUAra,EAAA2E,UAAA0V,MAAA,WACA,WAAAvc,KAAAiV,YAAAjV,KAAAsb,UAAA+B,WACArd,KAAAqc,WAAArc,KAAAwZ,YAAAlW,SACA5B,EAAA,gCAAA1B,KAAAwZ,YAAAlW,QACAtD,KAAAsb,UAAAc,KAAApc,KAAAwZ,aAGAxZ,KAAAyZ,cAAAzZ,KAAAwZ,YAAAlW,OACAtD,KAAA2J,KAAA,WAcAzH,EAAA2E,UAAA+Q,MACA1V,EAAA2E,UAAAuV,KAAA,SAAA3L,EAAAlC,EAAA4D,GAEA,MADAnS,MAAAod,WAAA,UAAA3M,EAAAlC,EAAA4D,GACAnS,MAaAkC,EAAA2E,UAAAuW,WAAA,SAAAtZ,EAAAiL,EAAAR,EAAA4D,GAWA,GAVA,kBAAApD,KACAoD,EAAApD,EACAA,EAAAjO,QAGA,kBAAAyN,KACA4D,EAAA5D,EACAA,EAAA,MAGA,YAAAvO,KAAAiV,YAAA,WAAAjV,KAAAiV,WAAA,CAIA1G,QACAA,EAAA+O,UAAA,IAAA/O,EAAA+O,QAEA,IAAA3N,IACA7L,OACAiL,OACAR,UAEAvO,MAAA2J,KAAA,eAAAgG,GACA3P,KAAAwZ,YAAAzQ,KAAA4G,GACAwC,GAAAnS,KAAAuJ,KAAA,QAAA4I,GACAnS,KAAAuc,UASAra,EAAA2E,UAAAuQ,MAAA,WAqBA,QAAAA,KACAhM,EAAA0Q,QAAA,gBACApa,EAAA,+CACA0J,EAAAkQ,UAAAlE,QAGA,QAAAmG,KACAnS,EAAA3B,eAAA,UAAA8T,GACAnS,EAAA3B,eAAA,eAAA8T,GACAnG,IAGA,QAAAoG,KAEApS,EAAA7B,KAAA,UAAAgU,GACAnS,EAAA7B,KAAA,eAAAgU,GAnCA,eAAAvd,KAAAiV,YAAA,SAAAjV,KAAAiV,WAAA,CACAjV,KAAAiV,WAAA,SAEA,IAAA7J,GAAApL,IAEAA,MAAAwZ,YAAAlW,OACAtD,KAAAuJ,KAAA,mBACAvJ,KAAAqc,UACAmB,IAEApG,MAGKpX,KAAAqc,UACLmB,IAEApG,IAsBA,MAAApX,OASAkC,EAAA2E,UAAAgV,QAAA,SAAAlK,GACAjQ,EAAA,kBAAAiQ,GACAzP,EAAAgZ,uBAAA,EACAlb,KAAA2J,KAAA,QAAAgI,GACA3R,KAAA8b,QAAA,kBAAAnK,IASAzP,EAAA2E,UAAAiV,QAAA,SAAA1D,EAAAqF,GACA,eAAAzd,KAAAiV,YAAA,SAAAjV,KAAAiV,YAAA,YAAAjV,KAAAiV,WAAA,CACAvT,EAAA,iCAAA0W,EACA,IAAAhN,GAAApL,IAGA+H,cAAA/H,KAAA8a,mBACA/S,aAAA/H,KAAA+a,kBAGA/a,KAAAsb,UAAA5R,mBAAA,SAGA1J,KAAAsb,UAAAlE,QAGApX,KAAAsb,UAAA5R,qBAGA1J,KAAAiV,WAAA,SAGAjV,KAAAK,GAAA,KAGAL,KAAA2J,KAAA,QAAAyO,EAAAqF,GAIArS,EAAAoO,eACApO,EAAAqO,cAAA,IAYAvX,EAAA2E,UAAAoW,eAAA,SAAAtC,GAEA,OADA+C,MACAla,EAAA,EAAAwD,EAAA2T,EAAArX,OAAsCE,EAAAwD,EAAOxD,KAC7C8B,EAAAtF,KAAAsZ,WAAAqB,EAAAnX,KAAAka,EAAA3U,KAAA4R,EAAAnX,GAEA,OAAAka,KnB8+FM,SAAU7d,EAAQD,EAASM,GoBjsHjC,QAAAyd,GAAA9c,GACA,GAAA+c,GACAC,GAAA,EACAC,GAAA,EACA9E,GAAA,IAAAnY,EAAAmY,KAEA,uBAAA3W,UAAA,CACA,GAAA0b,GAAA,WAAA1b,SAAAL,SACAU,EAAAL,SAAAK,IAGAA,KACAA,EAAAqb,EAAA,QAGAF,EAAAhd,EAAA4X,WAAApW,SAAAoW,UAAA/V,IAAA7B,EAAA6B,KACAob,EAAAjd,EAAA6X,SAAAqF,EAOA,GAJAld,EAAAmd,QAAAH,EACAhd,EAAAod,QAAAH,EACAF,EAAA,GAAAM,GAAArd,GAEA,QAAA+c,KAAA/c,EAAAkY,WACA,UAAAoF,GAAAtd,EAEA,KAAAmY,EAAA,SAAA1R,OAAA,iBACA,WAAA8W,GAAAvd,GA9CA,GAAAqd,GAAAhe,EAAA,IACAie,EAAAje,EAAA,IACAke,EAAAle,EAAA,IACAme,EAAAne,EAAA,GAMAN,GAAA+d,UACA/d,EAAAye,apBqwHM,SAAUxe,EAAQD,EAASM,GqBjxHjC,GAAAoe,GAAApe,EAAA,GAEAL,GAAAD,QAAA,SAAAiB,GACA,GAAAmd,GAAAnd,EAAAmd,QAIAC,EAAApd,EAAAod,QAIA/E,EAAArY,EAAAqY,UAGA,KACA,sBAAAgF,mBAAAF,GAAAM,GACA,UAAAJ,gBAEG,MAAA/a,IAKH,IACA,sBAAAob,kBAAAN,GAAA/E,EACA,UAAAqF,gBAEG,MAAApb,IAEH,IAAA6a,EACA,IACA,WAAA5S,MAAA,UAAAhD,OAAA,UAAA4E,KAAA,4BACK,MAAA7J,OrB4xHC,SAAUtD,EAAQD,GsBrzHxB,IACAC,EAAAD,QAAA,mBAAAse,iBACA,uBAAAA,gBACC,MAAAvM,GAGD9R,EAAAD,SAAA,ItBs0HM,SAAUC,EAAQD,EAASM,GuB9zHjC,QAAAse,MASA,QAAAL,GAAAtd,GAKA,GAJA4d,EAAAle,KAAAP,KAAAa,GACAb,KAAAwb,eAAA3a,EAAA2a,eACAxb,KAAAya,aAAA5Z,EAAA4Z,aAEA,mBAAApY,UAAA,CACA,GAAA0b,GAAA,WAAA1b,SAAAL,SACAU,EAAAL,SAAAK,IAGAA,KACAA,EAAAqb,EAAA,QAGA/d,KAAA6d,GAAA,mBAAAxb,WAAAxB,EAAA4X,WAAApW,SAAAoW,UACA/V,IAAA7B,EAAA6B,KACA1C,KAAA8d,GAAAjd,EAAA6X,SAAAqF,GA8FA,QAAAW,GAAA7d,GACAb,KAAA2e,OAAA9d,EAAA8d,QAAA,MACA3e,KAAAY,IAAAC,EAAAD,IACAZ,KAAA6d,KAAAhd,EAAAgd,GACA7d,KAAA8d,KAAAjd,EAAAid,GACA9d,KAAA4e,OAAA,IAAA/d,EAAA+d,MACA5e,KAAA+O,KAAAjO,SAAAD,EAAAkO,KAAAlO,EAAAkO,KAAA,KACA/O,KAAA2Y,MAAA9X,EAAA8X,MACA3Y,KAAA6e,SAAAhe,EAAAge,SACA7e,KAAAkc,eAAArb,EAAAqb,eACAlc,KAAAkZ,WAAArY,EAAAqY,WACAlZ,KAAAmZ,gBAAAtY,EAAAsY,gBACAnZ,KAAAwb,eAAA3a,EAAA2a,eAGAxb,KAAAga,IAAAnZ,EAAAmZ,IACAha,KAAAuN,IAAA1M,EAAA0M,IACAvN,KAAAia,WAAApZ,EAAAoZ,WACAja,KAAAka,KAAArZ,EAAAqZ,KACAla,KAAAma,GAAAtZ,EAAAsZ,GACAna,KAAAoa,QAAAvZ,EAAAuZ,QACApa,KAAAqa,mBAAAxZ,EAAAwZ,mBAGAra,KAAAya,aAAA5Z,EAAA4Z,aAEAza,KAAA8e,SAkPA,QAAAC,KACA,OAAAvb,KAAAkb,GAAAM,SACAN,EAAAM,SAAApJ,eAAApS,IACAkb,EAAAM,SAAAxb,GAAAyb,QAvZA,GAAAf,GAAAhe,EAAA,IACAue,EAAAve,EAAA,IACAwQ,EAAAxQ,EAAA,IACAgf,EAAAhf,EAAA,IACAwB,EAAAxB,EAAA,kCAuYA,IAjYAL,EAAAD,QAAAue,EACAte,EAAAD,QAAA8e,UAuCAQ,EAAAf,EAAAM,GAMAN,EAAAtX,UAAAqV,gBAAA,EASAiC,EAAAtX,UAAAsY,QAAA,SAAAte,GAuBA,MAtBAA,SACAA,EAAAD,IAAAZ,KAAAY,MACAC,EAAAgd,GAAA7d,KAAA6d,GACAhd,EAAAid,GAAA9d,KAAA8d,GACAjd,EAAA8X,MAAA3Y,KAAA2Y,QAAA,EACA9X,EAAAqb,eAAAlc,KAAAkc,eACArb,EAAAqY,WAAAlZ,KAAAkZ,WACArY,EAAAsY,gBAAAnZ,KAAAmZ,gBAGAtY,EAAAmZ,IAAAha,KAAAga,IACAnZ,EAAA0M,IAAAvN,KAAAuN,IACA1M,EAAAoZ,WAAAja,KAAAia,WACApZ,EAAAqZ,KAAAla,KAAAka,KACArZ,EAAAsZ,GAAAna,KAAAma,GACAtZ,EAAAuZ,QAAApa,KAAAoa,QACAvZ,EAAAwZ,mBAAAra,KAAAqa,mBACAxZ,EAAA2a,eAAAxb,KAAAwb,eAGA3a,EAAA4Z,aAAAza,KAAAya,aAEA,GAAAiE,GAAA7d,IAWAsd,EAAAtX,UAAAuY,QAAA,SAAArQ,EAAAoD,GACA,GAAA0M,GAAA,gBAAA9P,IAAAjO,SAAAiO,EACAsQ,EAAArf,KAAAmf,SAA0BR,OAAA,OAAA5P,OAAA8P,aAC1BzT,EAAApL,IACAqf,GAAAhW,GAAA,UAAA8I,GACAkN,EAAAhW,GAAA,iBAAAsI,GACAvG,EAAAyQ,QAAA,iBAAAlK,KAEA3R,KAAAsf,QAAAD,GASAlB,EAAAtX,UAAA0Y,OAAA,WACA7d,EAAA,WACA,IAAA2d,GAAArf,KAAAmf,UACA/T,EAAApL,IACAqf,GAAAhW,GAAA,gBAAA0F,GACA3D,EAAAoU,OAAAzQ,KAEAsQ,EAAAhW,GAAA,iBAAAsI,GACAvG,EAAAyQ,QAAA,iBAAAlK,KAEA3R,KAAAyf,QAAAJ,GA2CA3O,EAAAgO,EAAA7X,WAQA6X,EAAA7X,UAAAiY,OAAA,WACA,GAAAje,IAAc8X,MAAA3Y,KAAA2Y,MAAAqF,QAAAhe,KAAA6d,GAAAI,QAAAje,KAAA8d,GAAA5E,WAAAlZ,KAAAkZ,WAGdrY,GAAAmZ,IAAAha,KAAAga,IACAnZ,EAAA0M,IAAAvN,KAAAuN,IACA1M,EAAAoZ,WAAAja,KAAAia,WACApZ,EAAAqZ,KAAAla,KAAAka,KACArZ,EAAAsZ,GAAAna,KAAAma,GACAtZ,EAAAuZ,QAAApa,KAAAoa,QACAvZ,EAAAwZ,mBAAAra,KAAAqa,kBAEA,IAAAuD,GAAA5d,KAAA4d,IAAA,GAAAM,GAAArd,GACAuK,EAAApL,IAEA,KACA0B,EAAA,kBAAA1B,KAAA2e,OAAA3e,KAAAY,KACAgd,EAAAnI,KAAAzV,KAAA2e,OAAA3e,KAAAY,IAAAZ,KAAA4e,MACA,KACA,GAAA5e,KAAAya,aAAA,CACAmD,EAAA8B,uBAAA9B,EAAA8B,uBAAA,EACA,QAAAlc,KAAAxD,MAAAya,aACAza,KAAAya,aAAA7E,eAAApS,IACAoa,EAAA+B,iBAAAnc,EAAAxD,KAAAya,aAAAjX,KAIK,MAAAL,IAEL,YAAAnD,KAAA2e,OACA,IACA3e,KAAA6e,SACAjB,EAAA+B,iBAAA,2CAEA/B,EAAA+B,iBAAA,2CAEO,MAAAxc,IAGP,IACAya,EAAA+B,iBAAA,gBACK,MAAAxc,IAGL,mBAAAya,KACAA,EAAAzE,gBAAAnZ,KAAAmZ,iBAGAnZ,KAAAwb,iBACAoC,EAAArV,QAAAvI,KAAAwb,gBAGAxb,KAAA4f,UACAhC,EAAAjK,OAAA,WACAvI,EAAAyU,UAEAjC,EAAAnG,QAAA,WACArM,EAAAyQ,QAAA+B,EAAAkC,gBAGAlC,EAAAmC,mBAAA,WACA,OAAAnC,EAAA3I,WACA,IACA,GAAA+K,GAAApC,EAAAqC,kBAAA,iBACA7U,EAAA8Q,gBAAA,6BAAA8D,GAAA,4CAAAA,KACApC,EAAAsC,aAAA,eAEW,MAAA/c,IAEX,IAAAya,EAAA3I,aACA,MAAA2I,EAAAuC,QAAA,OAAAvC,EAAAuC,OACA/U,EAAAyU,SAIAlY,WAAA,WACAyD,EAAAyQ,QAAA,gBAAA+B,GAAAuC,OAAAvC,EAAAuC,OAAA,IACW,KAKXze,EAAA,cAAA1B,KAAA+O,MACA6O,EAAAxB,KAAApc,KAAA+O,MACG,MAAA5L,GAOH,WAHAwE,YAAA,WACAyD,EAAAyQ,QAAA1Y,IACK,GAIL,mBAAAiB,YACApE,KAAAsF,MAAAoZ,EAAA0B,gBACA1B,EAAAM,SAAAhf,KAAAsF,OAAAtF,OAUA0e,EAAA7X,UAAAwZ,UAAA,WACArgB,KAAA2J,KAAA,WACA3J,KAAAkX,WASAwH,EAAA7X,UAAA2Y,OAAA,SAAAzQ,GACA/O,KAAA2J,KAAA,OAAAoF,GACA/O,KAAAqgB,aASA3B,EAAA7X,UAAAgV,QAAA,SAAAlK,GACA3R,KAAA2J,KAAA,QAAAgI,GACA3R,KAAAkX,SAAA,IASAwH,EAAA7X,UAAAqQ,QAAA,SAAAoJ,GACA,sBAAAtgB,MAAA4d,KAAA,OAAA5d,KAAA4d,IAAA,CAUA,GANA5d,KAAA4f,SACA5f,KAAA4d,IAAAjK,OAAA3T,KAAA4d,IAAAnG,QAAA+G,EAEAxe,KAAA4d,IAAAmC,mBAAAvB,EAGA8B,EACA,IACAtgB,KAAA4d,IAAAqB,QACK,MAAA9b,IAGL,mBAAAiB,iBACAsa,GAAAM,SAAAhf,KAAAsF,OAGAtF,KAAA4d,IAAA,OASAc,EAAA7X,UAAAgZ,OAAA,WACA,GAAA9Q,EACA,KACA,GAAAiR,EACA,KACAA,EAAAhgB,KAAA4d,IAAAqC,kBAAA,gBACK,MAAA9c,IAEL4L,EADA,6BAAAiR,GAAA,4CAAAA,EACAhgB,KAAA4d,IAAA2C,UAAAvgB,KAAA4d,IAAAkC,aAEA9f,KAAA4d,IAAAkC,aAEG,MAAA3c,GACHnD,KAAA6b,QAAA1Y,GAEA,MAAA4L,GACA/O,KAAAwf,OAAAzQ,IAUA2P,EAAA7X,UAAA+Y,OAAA,WACA,yBAAArB,kBAAAve,KAAA8d,IAAA9d,KAAAkZ,YASAwF,EAAA7X,UAAAoY,MAAA,WACAjf,KAAAkX,WASAwH,EAAA0B,cAAA,EACA1B,EAAAM,YAEA,mBAAA5a,UACA,qBAAAoc,aACAA,YAAA,WAAAzB,OACG,sBAAA9M,kBAAA,CACH,GAAAwO,GAAA,cAAArV,MAAA,mBACA6G,kBAAAwO,EAAA1B,GAAA,KvBs2HM,SAAUlf,EAAQD,EAASM,GwB1tIjC,QAAAue,GAAA5d,GACA,GAAAoY,GAAApY,KAAAoY,WACAyH,KAAAzH,IACAjZ,KAAAkc,gBAAA,GAEAf,EAAA5a,KAAAP,KAAAa,GAnCA,GAAAsa,GAAAjb,EAAA,IACA0Y,EAAA1Y,EAAA,IACA4B,EAAA5B,EAAA,IACAgf,EAAAhf,EAAA,IACAygB,EAAAzgB,EAAA,IACAwB,EAAAxB,EAAA,8BAMAL,GAAAD,QAAA6e,CAMA,IAAAiC,GAAA,WACA,GAAAxC,GAAAhe,EAAA,IACA0d,EAAA,GAAAM,IAAgCF,SAAA,GAChC,cAAAJ,EAAAsC,eAsBAhB,GAAAT,EAAAtD,GAMAsD,EAAA5X,UAAAkD,KAAA,UASA0U,EAAA5X,UAAA+Z,OAAA,WACA5gB,KAAA6gB,QAUApC,EAAA5X,UAAAyV,MAAA,SAAAwE,GAKA,QAAAxE,KACA5a,EAAA,UACA0J,EAAA6J,WAAA,SACA6L,IAPA,GAAA1V,GAAApL,IAUA,IARAA,KAAAiV,WAAA,UAQAjV,KAAA2d,UAAA3d,KAAAqd,SAAA,CACA,GAAA0D,GAAA,CAEA/gB,MAAA2d,UACAjc,EAAA,+CACAqf,IACA/gB,KAAAuJ,KAAA,0BACA7H,EAAA,gCACAqf,GAAAzE,OAIAtc,KAAAqd,WACA3b,EAAA,+CACAqf,IACA/gB,KAAAuJ,KAAA,mBACA7H,EAAA,gCACAqf,GAAAzE,WAIAA,MAUAmC,EAAA5X,UAAAga,KAAA,WACAnf,EAAA,WACA1B,KAAA2d,SAAA,EACA3d,KAAAuf,SACAvf,KAAA2J,KAAA,SASA8U,EAAA5X,UAAA2Y,OAAA,SAAAzQ,GACA,GAAA3D,GAAApL,IACA0B,GAAA,sBAAAqN,EACA,IAAAK,GAAA,SAAAO,EAAArK,EAAAyb,GAOA,MALA,YAAA3V,EAAA6J,YACA7J,EAAAwR,SAIA,UAAAjN,EAAA7L,MACAsH,EAAA0Q,WACA,OAIA1Q,GAAAwQ,SAAAjM,GAIA7N,GAAAkf,cAAAjS,EAAA/O,KAAA6B,OAAA+X,WAAAxK,GAGA,WAAApP,KAAAiV,aAEAjV,KAAA2d,SAAA,EACA3d,KAAA2J,KAAA,gBAEA,SAAA3J,KAAAiV,WACAjV,KAAA6gB,OAEAnf,EAAA,uCAAA1B,KAAAiV,cAWAwJ,EAAA5X,UAAAoa,QAAA,WAGA,QAAA7J,KACA1V,EAAA,wBACA0J,EAAAwM,QAAiB9T,KAAA,WAJjB,GAAAsH,GAAApL,IAOA,UAAAA,KAAAiV,YACAvT,EAAA,4BACA0V,MAIA1V,EAAA,wCACA1B,KAAAuJ,KAAA,OAAA6N,KAYAqH,EAAA5X,UAAA+Q,MAAA,SAAAsJ,GACA,GAAA9V,GAAApL,IACAA,MAAAqd,UAAA,CACA,IAAA8D,GAAA,WACA/V,EAAAiS,UAAA,EACAjS,EAAAzB,KAAA,SAGA7H,GAAAsf,cAAAF,EAAAlhB,KAAAkc,eAAA,SAAAnN,GACA3D,EAAAgU,QAAArQ,EAAAoS,MAUA1C,EAAA5X,UAAAjG,IAAA,WACA,GAAAgB,GAAA5B,KAAA4B,UACAyf,EAAArhB,KAAA0Y,OAAA,eACAhW,EAAA,IAGA,IAAA1C,KAAAqZ,oBACAzX,EAAA5B,KAAAoZ,gBAAAuH,KAGA3gB,KAAAkc,gBAAAta,EAAA2Z,MACA3Z,EAAA0f,IAAA,GAGA1f,EAAAgX,EAAA5H,OAAApP,GAGA5B,KAAA0C,OAAA,UAAA2e,GAAA,MAAA/V,OAAAtL,KAAA0C,OACA,SAAA2e,GAAA,KAAA/V,OAAAtL,KAAA0C,SACAA,EAAA,IAAA1C,KAAA0C,MAIAd,EAAA0B,SACA1B,EAAA,IAAAA,EAGA,IAAAe,GAAA3C,KAAAyY,SAAA7V,QAAA,SACA,OAAAye,GAAA,OAAA1e,EAAA,IAAA3C,KAAAyY,SAAA,IAAAzY,KAAAyY,UAAA/V,EAAA1C,KAAAmB,KAAAS,IxBowIM,SAAU/B,EAAQD,EAASM,GyBn+IjC,QAAAib,GAAAta,GACAb,KAAAmB,KAAAN,EAAAM,KACAnB,KAAAyY,SAAA5X,EAAA4X,SACAzY,KAAA0C,KAAA7B,EAAA6B,KACA1C,KAAA0Y,OAAA7X,EAAA6X,OACA1Y,KAAA4B,MAAAf,EAAAe,MACA5B,KAAAoZ,eAAAvY,EAAAuY,eACApZ,KAAAqZ,kBAAAxY,EAAAwY,kBACArZ,KAAAiV,WAAA,GACAjV,KAAA2Y,MAAA9X,EAAA8X,QAAA,EACA3Y,KAAA6B,OAAAhB,EAAAgB,OACA7B,KAAAkZ,WAAArY,EAAAqY,WACAlZ,KAAAmZ,gBAAAtY,EAAAsY,gBAGAnZ,KAAAga,IAAAnZ,EAAAmZ,IACAha,KAAAuN,IAAA1M,EAAA0M,IACAvN,KAAAia,WAAApZ,EAAAoZ,WACAja,KAAAka,KAAArZ,EAAAqZ,KACAla,KAAAma,GAAAtZ,EAAAsZ,GACAna,KAAAoa,QAAAvZ,EAAAuZ,QACApa,KAAAqa,mBAAAxZ,EAAAwZ,mBACAra,KAAAsa,UAAAzZ,EAAAyZ,UAGAta,KAAAua,cAAA1Z,EAAA0Z,cAGAva,KAAAya,aAAA5Z,EAAA4Z,aACAza,KAAA0a,aAAA7Z,EAAA6Z,aA7CA,GAAA5Y,GAAA5B,EAAA,IACAwQ,EAAAxQ,EAAA,GAMAL,GAAAD,QAAAub,EA6CAzK,EAAAyK,EAAAtU,WAUAsU,EAAAtU,UAAAgV,QAAA,SAAApL,EAAAgN,GACA,GAAA9L,GAAA,GAAArK,OAAAmJ,EAIA,OAHAkB,GAAA7N,KAAA,iBACA6N,EAAA4P,YAAA9D,EACAzd,KAAA2J,KAAA,QAAAgI,GACA3R,MASAmb,EAAAtU,UAAA4O,KAAA,WAMA,MALA,WAAAzV,KAAAiV,YAAA,KAAAjV,KAAAiV,aACAjV,KAAAiV,WAAA,UACAjV,KAAA4gB,UAGA5gB,MASAmb,EAAAtU,UAAAuQ,MAAA,WAMA,MALA,YAAApX,KAAAiV,YAAA,SAAAjV,KAAAiV,aACAjV,KAAAihB,UACAjhB,KAAA8b,WAGA9b,MAUAmb,EAAAtU,UAAAuV,KAAA,SAAA8E,GACA,YAAAlhB,KAAAiV,WAGA,SAAA3N,OAAA,qBAFAtH,MAAA4X,MAAAsJ,IAYA/F,EAAAtU,UAAA+V,OAAA,WACA5c,KAAAiV,WAAA,OACAjV,KAAAqd,UAAA,EACArd,KAAA2J,KAAA,SAUAwR,EAAAtU,UAAA2Y,OAAA,SAAAzQ,GACA,GAAAY,GAAA7N,EAAA0f,aAAAzS,EAAA/O,KAAA6B,OAAA+X,WACA5Z,MAAA4b,SAAAjM,IAOAwL,EAAAtU,UAAA+U,SAAA,SAAAjM,GACA3P,KAAA2J,KAAA,SAAAgG,IASAwL,EAAAtU,UAAAiV,QAAA,WACA9b,KAAAiV,WAAA,SACAjV,KAAA2J,KAAA,WzB+/IM,SAAU9J,EAAQD,EAASM,G0BhiJjC,QAAAuhB,GAAA9R,EAAAP,GAEA,GAAAhI,GAAA,IAAAxH,EAAAshB,QAAAvR,EAAA7L,MAAA6L,EAAAZ,SACA,OAAAK,GAAAhI,GAOA,QAAAsa,GAAA/R,EAAAuM,EAAA9M,GACA,IAAA8M,EACA,MAAAtc,GAAA+hB,mBAAAhS,EAAAP,EAGA,IAAAL,GAAAY,EAAAZ,KACA6S,EAAA,GAAAC,YAAA9S,GACA+S,EAAA,GAAAD,YAAA,EAAA9S,EAAAgT,WAEAD,GAAA,GAAAZ,EAAAvR,EAAA7L,KACA,QAAAN,GAAA,EAAiBA,EAAAoe,EAAAte,OAAyBE,IAC1Cse,EAAAte,EAAA,GAAAoe,EAAApe,EAGA,OAAA4L,GAAA0S,EAAA1N,QAGA,QAAA4N,GAAArS,EAAAuM,EAAA9M,GACA,IAAA8M,EACA,MAAAtc,GAAA+hB,mBAAAhS,EAAAP,EAGA,IAAA6S,GAAA,GAAAvO,WAIA,OAHAuO,GAAAtO,OAAA,WACA/T,EAAAsiB,cAA0Bpe,KAAA6L,EAAA7L,KAAAiL,KAAAkT,EAAArO,QAAqCsI,GAAA,EAAA9M,IAE/D6S,EAAApO,kBAAAlE,EAAAZ,MAGA,QAAAoT,GAAAxS,EAAAuM,EAAA9M,GACA,IAAA8M,EACA,MAAAtc,GAAA+hB,mBAAAhS,EAAAP,EAGA,IAAAgT,EACA,MAAAJ,GAAArS,EAAAuM,EAAA9M,EAGA,IAAA9L,GAAA,GAAAue,YAAA,EACAve,GAAA,GAAA4d,EAAAvR,EAAA7L,KACA,IAAAue,GAAA,GAAApP,IAAA3P,EAAA8Q,OAAAzE,EAAAZ,MAEA,OAAAK,GAAAiT,GAkFA,QAAAC,GAAAvT,GACA,IACAA,EAAAwT,EAAA1J,OAAA9J,GAA8ByT,QAAA,IAC3B,MAAArf,GACH,SAEA,MAAA4L,GAgFA,QAAAjC,GAAA2V,EAAAC,EAAAC,GAWA,OAVA/O,GAAA,GAAA9K,OAAA2Z,EAAAnf,QACA6M,EAAAyS,EAAAH,EAAAnf,OAAAqf,GAEAE,EAAA,SAAArf,EAAAsf,EAAAvQ,GACAmQ,EAAAI,EAAA,SAAA5c,EAAAuK,GACAmD,EAAApQ,GAAAiN,EACA8B,EAAArM,EAAA0N,MAIApQ,EAAA,EAAiBA,EAAAif,EAAAnf,OAAgBE,IACjCqf,EAAArf,EAAAif,EAAAjf,GAAA2M,GAlWA,GAMA4S,GANA1V,EAAAnN,EAAA,IACA8iB,EAAA9iB,EAAA,IACA+iB,EAAA/iB,EAAA,IACA0iB,EAAA1iB,EAAA,IACAqiB,EAAAriB,EAAA,GAGA,oBAAAgU,eACA6O,EAAA7iB,EAAA,IAUA,IAAAgjB,GAAA,mBAAAlf,YAAA,WAAAxB,KAAAwB,UAAAC,WAQAkf,EAAA,mBAAAnf,YAAA,aAAAxB,KAAAwB,UAAAC,WAMAme,EAAAc,GAAAC,CAMAvjB,GAAAoC,SAAA,CAMA,IAAAkf,GAAAthB,EAAAshB,SACAzL,KAAA,EACA2B,MAAA,EACA+F,KAAA,EACAiG,KAAA,EACAhc,QAAA,EACA0R,QAAA,EACAlQ,KAAA,GAGAya,EAAAhW,EAAA6T,GAMAvP,GAAW7N,KAAA,QAAAiL,KAAA,gBAMXkE,EAAA/S,EAAA,GAkBAN,GAAAsiB,aAAA,SAAAvS,EAAAuM,EAAAoH,EAAAlU,GACA,kBAAA8M,KACA9M,EAAA8M,EACAA,GAAA,GAGA,kBAAAoH,KACAlU,EAAAkU,EACAA,EAAA,KAGA,IAAAvU,GAAAjO,SAAA6O,EAAAZ,KACAjO,OACA6O,EAAAZ,KAAAqF,QAAAzE,EAAAZ,IAEA,uBAAAmF,cAAAnF,YAAAmF,aACA,MAAAwN,GAAA/R,EAAAuM,EAAA9M,EACG,uBAAA6D,IAAAlE,YAAAkE,GACH,MAAAkP,GAAAxS,EAAAuM,EAAA9M,EAIA,IAAAL,KAAAoC,OACA,MAAAsQ,GAAA9R,EAAAP,EAIA,IAAAmU,GAAArC,EAAAvR,EAAA7L,KAOA,OAJAhD,UAAA6O,EAAAZ,OACAwU,GAAAD,EAAAf,EAAAvR,OAAAvD,OAAAkC,EAAAZ,OAA8DyT,QAAA,IAAgB/U,OAAAkC,EAAAZ,OAG9EK,EAAA,GAAAmU,IAkEA3jB,EAAA+hB,mBAAA,SAAAhS,EAAAP,GACA,GAAAhI,GAAA,IAAAxH,EAAAshB,QAAAvR,EAAA7L,KACA,uBAAAmP,IAAAtD,EAAAZ,eAAAkE,GAAA,CACA,GAAAgP,GAAA,GAAAvO,WAKA,OAJAuO,GAAAtO,OAAA,WACA,GAAA2N,GAAAW,EAAArO,OAAAlH,MAAA,OACA0C,GAAAhI,EAAAka,IAEAW,EAAAuB,cAAA7T,EAAAZ,MAGA,GAAA0U,EACA,KACAA,EAAAhW,OAAAiW,aAAA/d,MAAA,QAAAkc,YAAAlS,EAAAZ,OACG,MAAA5L,GAIH,OAFAwgB,GAAA,GAAA9B,YAAAlS,EAAAZ,MACA6U,EAAA,GAAA9a,OAAA6a,EAAArgB,QACAE,EAAA,EAAmBA,EAAAmgB,EAAArgB,OAAkBE,IACrCogB,EAAApgB,GAAAmgB,EAAAngB,EAEAigB,GAAAhW,OAAAiW,aAAA/d,MAAA,KAAAie,GAGA,MADAxc,IAAAyc,KAAAJ,GACArU,EAAAhI,IAUAxH,EAAA4hB,aAAA,SAAAzS,EAAA6K,EAAAkK,GACA,GAAAhjB,SAAAiO,EACA,MAAA4C,EAGA,oBAAA5C,GAAA,CACA,SAAAA,EAAAxM,OAAA,GACA,MAAA3C,GAAAmkB,mBAAAhV,EAAApC,OAAA,GAAAiN,EAGA,IAAAkK,IACA/U,EAAAuT,EAAAvT,GACAA,KAAA,GACA,MAAA4C,EAGA,IAAA7N,GAAAiL,EAAAxM,OAAA,EAEA,OAAA+I,QAAAxH,OAAAuf,EAAAvf,GAIAiL,EAAAzL,OAAA,GACcQ,KAAAuf,EAAAvf,GAAAiL,OAAA3L,UAAA,KAEAU,KAAAuf,EAAAvf,IANd6N,EAUA,GAAAqS,GAAA,GAAAnC,YAAA9S,GACAjL,EAAAkgB,EAAA,GACAC,EAAAhB,EAAAlU,EAAA,EAIA,OAHAkE,IAAA,SAAA2G,IACAqK,EAAA,GAAAhR,IAAAgR,MAEUngB,KAAAuf,EAAAvf,GAAAiL,KAAAkV,IAmBVrkB,EAAAmkB,mBAAA,SAAAtT,EAAAmJ,GACA,GAAA9V,GAAAuf,EAAA5S,EAAAlO,OAAA,GACA,KAAAwgB,EACA,OAAYjf,OAAAiL,MAAoBoC,QAAA,EAAApC,KAAA0B,EAAA9D,OAAA,IAGhC,IAAAoC,GAAAgU,EAAAlK,OAAApI,EAAA9D,OAAA,GAMA,OAJA,SAAAiN,GAAA3G,IACAlE,EAAA,GAAAkE,IAAAlE,MAGUjL,OAAAiL,SAmBVnP,EAAAwhB,cAAA,SAAAF,EAAAhF,EAAA9M,GAoBA,QAAA8U,GAAA9c,GACA,MAAAA,GAAA9D,OAAA,IAAA8D,EAGA,QAAA+c,GAAAxU,EAAAyU,GACAxkB,EAAAsiB,aAAAvS,IAAAkP,GAAA3C,GAAA,WAAA9U,GACAgd,EAAA,KAAAF,EAAA9c,MAzBA,kBAAA8U,KACA9M,EAAA8M,EACAA,EAAA,KAGA,IAAA2C,GAAAmE,EAAA9B,EAEA,OAAAhF,IAAA2C,EACA5L,IAAAmP,EACAxiB,EAAAykB,oBAAAnD,EAAA9R,GAGAxP,EAAA0kB,2BAAApD,EAAA9R,GAGA8R,EAAA5d,WAcAwJ,GAAAoU,EAAAiD,EAAA,SAAAxS,EAAA4S,GACA,MAAAnV,GAAAmV,EAAAvX,KAAA,OAdAoC,EAAA,OA8CAxP,EAAAohB,cAAA,SAAAjS,EAAA6K,EAAAxK,GACA,mBAAAL,GACA,MAAAnP,GAAA4kB,sBAAAzV,EAAA6K,EAAAxK,EAGA,mBAAAwK,KACAxK,EAAAwK,EACAA,EAAA,KAGA,IAAAjK,EACA,SAAAZ,EAEA,MAAAK,GAAAuC,EAAA,IAKA,QAFAjE,GAAA+C,EAAAnN,EAAA,GAEAE,EAAA,EAAAqZ,EAAA9N,EAAAzL,OAAkCE,EAAAqZ,EAAOrZ,IAAA,CACzC,GAAAihB,GAAA1V,EAAAxM,OAAAiB,EAEA,UAAAihB,EAAA,CAKA,QAAAnhB,OAAAoK,EAAApC,OAAAhI,IAEA,MAAA8L,GAAAuC,EAAA,IAKA,IAFAlB,EAAA1B,EAAApC,OAAAnJ,EAAA,EAAAkK,GAEApK,GAAAmN,EAAAnN,OAEA,MAAA8L,GAAAuC,EAAA,IAGA,IAAAlB,EAAAnN,OAAA,CAGA,GAFAqM,EAAA/P,EAAA4hB,aAAA/Q,EAAAmJ,GAAA,GAEAjI,EAAA7N,OAAA6L,EAAA7L,MAAA6N,EAAA5C,OAAAY,EAAAZ,KAEA,MAAAK,GAAAuC,EAAA,IAGA,IAAA+S,GAAAtV,EAAAO,EAAAnM,EAAAkK,EAAAmP,EACA,SAAA6H,EAAA,OAIAlhB,GAAAkK,EACApK,EAAA,OA9BAA,IAAAmhB,EAiCA,WAAAnhB,EAEA8L,EAAAuC,EAAA,KAFA,QAqBA/R,EAAA0kB,2BAAA,SAAApD,EAAA9R,GAKA,QAAA+U,GAAAxU,EAAAyU,GACAxkB,EAAAsiB,aAAAvS,GAAA,cAAAZ,GACA,MAAAqV,GAAA,KAAArV,KANA,MAAAmS,GAAA5d,WAUAwJ,GAAAoU,EAAAiD,EAAA,SAAAxS,EAAAgG,GACA,GAAAgN,GAAAhN,EAAAiN,OAAA,SAAAC,EAAAnkB,GACA,GAAA8H,EAMA,OAJAA,GADA,gBAAA9H,GACAA,EAAA4C,OAEA5C,EAAAqhB,WAEA8C,EAAArc,EAAA0E,WAAA5J,OAAAkF,EAAA,GACK,GAELsc,EAAA,GAAAjD,YAAA8C,GAEAI,EAAA,CA8BA,OA7BApN,GAAArK,QAAA,SAAA5M,GACA,GAAAskB,GAAA,gBAAAtkB,GACAukB,EAAAvkB,CACA,IAAAskB,EAAA,CAEA,OADAE,GAAA,GAAArD,YAAAnhB,EAAA4C,QACAE,EAAA,EAAuBA,EAAA9C,EAAA4C,OAAcE,IACrC0hB,EAAA1hB,GAAA9C,EAAAmK,WAAArH,EAEAyhB,GAAAC,EAAA9Q,OAGA4Q,EACAF,EAAAC,KAAA,EAEAD,EAAAC,KAAA,CAIA,QADAI,GAAAF,EAAAlD,WAAA7U,WACA1J,EAAA,EAAqBA,EAAA2hB,EAAA7hB,OAAmBE,IACxCshB,EAAAC,KAAAngB,SAAAugB,EAAA3hB,GAEAshB,GAAAC,KAAA,GAGA,QADAG,GAAA,GAAArD,YAAAoD,GACAzhB,EAAA,EAAqBA,EAAA0hB,EAAA5hB,OAAiBE,IACtCshB,EAAAC,KAAAG,EAAA1hB,KAIA4L,EAAA0V,EAAA1Q,UApDAhF,EAAA,GAAA8E,aAAA,KA4DAtU,EAAAykB,oBAAA,SAAAnD,EAAA9R,GACA,QAAA+U,GAAAxU,EAAAyU,GACAxkB,EAAAsiB,aAAAvS,GAAA,cAAA4T,GACA,GAAA6B,GAAA,GAAAvD,YAAA,EAEA,IADAuD,EAAA,KACA,gBAAA7B,GAAA,CAEA,OADA2B,GAAA,GAAArD,YAAA0B,EAAAjgB,QACAE,EAAA,EAAuBA,EAAA+f,EAAAjgB,OAAoBE,IAC3C0hB,EAAA1hB,GAAA+f,EAAA1Y,WAAArH,EAEA+f,GAAA2B,EAAA9Q,OACAgR,EAAA,KASA,OANA5c,GAAA+a,YAAArP,aACAqP,EAAAxB,WACAwB,EAAA8B,KAEAF,EAAA3c,EAAA0E,WACAoY,EAAA,GAAAzD,YAAAsD,EAAA7hB,OAAA,GACAE,EAAA,EAAqBA,EAAA2hB,EAAA7hB,OAAmBE,IACxC8hB,EAAA9hB,GAAAoB,SAAAugB,EAAA3hB,GAIA,IAFA8hB,EAAAH,EAAA7hB,QAAA,IAEA2P,EAAA,CACA,GAAAoP,GAAA,GAAApP,IAAAmS,EAAAhR,OAAAkR,EAAAlR,OAAAmP,GACAa,GAAA,KAAA/B,MAKAvV,EAAAoU,EAAAiD,EAAA,SAAAxS,EAAA4S,GACA,MAAAnV,GAAA,GAAA6D,GAAAsR,OAaA3kB,EAAA4kB,sBAAA,SAAAzV,EAAA6K,EAAAxK,GACA,kBAAAwK,KACAxK,EAAAwK,EACAA,EAAA,KAMA,KAHA,GAAA2L,GAAAxW,EACAa,KAEA2V,EAAAxD,WAAA,IAKA,OAJAyD,GAAA,GAAA3D,YAAA0D,GACAP,EAAA,IAAAQ,EAAA,GACAC,EAAA,GAEAjiB,EAAA,EACA,MAAAgiB,EAAAhiB,GADqBA,IAAA,CAIrB,GAAAiiB,EAAAniB,OAAA,IACA,MAAA8L,GAAAuC,EAAA,IAGA8T,IAAAD,EAAAhiB,GAGA+hB,EAAAtC,EAAAsC,EAAA,EAAAE,EAAAniB,QACAmiB,EAAA7gB,SAAA6gB,EAEA,IAAAhV,GAAAwS,EAAAsC,EAAA,EAAAE,EACA,IAAAT,EACA,IACAvU,EAAAhD,OAAAiW,aAAA/d,MAAA,QAAAkc,YAAApR,IACO,MAAAtN,GAEP,GAAAwgB,GAAA,GAAA9B,YAAApR,EACAA,GAAA,EACA,QAAAjN,GAAA,EAAuBA,EAAAmgB,EAAArgB,OAAkBE,IACzCiN,GAAAhD,OAAAiW,aAAAC,EAAAngB,IAKAoM,EAAA7G,KAAA0H,GACA8U,EAAAtC,EAAAsC,EAAAE,GAGA,GAAA1E,GAAAnR,EAAAtM,MACAsM,GAAAtC,QAAA,SAAA8G,EAAA5Q,GACA4L,EAAAxP,EAAA4hB,aAAApN,EAAAwF,GAAA,GAAApW,EAAAud,O1BuqJM,SAAUlhB,EAAQD,G2BzvKxBC,EAAAD,QAAAwN,OAAAC,MAAA,SAAAjL,GACA,GAAAkI,MACAqL,EAAAvI,OAAAvG,UAAA+O,cAEA,QAAApS,KAAApB,GACAuT,EAAApV,KAAA6B,EAAAoB,IACA8G,EAAAvB,KAAAvF,EAGA,OAAA8G,K3BywKM,SAAUzK,EAAQD,EAASM,G4B7vKjC,QAAA8iB,GAAA5gB,GACA,IAAAA,GAAA,gBAAAA,GACA,QAGA,IAAAmI,EAAAnI,GAAA,CACA,OAAAoB,GAAA,EAAAqZ,EAAAza,EAAAkB,OAAmCE,EAAAqZ,EAAOrZ,IAC1C,GAAAwf,EAAA5gB,EAAAoB,IACA,QAGA,UAGA,qBAAAuQ,gBAAAC,UAAAD,OAAAC,SAAA5R,IACA,kBAAA8R,cAAA9R,YAAA8R,cACAlB,GAAA5Q,YAAA6Q,OACAC,GAAA9Q,YAAA+Q,MAEA,QAIA,IAAA/Q,EAAAsjB,QAAA,kBAAAtjB,GAAAsjB,QAAA,IAAA9f,UAAAtC,OACA,MAAA0f,GAAA5gB,EAAAsjB,UAAA,EAGA,QAAAnY,KAAAnL,GACA,GAAAgL,OAAAvG,UAAA+O,eAAArV,KAAA6B,EAAAmL,IAAAyV,EAAA5gB,EAAAmL,IACA,QAIA,UAxDA,GAAAhD,GAAArK,EAAA,IAEAgN,EAAAE,OAAAvG,UAAAqG,SACA8F,EAAA,kBAAAC,OACA,mBAAAA,OAAA,6BAAA/F,EAAA3M,KAAA0S,MACAC,EAAA,kBAAAC,OACA,mBAAAA,OAAA,6BAAAjG,EAAA3M,KAAA4S,KAMAtT,GAAAD,QAAAojB,G5B80KM,SAAUnjB,EAAQD,G6Bz1KxBC,EAAAD,QAAA,SAAA+lB,EAAAC,EAAAC,GACA,GAAAC,GAAAH,EAAA5D,UAIA,IAHA6D,KAAA,EACAC,KAAAC,EAEAH,EAAAnT,MAA0B,MAAAmT,GAAAnT,MAAAoT,EAAAC,EAM1B,IAJAD,EAAA,IAAkBA,GAAAE,GAClBD,EAAA,IAAgBA,GAAAC,GAChBD,EAAAC,IAAoBD,EAAAC,GAEpBF,GAAAE,GAAAF,GAAAC,GAAA,IAAAC,EACA,UAAA5R,aAAA,EAKA,QAFA6R,GAAA,GAAAlE,YAAA8D,GACA/R,EAAA,GAAAiO,YAAAgE,EAAAD,GACApiB,EAAAoiB,EAAAI,EAAA,EAA6BxiB,EAAAqiB,EAASriB,IAAAwiB,IACtCpS,EAAAoS,GAAAD,EAAAviB,EAEA,OAAAoQ,GAAAQ,S7Bw2KM,SAAUvU,EAAQD,G8Bj4KxB,QAAAgjB,GAAAqD,EAAA7W,EAAA8W,GAOA,QAAAC,GAAAxU,EAAAiC,GACA,GAAAuS,EAAAF,OAAA,EACA,SAAA3e,OAAA,iCAEA6e,EAAAF,MAGAtU,GACAyU,GAAA,EACAhX,EAAAuC,GAEAvC,EAAA8W,GACS,IAAAC,EAAAF,OAAAG,GACThX,EAAA,KAAAwE,GAnBA,GAAAwS,IAAA,CAIA,OAHAF,MAAAtd,EACAud,EAAAF,QAEA,IAAAA,EAAA7W,IAAA+W,EAoBA,QAAAvd,MA3BA/I,EAAAD,QAAAgjB,G9Bq6KM,SAAU/iB,EAAQD,G+Bh6KxB,QAAAymB,GAAAC,GAMA,IALA,GAGAC,GACAC,EAJAC,KACAC,EAAA,EACApjB,EAAAgjB,EAAAhjB,OAGAojB,EAAApjB,GACAijB,EAAAD,EAAAzb,WAAA6b,KACAH,GAAA,OAAAA,GAAA,OAAAG,EAAApjB,GAEAkjB,EAAAF,EAAAzb,WAAA6b,KACA,cAAAF,GACAC,EAAA1d,OAAA,KAAAwd,IAAA,UAAAC,GAAA,QAIAC,EAAA1d,KAAAwd,GACAG,MAGAD,EAAA1d,KAAAwd,EAGA,OAAAE,GAIA,QAAAE,GAAAhe,GAKA,IAJA,GAEA4d,GAFAjjB,EAAAqF,EAAArF,OACAgC,GAAA,EAEAmhB,EAAA,KACAnhB,EAAAhC,GACAijB,EAAA5d,EAAArD,GACAihB,EAAA,QACAA,GAAA,MACAE,GAAAG,EAAAL,IAAA,eACAA,EAAA,WAAAA,GAEAE,GAAAG,EAAAL,EAEA,OAAAE,GAGA,QAAAI,GAAAC,EAAAtE,GACA,GAAAsE,GAAA,OAAAA,GAAA,OACA,GAAAtE,EACA,KAAAlb,OACA,oBAAAwf,EAAA5Z,SAAA,IAAA6Z,cACA,yBAGA,UAEA,SAIA,QAAAC,GAAAF,EAAAhP,GACA,MAAA8O,GAAAE,GAAAhP,EAAA,QAGA,QAAAmP,GAAAH,EAAAtE,GACA,kBAAAsE,GACA,MAAAF,GAAAE,EAEA,IAAAI,GAAA,EAiBA,OAhBA,gBAAAJ,GACAI,EAAAN,EAAAE,GAAA,UAEA,eAAAA,IACAD,EAAAC,EAAAtE,KACAsE,EAAA,OAEAI,EAAAN,EAAAE,GAAA,WACAI,GAAAF,EAAAF,EAAA,IAEA,eAAAA,KACAI,EAAAN,EAAAE,GAAA,UACAI,GAAAF,EAAAF,EAAA,IACAI,GAAAF,EAAAF,EAAA,IAEAI,GAAAN,EAAA,GAAAE,EAAA,KAIA,QAAAxD,GAAAgD,EAAAzlB,GACAA,OAQA,KAPA,GAKAimB,GALAtE,GAAA,IAAA3hB,EAAA2hB,OAEA2E,EAAAd,EAAAC,GACAhjB,EAAA6jB,EAAA7jB,OACAgC,GAAA,EAEA8hB,EAAA,KACA9hB,EAAAhC,GACAwjB,EAAAK,EAAA7hB,GACA8hB,GAAAH,EAAAH,EAAAtE,EAEA,OAAA4E,GAKA,QAAAC,KACA,GAAAC,GAAAC,EACA,KAAAjgB,OAAA,qBAGA,IAAAkgB,GAAA,IAAAC,EAAAH,EAGA,IAFAA,IAEA,UAAAE,GACA,UAAAA,CAIA,MAAAlgB,OAAA,6BAGA,QAAAogB,GAAAlF,GACA,GAAAmF,GACAC,EACAC,EACAC,EACAhB,CAEA,IAAAQ,EAAAC,EACA,KAAAjgB,OAAA,qBAGA,IAAAggB,GAAAC,EACA,QAQA,IAJAI,EAAA,IAAAF,EAAAH,GACAA,IAGA,QAAAK,GACA,MAAAA,EAIA,cAAAA,GAAA,CAGA,GAFAC,EAAAP,IACAP,GAAA,GAAAa,IAAA,EAAAC,EACAd,GAAA,IACA,MAAAA,EAEA,MAAAxf,OAAA,6BAKA,aAAAqgB,GAAA,CAIA,GAHAC,EAAAP,IACAQ,EAAAR,IACAP,GAAA,GAAAa,IAAA,GAAAC,GAAA,EAAAC,EACAf,GAAA,KACA,MAAAD,GAAAC,EAAAtE,GAAAsE,EAAA,KAEA,MAAAxf,OAAA,6BAKA,aAAAqgB,KACAC,EAAAP,IACAQ,EAAAR,IACAS,EAAAT,IACAP,GAAA,EAAAa,IAAA,GAAAC,GAAA,GACAC,GAAA,EAAAC,EACAhB,GAAA,OAAAA,GAAA,SACA,MAAAA,EAIA,MAAAxf,OAAA,0BAMA,QAAAwc,GAAAsD,EAAAvmB,GACAA,OACA,IAAA2hB,IAAA,IAAA3hB,EAAA2hB,MAEAiF,GAAApB,EAAAe,GACAG,EAAAE,EAAAnkB,OACAgkB,EAAA,CAGA,KAFA,GACAS,GADAZ,MAEAY,EAAAL,EAAAlF,OAAA,GACA2E,EAAApe,KAAAgf,EAEA,OAAApB,GAAAQ;AAxMA,GAyLAM,GACAF,EACAD,EA3LAV,EAAAnZ,OAAAiW,YA2MA7jB,GAAAD,SACAuJ,QAAA,QACA6H,OAAAsS,EACAzK,OAAAiL,I/B66KM,SAAUjkB,EAAQD,IgCtnLxB,WACA,YAMA,QAJAooB,GAAA,mEAGArnB,EAAA,GAAAkhB,YAAA,KACAre,EAAA,EAAiBA,EAAAwkB,EAAA1kB,OAAkBE,IACnC7C,EAAAqnB,EAAAnd,WAAArH,KAGA5D,GAAAoR,OAAA,SAAA2U,GACA,GACAniB,GADAsiB,EAAA,GAAAjE,YAAA8D,GACAnd,EAAAsd,EAAAxiB,OAAA6N,EAAA,EAEA,KAAA3N,EAAA,EAAeA,EAAAgF,EAAShF,GAAA,EACxB2N,GAAA6W,EAAAlC,EAAAtiB,IAAA,GACA2N,GAAA6W,GAAA,EAAAlC,EAAAtiB,KAAA,EAAAsiB,EAAAtiB,EAAA,OACA2N,GAAA6W,GAAA,GAAAlC,EAAAtiB,EAAA,OAAAsiB,EAAAtiB,EAAA,OACA2N,GAAA6W,EAAA,GAAAlC,EAAAtiB,EAAA,GASA,OANAgF,GAAA,MACA2I,IAAA/N,UAAA,EAAA+N,EAAA7N,OAAA,OACKkF,EAAA,QACL2I,IAAA/N,UAAA,EAAA+N,EAAA7N,OAAA,SAGA6N,GAGAvR,EAAAiZ,OAAA,SAAA1H,GACA,GACA3N,GACAykB,EAAAC,EAAAC,EAAAC,EAFAC,EAAA,IAAAlX,EAAA7N,OACAkF,EAAA2I,EAAA7N,OAAA5C,EAAA,CAGA,OAAAyQ,IAAA7N,OAAA,KACA+kB,IACA,MAAAlX,IAAA7N,OAAA,IACA+kB,IAIA,IAAA1C,GAAA,GAAAzR,aAAAmU,GACAvC,EAAA,GAAAjE,YAAA8D,EAEA,KAAAniB,EAAA,EAAeA,EAAAgF,EAAShF,GAAA,EACxBykB,EAAAtnB,EAAAwQ,EAAAtG,WAAArH,IACA0kB,EAAAvnB,EAAAwQ,EAAAtG,WAAArH,EAAA,IACA2kB,EAAAxnB,EAAAwQ,EAAAtG,WAAArH,EAAA,IACA4kB,EAAAznB,EAAAwQ,EAAAtG,WAAArH,EAAA,IAEAsiB,EAAAplB,KAAAunB,GAAA,EAAAC,GAAA,EACApC,EAAAplB,MAAA,GAAAwnB,IAAA,EAAAC,GAAA,EACArC,EAAAplB,MAAA,EAAAynB,IAAA,KAAAC,CAGA,OAAAzC,QhCsoLM,SAAU9lB,EAAQD,GiCnpLxB,QAAA0oB,GAAA7F,GACA,MAAAA,GAAA3V,IAAA,SAAAyb,GACA,GAAAA,EAAAnU,iBAAAF,aAAA,CACA,GAAAhE,GAAAqY,EAAAnU,MAIA,IAAAmU,EAAAxG,aAAA7R,EAAA6R,WAAA,CACA,GAAAyG,GAAA,GAAA3G,YAAA0G,EAAAxG,WACAyG,GAAAC,IAAA,GAAA5G,YAAA3R,EAAAqY,EAAAG,WAAAH,EAAAxG,aACA7R,EAAAsY,EAAApU,OAGA,MAAAlE,GAGA,MAAAqY,KAIA,QAAAI,GAAAlG,EAAAlU,GACAA,OAEA,IAAAqa,GAAA,GAAAC,EAKA,OAJAP,GAAA7F,GAAAnV,QAAA,SAAAwb,GACAF,EAAAG,OAAAD,KAGAva,EAAA,KAAAqa,EAAAI,QAAAza,EAAAzK,MAAA8kB,EAAAI,UAGA,QAAAC,GAAAxG,EAAAlU,GACA,UAAA0E,MAAAqV,EAAA7F,GAAAlU,OA/EA,GAAAsa,GAAA,mBAAAA,KACA,mBAAAK,qCACA,mBAAAC,6BACA,mBAAAC,gCAOAC,EAAA,WACA,IACA,GAAAC,GAAA,GAAArW,OAAA,MACA,YAAAqW,EAAAjE,KACG,MAAAliB,GACH,aASAomB,EAAAF,GAAA,WACA,IACA,GAAAnmB,GAAA,GAAA+P,OAAA,GAAA4O,aAAA,OACA,YAAA3e,EAAAmiB,KACG,MAAAliB,GACH,aAQAqmB,EAAAX,GACAA,EAAAhiB,UAAAkiB,QACAF,EAAAhiB,UAAAmiB,OA2CA,oBAAA/V,QACA0V,EAAA9hB,UAAAoM,KAAApM,UACAoiB,EAAApiB,UAAAoM,KAAApM,WAGAhH,EAAAD,QAAA,WACA,MAAAypB,GACAE,EAAAtW,KAAAgW,EACGO,EACHb,EAEA,WjC+sLM,SAAU9oB,EAAQD,GkCxyLxBA,EAAAoR,OAAA,SAAA5O,GACA,GAAAY,GAAA,EAEA,QAAAQ,KAAApB,GACAA,EAAAwT,eAAApS,KACAR,EAAAM,SAAAN,GAAA,KACAA,GAAAymB,mBAAAjmB,GAAA,IAAAimB,mBAAArnB,EAAAoB,IAIA,OAAAR,IAUApD,EAAAiZ,OAAA,SAAA6Q,GAGA,OAFAC,MACAC,EAAAF,EAAAhd,MAAA,KACAlJ,EAAA,EAAAqZ,EAAA+M,EAAAtmB,OAAmCE,EAAAqZ,EAAOrZ,IAAA,CAC1C,GAAAqmB,GAAAD,EAAApmB,GAAAkJ,MAAA,IACAid,GAAAG,mBAAAD,EAAA,KAAAC,mBAAAD,EAAA,IAEA,MAAAF,KlCwzLM,SAAU9pB,EAAQD,GmC11LxBC,EAAAD,QAAA,SAAA0pB,EAAApmB,GACA,GAAAiP,GAAA,YACAA,GAAAtL,UAAA3D,EAAA2D,UACAyiB,EAAAziB,UAAA,GAAAsL,GACAmX,EAAAziB,UAAAD,YAAA0iB,InCk2LM,SAAUzpB,EAAQD,GoCv2LxB,YAgBA,SAAAoR,GAAA6B,GACA,GAAA0Q,GAAA,EAEA,GACAA,GAAAwG,EAAAlX,EAAAvP,GAAAigB,EACA1Q,EAAA9H,KAAA8G,MAAAgB,EAAAvP,SACGuP,EAAA,EAEH,OAAA0Q,GAUA,QAAA1K,GAAA7V,GACA,GAAAgnB,GAAA,CAEA,KAAAxmB,EAAA,EAAaA,EAAAR,EAAAM,OAAgBE,IAC7BwmB,IAAA1mB,EAAAwJ,EAAA9J,EAAAT,OAAAiB,GAGA,OAAAwmB,GASA,QAAArJ,KACA,GAAAsJ,GAAAjZ,GAAA,GAAAzF,MAEA,OAAA0e,KAAAve,GAAAwe,EAAA,EAAAxe,EAAAue,GACAA,EAAA,IAAAjZ,EAAAkZ,KAMA,IA1DA,GAKAxe,GALAqe,EAAA,mEAAArd,MAAA,IACApJ,EAAA,GACAwJ,KACAod,EAAA,EACA1mB,EAAA,EAsDMA,EAAAF,EAAYE,IAAAsJ,EAAAid,EAAAvmB,KAKlBmd,GAAA3P,SACA2P,EAAA9H,SACAhZ,EAAAD,QAAA+gB,GpC82LM,SAAU9gB,EAAQD,EAASM,IqCj7LjC,SAAAiqB,GA8BA,QAAA3L,MAKA,QAAA4L,KACA,yBAAAhf,WACA,mBAAAvH,eACA,mBAAAsmB,QAUA,QAAAE,GAAAxpB,GAOA,GANA4d,EAAAle,KAAAP,KAAAa,GAEAb,KAAA4B,MAAA5B,KAAA4B,WAIA0Q,EAAA,CAEA,GAAA6X,GAAAC,GACA9X,GAAA6X,EAAAG,OAAAH,EAAAG,WAIAtqB,KAAAsF,MAAAgN,EAAAhP,MAGA,IAAA8H,GAAApL,IACAsS,GAAAvJ,KAAA,SAAA0H,GACArF,EAAAoU,OAAA/O,KAIAzQ,KAAA4B,MAAAoF,EAAAhH,KAAAsF,MAGA,kBAAA2M,mBACAA,iBAAA,0BACA7G,EAAAmf,SAAAnf,EAAAmf,OAAA9S,QAAA+G,KACK,GAzEL,GAAAC,GAAAve,EAAA,IACAgf,EAAAhf,EAAA,GAMAL,GAAAD,QAAAyqB,CAMA,IAOA/X,GAPAkY,EAAA,MACAC,EAAA,MAmEAvL,GAAAmL,EAAA5L,GAMA4L,EAAAxjB,UAAAqV,gBAAA,EAQAmO,EAAAxjB,UAAAoa,QAAA,WACAjhB,KAAAuqB,SACAvqB,KAAAuqB,OAAAG,WAAAC,YAAA3qB,KAAAuqB,QACAvqB,KAAAuqB,OAAA,MAGAvqB,KAAA4qB,OACA5qB,KAAA4qB,KAAAF,WAAAC,YAAA3qB,KAAA4qB,MACA5qB,KAAA4qB,KAAA,KACA5qB,KAAA6qB,OAAA,MAGApM,EAAA5X,UAAAoa,QAAA1gB,KAAAP,OASAqqB,EAAAxjB,UAAA0Y,OAAA,WACA,GAAAnU,GAAApL,KACAuqB,EAAAnmB,SAAA0mB,cAAA,SAEA9qB,MAAAuqB,SACAvqB,KAAAuqB,OAAAG,WAAAC,YAAA3qB,KAAAuqB,QACAvqB,KAAAuqB,OAAA,MAGAA,EAAA3L,OAAA,EACA2L,EAAAtnB,IAAAjD,KAAAY,MACA2pB,EAAA9S,QAAA,SAAAtU,GACAiI,EAAAyQ,QAAA,mBAAA1Y,GAGA,IAAA4nB,GAAA3mB,SAAA4mB,qBAAA,YACAD,GACAA,EAAAL,WAAAO,aAAAV,EAAAQ,IAEA3mB,SAAA8mB,MAAA9mB,SAAA+mB,MAAAC,YAAAb,GAEAvqB,KAAAuqB,QAEA,IAAAc,GAAA,mBAAArnB,YAAA,SAAAxB,KAAAwB,UAAAC,UAEAonB,IACA1jB,WAAA,WACA,GAAAkjB,GAAAzmB,SAAA0mB,cAAA,SACA1mB,UAAA+mB,KAAAC,YAAAP,GACAzmB,SAAA+mB,KAAAR,YAAAE,IACK,MAYLR,EAAAxjB,UAAAuY,QAAA,SAAArQ,EAAAoD,GA0BA,QAAAmZ,KACAC,IACApZ,IAGA,QAAAoZ,KACA,GAAAngB,EAAAyf,OACA,IACAzf,EAAAwf,KAAAD,YAAAvf,EAAAyf,QACO,MAAA1nB,GACPiI,EAAAyQ,QAAA,qCAAA1Y,GAIA,IAEA,GAAAqoB,GAAA,oCAAApgB,EAAAqgB,SAAA,IACAZ,GAAAzmB,SAAA0mB,cAAAU,GACK,MAAAroB,GACL0nB,EAAAzmB,SAAA0mB,cAAA,UACAD,EAAA9gB,KAAAqB,EAAAqgB,SACAZ,EAAA5nB,IAAA,eAGA4nB,EAAAxqB,GAAA+K,EAAAqgB,SAEArgB,EAAAwf,KAAAQ,YAAAP,GACAzf,EAAAyf,SApDA,GAAAzf,GAAApL,IAEA,KAAAA,KAAA4qB,KAAA,CACA,GAGAC,GAHAD,EAAAxmB,SAAA0mB,cAAA,QACAY,EAAAtnB,SAAA0mB,cAAA,YACAzqB,EAAAL,KAAAyrB,SAAA,cAAAzrB,KAAAsF,KAGAslB,GAAAe,UAAA,WACAf,EAAAtmB,MAAAsnB,SAAA,WACAhB,EAAAtmB,MAAAunB,IAAA,UACAjB,EAAAtmB,MAAAwnB,KAAA,UACAlB,EAAAmB,OAAA1rB,EACAuqB,EAAAjM,OAAA,OACAiM,EAAAoB,aAAA,0BACAN,EAAA3hB,KAAA,IACA6gB,EAAAQ,YAAAM,GACAtnB,SAAA+mB,KAAAC,YAAAR,GAEA5qB,KAAA4qB,OACA5qB,KAAA0rB,OAGA1rB,KAAA4qB,KAAAqB,OAAAjsB,KAAAY,MAgCA2qB,IAIAxc,IAAA1L,QAAAonB,EAAA,QACAzqB,KAAA0rB,KAAAnF,MAAAxX,EAAA1L,QAAAmnB,EAAA,MAEA,KACAxqB,KAAA4qB,KAAAsB,SACG,MAAA/oB,IAEHnD,KAAA6qB,OAAArK,YACAxgB,KAAA6qB,OAAA9K,mBAAA,WACA,aAAA3U,EAAAyf,OAAA5V,YACAqW,KAIAtrB,KAAA6qB,OAAAlX,OAAA2X,KrCu7L8B/qB,KAAKX,EAAU,WAAa,MAAOI,WAI3D,SAAUH,EAAQD,EAASM,GsCznMjC,QAAAisB,GAAAtrB,GACA,GAAAoY,GAAApY,KAAAoY,WACAA,KACAjZ,KAAAkc,gBAAA,GAEAlc,KAAA8Z,kBAAAjZ,EAAAiZ,kBACA9Z,KAAAosB,sBAAAC,IAAAxrB,EAAAyZ,UACAta,KAAAyb,UAAA5a,EAAA4a,UACAzb,KAAAosB,wBACAE,EAAAC,GAEApR,EAAA5a,KAAAP,KAAAa,GArDA,GAOAwrB,GAAAE,EAPApR,EAAAjb,EAAA,IACA4B,EAAA5B,EAAA,IACA0Y,EAAA1Y,EAAA,IACAgf,EAAAhf,EAAA,IACAygB,EAAAzgB,EAAA,IACAwB,EAAAxB,EAAA,gCAUA,IANA,mBAAAssB,WACAH,EAAAG,UACC,mBAAAphB,QACDihB,EAAAjhB,KAAAohB,WAAAphB,KAAAqhB,cAGA,mBAAA5oB,QACA,IACA0oB,EAAArsB,EAAA,IACG,MAAAiD,IASH,GAAAmpB,GAAAD,GAAAE,CAMA1sB,GAAAD,QAAAusB,EA2BAjN,EAAAiN,EAAAhR,GAQAgR,EAAAtlB,UAAAkD,KAAA,YAMAoiB,EAAAtlB,UAAAqV,gBAAA,EAQAiQ,EAAAtlB,UAAA+Z,OAAA,WACA,GAAA5gB,KAAA0sB,QAAA,CAKA,GAAA9rB,GAAAZ,KAAAY,MACA6a,EAAAzb,KAAAyb,UACA5a,GACA8X,MAAA3Y,KAAA2Y,MACAmB,kBAAA9Z,KAAA8Z,kBAIAjZ,GAAAmZ,IAAAha,KAAAga,IACAnZ,EAAA0M,IAAAvN,KAAAuN,IACA1M,EAAAoZ,WAAAja,KAAAia,WACApZ,EAAAqZ,KAAAla,KAAAka,KACArZ,EAAAsZ,GAAAna,KAAAma,GACAtZ,EAAAuZ,QAAApa,KAAAoa,QACAvZ,EAAAwZ,mBAAAra,KAAAqa,mBACAra,KAAAya,eACA5Z,EAAA8rB,QAAA3sB,KAAAya,cAEAza,KAAA0a,eACA7Z,EAAA6Z,aAAA1a,KAAA0a,aAGA,KACA1a,KAAA4sB,GACA5sB,KAAAosB,wBAAApsB,KAAAua,cACAkB,EACA,GAAA6Q,GAAA1rB,EAAA6a,GACA,GAAA6Q,GAAA1rB,GACA,GAAA0rB,GAAA1rB,EAAA6a,EAAA5a,GACG,MAAA8Q,GACH,MAAA3R,MAAA2J,KAAA,QAAAgI,GAGA7Q,SAAAd,KAAA4sB,GAAAhT,aACA5Z,KAAAkc,gBAAA,GAGAlc,KAAA4sB,GAAAC,UAAA7sB,KAAA4sB,GAAAC,SAAArd,QACAxP,KAAAkc,gBAAA,EACAlc,KAAA4sB,GAAAhT,WAAA,cAEA5Z,KAAA4sB,GAAAhT,WAAA,cAGA5Z,KAAA8sB,sBASAX,EAAAtlB,UAAAimB,kBAAA,WACA,GAAA1hB,GAAApL,IAEAA,MAAA4sB,GAAA5V,OAAA,WACA5L,EAAAwR,UAEA5c,KAAA4sB,GAAAzU,QAAA,WACA/M,EAAA0Q,WAEA9b,KAAA4sB,GAAAG,UAAA,SAAAC,GACA5hB,EAAAoU,OAAAwN,EAAAje,OAEA/O,KAAA4sB,GAAAnV,QAAA,SAAAtU,GACAiI,EAAAyQ,QAAA,kBAAA1Y,KAWAgpB,EAAAtlB,UAAA+Q,MAAA,SAAAsJ,GA4CA,QAAAyB,KACAvX,EAAAzB,KAAA,SAIAhC,WAAA,WACAyD,EAAAiS,UAAA,EACAjS,EAAAzB,KAAA,UACK,GAnDL,GAAAyB,GAAApL,IACAA,MAAAqd,UAAA,CAKA,QADA0D,GAAAG,EAAA5d,OACAE,EAAA,EAAAqZ,EAAAkE,EAA4Bvd,EAAAqZ,EAAOrZ,KACnC,SAAAmM,GACA7N,EAAAogB,aAAAvS,EAAAvE,EAAA8Q,eAAA,SAAAnN,GACA,IAAA3D,EAAAghB,sBAAA,CAEA,GAAAvrB,KAKA,IAJA8O,EAAApB,UACA1N,EAAAyc,SAAA3N,EAAApB,QAAA+O,UAGAlS,EAAA0O,kBAAA,CACA,GAAAtR,GAAA,gBAAAuG,GAAAgF,OAAAgO,WAAAhT,KAAAzL,MACAkF,GAAA4C,EAAA0O,kBAAAC,YACAlZ,EAAAyc,UAAA,IAQA,IACAlS,EAAAghB,sBAEAhhB,EAAAwhB,GAAAxQ,KAAArN,GAEA3D,EAAAwhB,GAAAxQ,KAAArN,EAAAlO,GAES,MAAAsC,GACTzB,EAAA,2CAGAqf,GAAA4B,OAEKzB,EAAA1d,KAqBL2oB,EAAAtlB,UAAAiV,QAAA,WACAX,EAAAtU,UAAAiV,QAAAvb,KAAAP,OASAmsB,EAAAtlB,UAAAoa,QAAA,WACA,mBAAAjhB,MAAA4sB,IACA5sB,KAAA4sB,GAAAxV,SAUA+U,EAAAtlB,UAAAjG,IAAA,WACA,GAAAgB,GAAA5B,KAAA4B,UACAyf,EAAArhB,KAAA0Y,OAAA,WACAhW,EAAA,EAGA1C,MAAA0C,OAAA,QAAA2e,GAAA,MAAA/V,OAAAtL,KAAA0C,OACA,OAAA2e,GAAA,KAAA/V,OAAAtL,KAAA0C,SACAA,EAAA,IAAA1C,KAAA0C,MAIA1C,KAAAqZ,oBACAzX,EAAA5B,KAAAoZ,gBAAAuH,KAIA3gB,KAAAkc,iBACAta,EAAA0f,IAAA,GAGA1f,EAAAgX,EAAA5H,OAAApP,GAGAA,EAAA0B,SACA1B,EAAA,IAAAA,EAGA,IAAAe,GAAA3C,KAAAyY,SAAA7V,QAAA,SACA,OAAAye,GAAA,OAAA1e,EAAA,IAAA3C,KAAAyY,SAAA,IAAAzY,KAAAyY,UAAA/V,EAAA1C,KAAAmB,KAAAS,GAUAuqB,EAAAtlB,UAAA6lB,MAAA,WACA,SAAAJ,GAAA,gBAAAA,IAAAtsB,KAAA+J,OAAAoiB,EAAAtlB,UAAAkD,QtC+qMM,SAAUlK,EAAQD,KAMlB,SAAUC,EAAQD,GuCz9MxB,GAAAgD,aAEA/C,GAAAD,QAAA,SAAA0K,EAAAlI,GACA,GAAAQ,EAAA,MAAA0H,GAAA1H,QAAAR,EACA,QAAAoB,GAAA,EAAiBA,EAAA8G,EAAAhH,SAAgBE,EACjC,GAAA8G,EAAA9G,KAAApB,EAAA,MAAAoB,EAEA,YvCi+MM,SAAU3D,EAAQD,EAASM,GwCl7MjC,QAAAgC,GAAAnB,EAAA+N,EAAAjO,GACAb,KAAAe,KACAf,KAAA8O,MACA9O,KAAAitB,KAAAjtB,KACAA,KAAAktB,IAAA,EACAltB,KAAAmtB,QACAntB,KAAAotB,iBACAptB,KAAAqtB,cACArtB,KAAAstB,WAAA,EACAttB,KAAAutB,cAAA,EACAvtB,KAAAwtB,SACA3sB,KAAAe,QACA5B,KAAA4B,MAAAf,EAAAe,OAEA5B,KAAAe,GAAAyU,aAAAxV,KAAAyV,OAhEA,GAAA3T,GAAA5B,EAAA,GACAwQ,EAAAxQ,EAAA,IACAutB,EAAAvtB,EAAA,IACAmJ,EAAAnJ,EAAA,IACA0R,EAAA1R,EAAA,IACAwB,EAAAxB,EAAA,8BACA0Y,EAAA1Y,EAAA,IACAwtB,EAAAxtB,EAAA,GAMAL,GAAAD,UAAAsC,CASA,IAAAyrB,IACA1rB,QAAA,EACA2rB,cAAA,EACAC,gBAAA,EACA3Y,WAAA,EACA+C,WAAA,EACA/R,MAAA,EACA2Q,UAAA,EACAiX,kBAAA,EACAC,iBAAA,EACAC,gBAAA,EACArX,aAAA,EACAwG,KAAA,EACAiG,KAAA,GAOAzZ,EAAA+G,EAAA7J,UAAA8C,IA6BA+G,GAAAxO,EAAA2E,WAQA3E,EAAA2E,UAAAonB,UAAA,WACA,IAAAjuB,KAAAqU,KAAA,CAEA,GAAAtT,GAAAf,KAAAe,EACAf,MAAAqU,MACAhL,EAAAtI,EAAA,OAAA6Q,EAAA5R,KAAA,WACAqJ,EAAAtI,EAAA,SAAA6Q,EAAA5R,KAAA,aACAqJ,EAAAtI,EAAA,QAAA6Q,EAAA5R,KAAA,eAUAkC,EAAA2E,UAAA4O,KACAvT,EAAA2E,UAAA5E,QAAA,WACA,MAAAjC,MAAAstB,UAAAttB,MAEAA,KAAAiuB,YACAjuB,KAAAe,GAAA0U,OACA,SAAAzV,KAAAe,GAAAkU,YAAAjV,KAAAgX,SACAhX,KAAA2J,KAAA,cACA3J,OAUAkC,EAAA2E,UAAAuV,KAAA,WACA,GAAApX,GAAAyoB,EAAA7nB,UAGA,OAFAZ,GAAA4G,QAAA,WACA5L,KAAA2J,KAAAhE,MAAA3F,KAAAgF,GACAhF,MAYAkC,EAAA2E,UAAA8C,KAAA,SAAAqjB,GACA,GAAAW,EAAA/X,eAAAoX,GAEA,MADArjB,GAAAhE,MAAA3F,KAAA4F,WACA5F,IAGA,IAAAgF,GAAAyoB,EAAA7nB,WACA+J,GACA7L,MAAAhD,SAAAd,KAAAwtB,MAAAhe,OAAAxP,KAAAwtB,MAAAhe,OAAAke,EAAA1oB,IAAAlD,EAAA6M,aAAA7M,EAAAgP,MACA/B,KAAA/J,EAqBA,OAlBA2K,GAAApB,WACAoB,EAAApB,QAAA+O,UAAAtd,KAAAwtB,QAAA,IAAAxtB,KAAAwtB,MAAAlQ,SAGA,kBAAAtY,KAAA1B,OAAA,KACA5B,EAAA,iCAAA1B,KAAAktB,KACAltB,KAAAmtB,KAAAntB,KAAAktB,KAAAloB,EAAAkpB,MACAve,EAAAtP,GAAAL,KAAAktB,OAGAltB,KAAAstB,UACAttB,KAAA2P,UAEA3P,KAAAqtB,WAAAtkB,KAAA4G,GAGA3P,KAAAwtB,SAEAxtB,MAUAkC,EAAA2E,UAAA8I,OAAA,SAAAA,GACAA,EAAAb,IAAA9O,KAAA8O,IACA9O,KAAAe,GAAA4O,WASAzN,EAAA2E,UAAAmQ,OAAA,WAIA,GAHAtV,EAAA,kCAGA,MAAA1B,KAAA8O,IACA,GAAA9O,KAAA4B,MAAA,CACA,GAAAA,GAAA,gBAAA5B,MAAA4B,MAAAgX,EAAA5H,OAAAhR,KAAA4B,OAAA5B,KAAA4B,KACAF,GAAA,uCAAAE,GACA5B,KAAA2P,QAAmB7L,KAAAhC,EAAA8O,QAAAhP,cAEnB5B,MAAA2P,QAAmB7L,KAAAhC,EAAA8O,WAYnB1O,EAAA2E,UAAAsR,QAAA,SAAAC,GACA1W,EAAA,aAAA0W,GACApY,KAAAstB,WAAA,EACAttB,KAAAutB,cAAA,QACAvtB,MAAAK,GACAL,KAAA2J,KAAA,aAAAyO,IAUAlW,EAAA2E,UAAAsnB,SAAA,SAAAxe,GACA,GAAAvO,GAAAuO,EAAAb,MAAA9O,KAAA8O,IACAsf,EAAAze,EAAA7L,OAAAhC,EAAAwO,OAAA,MAAAX,EAAAb,GAEA,IAAA1N,GAAAgtB,EAEA,OAAAze,EAAA7L,MACA,IAAAhC,GAAA8O,QACA5Q,KAAAquB,WACA,MAEA,KAAAvsB,GAAAgP,MACA9Q,KAAAsuB,QAAA3e,EACA,MAEA,KAAA7N,GAAA6M,aACA3O,KAAAsuB,QAAA3e,EACA,MAEA,KAAA7N,GAAAiP,IACA/Q,KAAAuuB,MAAA5e,EACA,MAEA,KAAA7N,GAAA8M,WACA5O,KAAAuuB,MAAA5e,EACA,MAEA,KAAA7N,GAAA+O,WACA7Q,KAAAwuB,cACA,MAEA,KAAA1sB,GAAAwO,MACAtQ,KAAA2J,KAAA,QAAAgG,EAAAZ,QAYA7M,EAAA2E,UAAAynB,QAAA,SAAA3e,GACA,GAAA3K,GAAA2K,EAAAZ,QACArN,GAAA,oBAAAsD,GAEA,MAAA2K,EAAAtP,KACAqB,EAAA,mCACAsD,EAAA+D,KAAA/I,KAAAyuB,IAAA9e,EAAAtP,MAGAL,KAAAstB,UACA3jB,EAAAhE,MAAA3F,KAAAgF,GAEAhF,KAAAotB,cAAArkB,KAAA/D,IAUA9C,EAAA2E,UAAA4nB,IAAA,SAAApuB,GACA,GAAA+K,GAAApL,KACA0uB,GAAA,CACA,mBAEA,IAAAA,EAAA,CACAA,GAAA,CACA,IAAA1pB,GAAAyoB,EAAA7nB,UACAlE,GAAA,iBAAAsD,GAEAoG,EAAAuE,QACA7L,KAAA4pB,EAAA1oB,GAAAlD,EAAA8M,WAAA9M,EAAAiP,IACA1Q,KACA0O,KAAA/J,OAYA9C,EAAA2E,UAAA0nB,MAAA,SAAA5e,GACA,GAAA8e,GAAAzuB,KAAAmtB,KAAAxd,EAAAtP,GACA,mBAAAouB,IACA/sB,EAAA,yBAAAiO,EAAAtP,GAAAsP,EAAAZ,MACA0f,EAAA9oB,MAAA3F,KAAA2P,EAAAZ,YACA/O,MAAAmtB,KAAAxd,EAAAtP,KAEAqB,EAAA,aAAAiO,EAAAtP,KAUA6B,EAAA2E,UAAAwnB,UAAA,WACAruB,KAAAstB,WAAA,EACAttB,KAAAutB,cAAA,EACAvtB,KAAA2J,KAAA,WACA3J,KAAA2uB,gBASAzsB,EAAA2E,UAAA8nB,aAAA,WACA,GAAAnrB,EACA,KAAAA,EAAA,EAAaA,EAAAxD,KAAAotB,cAAA9pB,OAA+BE,IAC5CmG,EAAAhE,MAAA3F,UAAAotB,cAAA5pB,GAIA,KAFAxD,KAAAotB,iBAEA5pB,EAAA,EAAaA,EAAAxD,KAAAqtB,WAAA/pB,OAA4BE,IACzCxD,KAAA2P,OAAA3P,KAAAqtB,WAAA7pB,GAEAxD,MAAAqtB,eASAnrB,EAAA2E,UAAA2nB,aAAA,WACA9sB,EAAA,yBAAA1B,KAAA8O,KACA9O,KAAAiM,UACAjM,KAAAmY,QAAA,yBAWAjW,EAAA2E,UAAAoF,QAAA,WACA,GAAAjM,KAAAqU,KAAA,CAEA,OAAA7Q,GAAA,EAAmBA,EAAAxD,KAAAqU,KAAA/Q,OAAsBE,IACzCxD,KAAAqU,KAAA7Q,GAAAyI,SAEAjM,MAAAqU,KAAA,KAGArU,KAAAe,GAAAkL,QAAAjM,OAUAkC,EAAA2E,UAAAuQ,MACAlV,EAAA2E,UAAAoR,WAAA,WAaA,MAZAjY,MAAAstB,YACA5rB,EAAA,6BAAA1B,KAAA8O,KACA9O,KAAA2P,QAAiB7L,KAAAhC,EAAA+O,cAIjB7Q,KAAAiM,UAEAjM,KAAAstB,WAEAttB,KAAAmY,QAAA,wBAEAnY,MAWAkC,EAAA2E,UAAAyW,SAAA,SAAAA,GAEA,MADAtd,MAAAwtB,MAAAlQ,WACAtd,MAWAkC,EAAA2E,UAAA2I,OAAA,SAAAA,GAEA,MADAxP,MAAAwtB,MAAAhe,SACAxP,OxCi/MM,SAAUH,EAAQD,GyCn6NxB,QAAA6tB,GAAAmB,EAAAtpB,GACA,GAAAqD,KAEArD,MAAA,CAEA,QAAA9B,GAAA8B,GAAA,EAA4B9B,EAAAorB,EAAAtrB,OAAiBE,IAC7CmF,EAAAnF,EAAA8B,GAAAspB,EAAAprB,EAGA,OAAAmF,GAXA9I,EAAAD,QAAA6tB,GzCw7NM,SAAU5tB,EAAQD,G0Cx6NxB,QAAAyJ,GAAAjH,EAAA4qB,EAAA7a,GAEA,MADA/P,GAAAiH,GAAA2jB,EAAA7a,IAEAlG,QAAA,WACA7J,EAAAqH,eAAAujB,EAAA7a,KAfAtS,EAAAD,QAAAyJ,G1Ci9NM,SAAUxJ,EAAQD,G2Cl9NxB,GAAA4S,WAWA3S,GAAAD,QAAA,SAAAwC,EAAA+P,GAEA,GADA,gBAAAA,OAAA/P,EAAA+P,IACA,kBAAAA,GAAA,SAAA7K,OAAA,6BACA,IAAAtC,GAAAwN,EAAAjS,KAAAqF,UAAA,EACA,mBACA,MAAAuM,GAAAxM,MAAAvD,EAAA4C,EAAAoD,OAAAoK,EAAAjS,KAAAqF,gB3C+9NM,SAAU/F,EAAQD,G4Ch+NxB,QAAAiV,GAAAhU,GACAA,QACAb,KAAAwL,GAAA3K,EAAAiU,KAAA,IACA9U,KAAA+U,IAAAlU,EAAAkU,KAAA,IACA/U,KAAA6uB,OAAAhuB,EAAAguB,QAAA,EACA7uB,KAAAgV,OAAAnU,EAAAmU,OAAA,GAAAnU,EAAAmU,QAAA,EAAAnU,EAAAmU,OAAA,EACAhV,KAAA4W,SAAA,EApBA/W,EAAAD,QAAAiV,EA8BAA,EAAAhO,UAAAyR,SAAA,WACA,GAAA9M,GAAAxL,KAAAwL,GAAAT,KAAA+jB,IAAA9uB,KAAA6uB,OAAA7uB,KAAA4W,WACA,IAAA5W,KAAAgV,OAAA,CACA,GAAA+Z,GAAAhkB,KAAAikB,SACAC,EAAAlkB,KAAA8G,MAAAkd,EAAA/uB,KAAAgV,OAAAxJ,EACAA,GAAA,MAAAT,KAAA8G,MAAA,GAAAkd,IAAAvjB,EAAAyjB,EAAAzjB,EAAAyjB,EAEA,SAAAlkB,KAAA+J,IAAAtJ,EAAAxL,KAAA+U,MASAF,EAAAhO,UAAAqR,MAAA,WACAlY,KAAA4W,SAAA,GASA/B,EAAAhO,UAAAuP,OAAA,SAAAtB,GACA9U,KAAAwL,GAAAsJ,GASAD,EAAAhO,UAAA2P,OAAA,SAAAzB,GACA/U,KAAA+U,OASAF,EAAAhO,UAAAyP,UAAA,SAAAtB,GACAhV,KAAAgV", "file": "socket.io.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"io\"] = factory();\n\telse\n\t\troot[\"io\"] = factory();\n})(this, function() {\nreturn \n\n\n// WEBPACK FOOTER //\n// webpack/universalModuleDefinition", "(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"io\"] = factory();\n\telse\n\t\troot[\"io\"] = factory();\n})(this, function() {\nreturn /******/ (function(modules) { // webpackBootstrap\n/******/ \t// The module cache\n/******/ \tvar installedModules = {};\n/******/\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/\n/******/ \t\t// Check if module is in cache\n/******/ \t\tif(installedModules[moduleId])\n/******/ \t\t\treturn installedModules[moduleId].exports;\n/******/\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = installedModules[moduleId] = {\n/******/ \t\t\texports: {},\n/******/ \t\t\tid: moduleId,\n/******/ \t\t\tloaded: false\n/******/ \t\t};\n/******/\n/******/ \t\t// Execute the module function\n/******/ \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n/******/\n/******/ \t\t// Flag the module as loaded\n/******/ \t\tmodule.loaded = true;\n/******/\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/\n/******/\n/******/ \t// expose the modules object (__webpack_modules__)\n/******/ \t__webpack_require__.m = modules;\n/******/\n/******/ \t// expose the module cache\n/******/ \t__webpack_require__.c = installedModules;\n/******/\n/******/ \t// __webpack_public_path__\n/******/ \t__webpack_require__.p = \"\";\n/******/\n/******/ \t// Load entry module and return exports\n/******/ \treturn __webpack_require__(0);\n/******/ })\n/************************************************************************/\n/******/ ([\n/* 0 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t\n\t/**\n\t * Module dependencies.\n\t */\n\t\n\tvar url = __webpack_require__(1);\n\tvar parser = __webpack_require__(7);\n\tvar Manager = __webpack_require__(15);\n\tvar debug = __webpack_require__(3)('socket.io-client');\n\t\n\t/**\n\t * Module exports.\n\t */\n\t\n\tmodule.exports = exports = lookup;\n\t\n\t/**\n\t * Managers cache.\n\t */\n\t\n\tvar cache = exports.managers = {};\n\t\n\t/**\n\t * Looks up an existing `Manager` for multiplexing.\n\t * If the user summons:\n\t *\n\t *   `io('http://localhost/a');`\n\t *   `io('http://localhost/b');`\n\t *\n\t * We reuse the existing instance based on same scheme/port/host,\n\t * and we initialize sockets for each namespace.\n\t *\n\t * @api public\n\t */\n\t\n\tfunction lookup (uri, opts) {\n\t  if (typeof uri === 'object') {\n\t    opts = uri;\n\t    uri = undefined;\n\t  }\n\t\n\t  opts = opts || {};\n\t\n\t  var parsed = url(uri);\n\t  var source = parsed.source;\n\t  var id = parsed.id;\n\t  var path = parsed.path;\n\t  var sameNamespace = cache[id] && path in cache[id].nsps;\n\t  var newConnection = opts.forceNew || opts['force new connection'] ||\n\t                      false === opts.multiplex || sameNamespace;\n\t\n\t  var io;\n\t\n\t  if (newConnection) {\n\t    debug('ignoring socket cache for %s', source);\n\t    io = Manager(source, opts);\n\t  } else {\n\t    if (!cache[id]) {\n\t      debug('new io instance for %s', source);\n\t      cache[id] = Manager(source, opts);\n\t    }\n\t    io = cache[id];\n\t  }\n\t  if (parsed.query && !opts.query) {\n\t    opts.query = parsed.query;\n\t  }\n\t  return io.socket(parsed.path, opts);\n\t}\n\t\n\t/**\n\t * Protocol version.\n\t *\n\t * @api public\n\t */\n\t\n\texports.protocol = parser.protocol;\n\t\n\t/**\n\t * `connect`.\n\t *\n\t * @param {String} uri\n\t * @api public\n\t */\n\t\n\texports.connect = lookup;\n\t\n\t/**\n\t * Expose constructors for standalone build.\n\t *\n\t * @api public\n\t */\n\t\n\texports.Manager = __webpack_require__(15);\n\texports.Socket = __webpack_require__(39);\n\n\n/***/ }),\n/* 1 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t\n\t/**\n\t * Module dependencies.\n\t */\n\t\n\tvar parseuri = __webpack_require__(2);\n\tvar debug = __webpack_require__(3)('socket.io-client:url');\n\t\n\t/**\n\t * Module exports.\n\t */\n\t\n\tmodule.exports = url;\n\t\n\t/**\n\t * URL parser.\n\t *\n\t * @param {String} url\n\t * @param {Object} An object meant to mimic window.location.\n\t *                 Defaults to window.location.\n\t * @api public\n\t */\n\t\n\tfunction url (uri, loc) {\n\t  var obj = uri;\n\t\n\t  // default to window.location\n\t  loc = loc || (typeof location !== 'undefined' && location);\n\t  if (null == uri) uri = loc.protocol + '//' + loc.host;\n\t\n\t  // relative path support\n\t  if ('string' === typeof uri) {\n\t    if ('/' === uri.charAt(0)) {\n\t      if ('/' === uri.charAt(1)) {\n\t        uri = loc.protocol + uri;\n\t      } else {\n\t        uri = loc.host + uri;\n\t      }\n\t    }\n\t\n\t    if (!/^(https?|wss?):\\/\\//.test(uri)) {\n\t      debug('protocol-less url %s', uri);\n\t      if ('undefined' !== typeof loc) {\n\t        uri = loc.protocol + '//' + uri;\n\t      } else {\n\t        uri = 'https://' + uri;\n\t      }\n\t    }\n\t\n\t    // parse\n\t    debug('parse %s', uri);\n\t    obj = parseuri(uri);\n\t  }\n\t\n\t  // make sure we treat `localhost:80` and `localhost` equally\n\t  if (!obj.port) {\n\t    if (/^(http|ws)$/.test(obj.protocol)) {\n\t      obj.port = '80';\n\t    } else if (/^(http|ws)s$/.test(obj.protocol)) {\n\t      obj.port = '443';\n\t    }\n\t  }\n\t\n\t  obj.path = obj.path || '/';\n\t\n\t  var ipv6 = obj.host.indexOf(':') !== -1;\n\t  var host = ipv6 ? '[' + obj.host + ']' : obj.host;\n\t\n\t  // define unique id\n\t  obj.id = obj.protocol + '://' + host + ':' + obj.port;\n\t  // define href\n\t  obj.href = obj.protocol + '://' + host + (loc && loc.port === obj.port ? '' : (':' + obj.port));\n\t\n\t  return obj;\n\t}\n\n\n/***/ }),\n/* 2 */\n/***/ (function(module, exports) {\n\n\t/**\r\n\t * Parses an URI\r\n\t *\r\n\t * <AUTHOR> Levithan <stevenlevithan.com> (MIT license)\r\n\t * @api private\r\n\t */\r\n\t\r\n\tvar re = /^(?:(?![^:@]+:[^:@\\/]*@)(http|https|ws|wss):\\/\\/)?((?:(([^:@]*)(?::([^:@]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\\/?#]*)(?::(\\d*))?)(((\\/(?:[^?#](?![^?#\\/]*\\.[^?#\\/.]+(?:[?#]|$)))*\\/?)?([^?#\\/]*))(?:\\?([^#]*))?(?:#(.*))?)/;\r\n\t\r\n\tvar parts = [\r\n\t    'source', 'protocol', 'authority', 'userInfo', 'user', 'password', 'host', 'port', 'relative', 'path', 'directory', 'file', 'query', 'anchor'\r\n\t];\r\n\t\r\n\tmodule.exports = function parseuri(str) {\r\n\t    var src = str,\r\n\t        b = str.indexOf('['),\r\n\t        e = str.indexOf(']');\r\n\t\r\n\t    if (b != -1 && e != -1) {\r\n\t        str = str.substring(0, b) + str.substring(b, e).replace(/:/g, ';') + str.substring(e, str.length);\r\n\t    }\r\n\t\r\n\t    var m = re.exec(str || ''),\r\n\t        uri = {},\r\n\t        i = 14;\r\n\t\r\n\t    while (i--) {\r\n\t        uri[parts[i]] = m[i] || '';\r\n\t    }\r\n\t\r\n\t    if (b != -1 && e != -1) {\r\n\t        uri.source = src;\r\n\t        uri.host = uri.host.substring(1, uri.host.length - 1).replace(/;/g, ':');\r\n\t        uri.authority = uri.authority.replace('[', '').replace(']', '').replace(/;/g, ':');\r\n\t        uri.ipv6uri = true;\r\n\t    }\r\n\t\r\n\t    return uri;\r\n\t};\r\n\n\n/***/ }),\n/* 3 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/* WEBPACK VAR INJECTION */(function(process) {'use strict';\n\t\n\tvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; };\n\t\n\t/* eslint-env browser */\n\t\n\t/**\n\t * This is the web browser implementation of `debug()`.\n\t */\n\t\n\texports.log = log;\n\texports.formatArgs = formatArgs;\n\texports.save = save;\n\texports.load = load;\n\texports.useColors = useColors;\n\texports.storage = localstorage();\n\t\n\t/**\n\t * Colors.\n\t */\n\t\n\texports.colors = ['#0000CC', '#0000FF', '#0033CC', '#0033FF', '#0066CC', '#0066FF', '#0099CC', '#0099FF', '#00CC00', '#00CC33', '#00CC66', '#00CC99', '#00CCCC', '#00CCFF', '#3300CC', '#3300FF', '#3333CC', '#3333FF', '#3366CC', '#3366FF', '#3399CC', '#3399FF', '#33CC00', '#33CC33', '#33CC66', '#33CC99', '#33CCCC', '#33CCFF', '#6600CC', '#6600FF', '#6633CC', '#6633FF', '#66CC00', '#66CC33', '#9900CC', '#9900FF', '#9933CC', '#9933FF', '#99CC00', '#99CC33', '#CC0000', '#CC0033', '#CC0066', '#CC0099', '#CC00CC', '#CC00FF', '#CC3300', '#CC3333', '#CC3366', '#CC3399', '#CC33CC', '#CC33FF', '#CC6600', '#CC6633', '#CC9900', '#CC9933', '#CCCC00', '#CCCC33', '#FF0000', '#FF0033', '#FF0066', '#FF0099', '#FF00CC', '#FF00FF', '#FF3300', '#FF3333', '#FF3366', '#FF3399', '#FF33CC', '#FF33FF', '#FF6600', '#FF6633', '#FF9900', '#FF9933', '#FFCC00', '#FFCC33'];\n\t\n\t/**\n\t * Currently only WebKit-based Web Inspectors, Firefox >= v31,\n\t * and the Firebug extension (any Firefox version) are known\n\t * to support \"%c\" CSS customizations.\n\t *\n\t * TODO: add a `localStorage` variable to explicitly enable/disable colors\n\t */\n\t\n\t// eslint-disable-next-line complexity\n\tfunction useColors() {\n\t\t// NB: In an Electron preload script, document will be defined but not fully\n\t\t// initialized. Since we know we're in Chrome, we'll just detect this case\n\t\t// explicitly\n\t\tif (typeof window !== 'undefined' && window.process && (window.process.type === 'renderer' || window.process.__nwjs)) {\n\t\t\treturn true;\n\t\t}\n\t\n\t\t// Internet Explorer and Edge do not support colors.\n\t\tif (typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/(edge|trident)\\/(\\d+)/)) {\n\t\t\treturn false;\n\t\t}\n\t\n\t\t// Is webkit? http://stackoverflow.com/a/16459606/376773\n\t\t// document is undefined in react-native: https://github.com/facebook/react-native/pull/1632\n\t\treturn typeof document !== 'undefined' && document.documentElement && document.documentElement.style && document.documentElement.style.WebkitAppearance ||\n\t\t// Is firebug? http://stackoverflow.com/a/398120/376773\n\t\ttypeof window !== 'undefined' && window.console && (window.console.firebug || window.console.exception && window.console.table) ||\n\t\t// Is firefox >= v31?\n\t\t// https://developer.mozilla.org/en-US/docs/Tools/Web_Console#Styling_messages\n\t\ttypeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/firefox\\/(\\d+)/) && parseInt(RegExp.$1, 10) >= 31 ||\n\t\t// Double check webkit in userAgent just in case we are in a worker\n\t\ttypeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/applewebkit\\/(\\d+)/);\n\t}\n\t\n\t/**\n\t * Colorize log arguments if enabled.\n\t *\n\t * @api public\n\t */\n\t\n\tfunction formatArgs(args) {\n\t\targs[0] = (this.useColors ? '%c' : '') + this.namespace + (this.useColors ? ' %c' : ' ') + args[0] + (this.useColors ? '%c ' : ' ') + '+' + module.exports.humanize(this.diff);\n\t\n\t\tif (!this.useColors) {\n\t\t\treturn;\n\t\t}\n\t\n\t\tvar c = 'color: ' + this.color;\n\t\targs.splice(1, 0, c, 'color: inherit');\n\t\n\t\t// The final \"%c\" is somewhat tricky, because there could be other\n\t\t// arguments passed either before or after the %c, so we need to\n\t\t// figure out the correct index to insert the CSS into\n\t\tvar index = 0;\n\t\tvar lastC = 0;\n\t\targs[0].replace(/%[a-zA-Z%]/g, function (match) {\n\t\t\tif (match === '%%') {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tindex++;\n\t\t\tif (match === '%c') {\n\t\t\t\t// We only are interested in the *last* %c\n\t\t\t\t// (the user may have provided their own)\n\t\t\t\tlastC = index;\n\t\t\t}\n\t\t});\n\t\n\t\targs.splice(lastC, 0, c);\n\t}\n\t\n\t/**\n\t * Invokes `console.log()` when available.\n\t * No-op when `console.log` is not a \"function\".\n\t *\n\t * @api public\n\t */\n\tfunction log() {\n\t\tvar _console;\n\t\n\t\t// This hackery is required for IE8/9, where\n\t\t// the `console.log` function doesn't have 'apply'\n\t\treturn (typeof console === 'undefined' ? 'undefined' : _typeof(console)) === 'object' && console.log && (_console = console).log.apply(_console, arguments);\n\t}\n\t\n\t/**\n\t * Save `namespaces`.\n\t *\n\t * @param {String} namespaces\n\t * @api private\n\t */\n\tfunction save(namespaces) {\n\t\ttry {\n\t\t\tif (namespaces) {\n\t\t\t\texports.storage.setItem('debug', namespaces);\n\t\t\t} else {\n\t\t\t\texports.storage.removeItem('debug');\n\t\t\t}\n\t\t} catch (error) {\n\t\t\t// Swallow\n\t\t\t// XXX (@Qix-) should we be logging these?\n\t\t}\n\t}\n\t\n\t/**\n\t * Load `namespaces`.\n\t *\n\t * @return {String} returns the previously persisted debug modes\n\t * @api private\n\t */\n\tfunction load() {\n\t\tvar r = void 0;\n\t\ttry {\n\t\t\tr = exports.storage.getItem('debug');\n\t\t} catch (error) {}\n\t\t// Swallow\n\t\t// XXX (@Qix-) should we be logging these?\n\t\n\t\n\t\t// If debug isn't set in LS, and we're in Electron, try to load $DEBUG\n\t\tif (!r && typeof process !== 'undefined' && 'env' in process) {\n\t\t\tr = process.env.DEBUG;\n\t\t}\n\t\n\t\treturn r;\n\t}\n\t\n\t/**\n\t * Localstorage attempts to return the localstorage.\n\t *\n\t * This is necessary because safari throws\n\t * when a user disables cookies/localstorage\n\t * and you attempt to access it.\n\t *\n\t * @return {LocalStorage}\n\t * @api private\n\t */\n\t\n\tfunction localstorage() {\n\t\ttry {\n\t\t\t// TVMLKit (Apple TV JS Runtime) does not have a window object, just localStorage in the global context\n\t\t\t// The Browser also has localStorage in the global context.\n\t\t\treturn localStorage;\n\t\t} catch (error) {\n\t\t\t// Swallow\n\t\t\t// XXX (@Qix-) should we be logging these?\n\t\t}\n\t}\n\t\n\tmodule.exports = __webpack_require__(5)(exports);\n\t\n\tvar formatters = module.exports.formatters;\n\t\n\t/**\n\t * Map %j to `JSON.stringify()`, since no Web Inspectors do that by default.\n\t */\n\t\n\tformatters.j = function (v) {\n\t\ttry {\n\t\t\treturn JSON.stringify(v);\n\t\t} catch (error) {\n\t\t\treturn '[UnexpectedJSONParseError]: ' + error.message;\n\t\t}\n\t};\n\t/* WEBPACK VAR INJECTION */}.call(exports, __webpack_require__(4)))\n\n/***/ }),\n/* 4 */\n/***/ (function(module, exports) {\n\n\t// shim for using process in browser\n\tvar process = module.exports = {};\n\t\n\t// cached from whatever global is present so that test runners that stub it\n\t// don't break things.  But we need to wrap it in a try catch in case it is\n\t// wrapped in strict mode code which doesn't define any globals.  It's inside a\n\t// function because try/catches deoptimize in certain engines.\n\t\n\tvar cachedSetTimeout;\n\tvar cachedClearTimeout;\n\t\n\tfunction defaultSetTimout() {\n\t    throw new Error('setTimeout has not been defined');\n\t}\n\tfunction defaultClearTimeout () {\n\t    throw new Error('clearTimeout has not been defined');\n\t}\n\t(function () {\n\t    try {\n\t        if (typeof setTimeout === 'function') {\n\t            cachedSetTimeout = setTimeout;\n\t        } else {\n\t            cachedSetTimeout = defaultSetTimout;\n\t        }\n\t    } catch (e) {\n\t        cachedSetTimeout = defaultSetTimout;\n\t    }\n\t    try {\n\t        if (typeof clearTimeout === 'function') {\n\t            cachedClearTimeout = clearTimeout;\n\t        } else {\n\t            cachedClearTimeout = defaultClearTimeout;\n\t        }\n\t    } catch (e) {\n\t        cachedClearTimeout = defaultClearTimeout;\n\t    }\n\t} ())\n\tfunction runTimeout(fun) {\n\t    if (cachedSetTimeout === setTimeout) {\n\t        //normal enviroments in sane situations\n\t        return setTimeout(fun, 0);\n\t    }\n\t    // if setTimeout wasn't available but was latter defined\n\t    if ((cachedSetTimeout === defaultSetTimout || !cachedSetTimeout) && setTimeout) {\n\t        cachedSetTimeout = setTimeout;\n\t        return setTimeout(fun, 0);\n\t    }\n\t    try {\n\t        // when when somebody has screwed with setTimeout but no I.E. maddness\n\t        return cachedSetTimeout(fun, 0);\n\t    } catch(e){\n\t        try {\n\t            // When we are in I.E. but the script has been evaled so I.E. doesn't trust the global object when called normally\n\t            return cachedSetTimeout.call(null, fun, 0);\n\t        } catch(e){\n\t            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error\n\t            return cachedSetTimeout.call(this, fun, 0);\n\t        }\n\t    }\n\t\n\t\n\t}\n\tfunction runClearTimeout(marker) {\n\t    if (cachedClearTimeout === clearTimeout) {\n\t        //normal enviroments in sane situations\n\t        return clearTimeout(marker);\n\t    }\n\t    // if clearTimeout wasn't available but was latter defined\n\t    if ((cachedClearTimeout === defaultClearTimeout || !cachedClearTimeout) && clearTimeout) {\n\t        cachedClearTimeout = clearTimeout;\n\t        return clearTimeout(marker);\n\t    }\n\t    try {\n\t        // when when somebody has screwed with setTimeout but no I.E. maddness\n\t        return cachedClearTimeout(marker);\n\t    } catch (e){\n\t        try {\n\t            // When we are in I.E. but the script has been evaled so I.E. doesn't  trust the global object when called normally\n\t            return cachedClearTimeout.call(null, marker);\n\t        } catch (e){\n\t            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error.\n\t            // Some versions of I.E. have different rules for clearTimeout vs setTimeout\n\t            return cachedClearTimeout.call(this, marker);\n\t        }\n\t    }\n\t\n\t\n\t\n\t}\n\tvar queue = [];\n\tvar draining = false;\n\tvar currentQueue;\n\tvar queueIndex = -1;\n\t\n\tfunction cleanUpNextTick() {\n\t    if (!draining || !currentQueue) {\n\t        return;\n\t    }\n\t    draining = false;\n\t    if (currentQueue.length) {\n\t        queue = currentQueue.concat(queue);\n\t    } else {\n\t        queueIndex = -1;\n\t    }\n\t    if (queue.length) {\n\t        drainQueue();\n\t    }\n\t}\n\t\n\tfunction drainQueue() {\n\t    if (draining) {\n\t        return;\n\t    }\n\t    var timeout = runTimeout(cleanUpNextTick);\n\t    draining = true;\n\t\n\t    var len = queue.length;\n\t    while(len) {\n\t        currentQueue = queue;\n\t        queue = [];\n\t        while (++queueIndex < len) {\n\t            if (currentQueue) {\n\t                currentQueue[queueIndex].run();\n\t            }\n\t        }\n\t        queueIndex = -1;\n\t        len = queue.length;\n\t    }\n\t    currentQueue = null;\n\t    draining = false;\n\t    runClearTimeout(timeout);\n\t}\n\t\n\tprocess.nextTick = function (fun) {\n\t    var args = new Array(arguments.length - 1);\n\t    if (arguments.length > 1) {\n\t        for (var i = 1; i < arguments.length; i++) {\n\t            args[i - 1] = arguments[i];\n\t        }\n\t    }\n\t    queue.push(new Item(fun, args));\n\t    if (queue.length === 1 && !draining) {\n\t        runTimeout(drainQueue);\n\t    }\n\t};\n\t\n\t// v8 likes predictible objects\n\tfunction Item(fun, array) {\n\t    this.fun = fun;\n\t    this.array = array;\n\t}\n\tItem.prototype.run = function () {\n\t    this.fun.apply(null, this.array);\n\t};\n\tprocess.title = 'browser';\n\tprocess.browser = true;\n\tprocess.env = {};\n\tprocess.argv = [];\n\tprocess.version = ''; // empty string to avoid regexp issues\n\tprocess.versions = {};\n\t\n\tfunction noop() {}\n\t\n\tprocess.on = noop;\n\tprocess.addListener = noop;\n\tprocess.once = noop;\n\tprocess.off = noop;\n\tprocess.removeListener = noop;\n\tprocess.removeAllListeners = noop;\n\tprocess.emit = noop;\n\tprocess.prependListener = noop;\n\tprocess.prependOnceListener = noop;\n\t\n\tprocess.listeners = function (name) { return [] }\n\t\n\tprocess.binding = function (name) {\n\t    throw new Error('process.binding is not supported');\n\t};\n\t\n\tprocess.cwd = function () { return '/' };\n\tprocess.chdir = function (dir) {\n\t    throw new Error('process.chdir is not supported');\n\t};\n\tprocess.umask = function() { return 0; };\n\n\n/***/ }),\n/* 5 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t'use strict';\n\t\n\tfunction _toConsumableArray(arr) { if (Array.isArray(arr)) { for (var i = 0, arr2 = Array(arr.length); i < arr.length; i++) { arr2[i] = arr[i]; } return arr2; } else { return Array.from(arr); } }\n\t\n\t/**\n\t * This is the common logic for both the Node.js and web browser\n\t * implementations of `debug()`.\n\t */\n\t\n\tfunction setup(env) {\n\t\tcreateDebug.debug = createDebug;\n\t\tcreateDebug.default = createDebug;\n\t\tcreateDebug.coerce = coerce;\n\t\tcreateDebug.disable = disable;\n\t\tcreateDebug.enable = enable;\n\t\tcreateDebug.enabled = enabled;\n\t\tcreateDebug.humanize = __webpack_require__(6);\n\t\n\t\tObject.keys(env).forEach(function (key) {\n\t\t\tcreateDebug[key] = env[key];\n\t\t});\n\t\n\t\t/**\n\t * Active `debug` instances.\n\t */\n\t\tcreateDebug.instances = [];\n\t\n\t\t/**\n\t * The currently active debug mode names, and names to skip.\n\t */\n\t\n\t\tcreateDebug.names = [];\n\t\tcreateDebug.skips = [];\n\t\n\t\t/**\n\t * Map of special \"%n\" handling functions, for the debug \"format\" argument.\n\t *\n\t * Valid key names are a single, lower or upper-case letter, i.e. \"n\" and \"N\".\n\t */\n\t\tcreateDebug.formatters = {};\n\t\n\t\t/**\n\t * Selects a color for a debug namespace\n\t * @param {String} namespace The namespace string for the for the debug instance to be colored\n\t * @return {Number|String} An ANSI color code for the given namespace\n\t * @api private\n\t */\n\t\tfunction selectColor(namespace) {\n\t\t\tvar hash = 0;\n\t\n\t\t\tfor (var i = 0; i < namespace.length; i++) {\n\t\t\t\thash = (hash << 5) - hash + namespace.charCodeAt(i);\n\t\t\t\thash |= 0; // Convert to 32bit integer\n\t\t\t}\n\t\n\t\t\treturn createDebug.colors[Math.abs(hash) % createDebug.colors.length];\n\t\t}\n\t\tcreateDebug.selectColor = selectColor;\n\t\n\t\t/**\n\t * Create a debugger with the given `namespace`.\n\t *\n\t * @param {String} namespace\n\t * @return {Function}\n\t * @api public\n\t */\n\t\tfunction createDebug(namespace) {\n\t\t\tvar prevTime = void 0;\n\t\n\t\t\tfunction debug() {\n\t\t\t\tfor (var _len = arguments.length, args = Array(_len), _key = 0; _key < _len; _key++) {\n\t\t\t\t\targs[_key] = arguments[_key];\n\t\t\t\t}\n\t\n\t\t\t\t// Disabled?\n\t\t\t\tif (!debug.enabled) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\n\t\t\t\tvar self = debug;\n\t\n\t\t\t\t// Set `diff` timestamp\n\t\t\t\tvar curr = Number(new Date());\n\t\t\t\tvar ms = curr - (prevTime || curr);\n\t\t\t\tself.diff = ms;\n\t\t\t\tself.prev = prevTime;\n\t\t\t\tself.curr = curr;\n\t\t\t\tprevTime = curr;\n\t\n\t\t\t\targs[0] = createDebug.coerce(args[0]);\n\t\n\t\t\t\tif (typeof args[0] !== 'string') {\n\t\t\t\t\t// Anything else let's inspect with %O\n\t\t\t\t\targs.unshift('%O');\n\t\t\t\t}\n\t\n\t\t\t\t// Apply any `formatters` transformations\n\t\t\t\tvar index = 0;\n\t\t\t\targs[0] = args[0].replace(/%([a-zA-Z%])/g, function (match, format) {\n\t\t\t\t\t// If we encounter an escaped % then don't increase the array index\n\t\t\t\t\tif (match === '%%') {\n\t\t\t\t\t\treturn match;\n\t\t\t\t\t}\n\t\t\t\t\tindex++;\n\t\t\t\t\tvar formatter = createDebug.formatters[format];\n\t\t\t\t\tif (typeof formatter === 'function') {\n\t\t\t\t\t\tvar val = args[index];\n\t\t\t\t\t\tmatch = formatter.call(self, val);\n\t\n\t\t\t\t\t\t// Now we need to remove `args[index]` since it's inlined in the `format`\n\t\t\t\t\t\targs.splice(index, 1);\n\t\t\t\t\t\tindex--;\n\t\t\t\t\t}\n\t\t\t\t\treturn match;\n\t\t\t\t});\n\t\n\t\t\t\t// Apply env-specific formatting (colors, etc.)\n\t\t\t\tcreateDebug.formatArgs.call(self, args);\n\t\n\t\t\t\tvar logFn = self.log || createDebug.log;\n\t\t\t\tlogFn.apply(self, args);\n\t\t\t}\n\t\n\t\t\tdebug.namespace = namespace;\n\t\t\tdebug.enabled = createDebug.enabled(namespace);\n\t\t\tdebug.useColors = createDebug.useColors();\n\t\t\tdebug.color = selectColor(namespace);\n\t\t\tdebug.destroy = destroy;\n\t\t\tdebug.extend = extend;\n\t\t\t// Debug.formatArgs = formatArgs;\n\t\t\t// debug.rawLog = rawLog;\n\t\n\t\t\t// env-specific initialization logic for debug instances\n\t\t\tif (typeof createDebug.init === 'function') {\n\t\t\t\tcreateDebug.init(debug);\n\t\t\t}\n\t\n\t\t\tcreateDebug.instances.push(debug);\n\t\n\t\t\treturn debug;\n\t\t}\n\t\n\t\tfunction destroy() {\n\t\t\tvar index = createDebug.instances.indexOf(this);\n\t\t\tif (index !== -1) {\n\t\t\t\tcreateDebug.instances.splice(index, 1);\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\treturn false;\n\t\t}\n\t\n\t\tfunction extend(namespace, delimiter) {\n\t\t\tvar newDebug = createDebug(this.namespace + (typeof delimiter === 'undefined' ? ':' : delimiter) + namespace);\n\t\t\tnewDebug.log = this.log;\n\t\t\treturn newDebug;\n\t\t}\n\t\n\t\t/**\n\t * Enables a debug mode by namespaces. This can include modes\n\t * separated by a colon and wildcards.\n\t *\n\t * @param {String} namespaces\n\t * @api public\n\t */\n\t\tfunction enable(namespaces) {\n\t\t\tcreateDebug.save(namespaces);\n\t\n\t\t\tcreateDebug.names = [];\n\t\t\tcreateDebug.skips = [];\n\t\n\t\t\tvar i = void 0;\n\t\t\tvar split = (typeof namespaces === 'string' ? namespaces : '').split(/[\\s,]+/);\n\t\t\tvar len = split.length;\n\t\n\t\t\tfor (i = 0; i < len; i++) {\n\t\t\t\tif (!split[i]) {\n\t\t\t\t\t// ignore empty strings\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\n\t\t\t\tnamespaces = split[i].replace(/\\*/g, '.*?');\n\t\n\t\t\t\tif (namespaces[0] === '-') {\n\t\t\t\t\tcreateDebug.skips.push(new RegExp('^' + namespaces.substr(1) + '$'));\n\t\t\t\t} else {\n\t\t\t\t\tcreateDebug.names.push(new RegExp('^' + namespaces + '$'));\n\t\t\t\t}\n\t\t\t}\n\t\n\t\t\tfor (i = 0; i < createDebug.instances.length; i++) {\n\t\t\t\tvar instance = createDebug.instances[i];\n\t\t\t\tinstance.enabled = createDebug.enabled(instance.namespace);\n\t\t\t}\n\t\t}\n\t\n\t\t/**\n\t * Disable debug output.\n\t *\n\t * @return {String} namespaces\n\t * @api public\n\t */\n\t\tfunction disable() {\n\t\t\tvar namespaces = [].concat(_toConsumableArray(createDebug.names.map(toNamespace)), _toConsumableArray(createDebug.skips.map(toNamespace).map(function (namespace) {\n\t\t\t\treturn '-' + namespace;\n\t\t\t}))).join(',');\n\t\t\tcreateDebug.enable('');\n\t\t\treturn namespaces;\n\t\t}\n\t\n\t\t/**\n\t * Returns true if the given mode name is enabled, false otherwise.\n\t *\n\t * @param {String} name\n\t * @return {Boolean}\n\t * @api public\n\t */\n\t\tfunction enabled(name) {\n\t\t\tif (name[name.length - 1] === '*') {\n\t\t\t\treturn true;\n\t\t\t}\n\t\n\t\t\tvar i = void 0;\n\t\t\tvar len = void 0;\n\t\n\t\t\tfor (i = 0, len = createDebug.skips.length; i < len; i++) {\n\t\t\t\tif (createDebug.skips[i].test(name)) {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t}\n\t\n\t\t\tfor (i = 0, len = createDebug.names.length; i < len; i++) {\n\t\t\t\tif (createDebug.names[i].test(name)) {\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t}\n\t\n\t\t\treturn false;\n\t\t}\n\t\n\t\t/**\n\t * Convert regexp to namespace\n\t *\n\t * @param {RegExp} regxep\n\t * @return {String} namespace\n\t * @api private\n\t */\n\t\tfunction toNamespace(regexp) {\n\t\t\treturn regexp.toString().substring(2, regexp.toString().length - 2).replace(/\\.\\*\\?$/, '*');\n\t\t}\n\t\n\t\t/**\n\t * Coerce `val`.\n\t *\n\t * @param {Mixed} val\n\t * @return {Mixed}\n\t * @api private\n\t */\n\t\tfunction coerce(val) {\n\t\t\tif (val instanceof Error) {\n\t\t\t\treturn val.stack || val.message;\n\t\t\t}\n\t\t\treturn val;\n\t\t}\n\t\n\t\tcreateDebug.enable(createDebug.load());\n\t\n\t\treturn createDebug;\n\t}\n\t\n\tmodule.exports = setup;\n\n/***/ }),\n/* 6 */\n/***/ (function(module, exports) {\n\n\t/**\n\t * Helpers.\n\t */\n\t\n\tvar s = 1000;\n\tvar m = s * 60;\n\tvar h = m * 60;\n\tvar d = h * 24;\n\tvar w = d * 7;\n\tvar y = d * 365.25;\n\t\n\t/**\n\t * Parse or format the given `val`.\n\t *\n\t * Options:\n\t *\n\t *  - `long` verbose formatting [false]\n\t *\n\t * @param {String|Number} val\n\t * @param {Object} [options]\n\t * @throws {Error} throw an error if val is not a non-empty string or a number\n\t * @return {String|Number}\n\t * @api public\n\t */\n\t\n\tmodule.exports = function(val, options) {\n\t  options = options || {};\n\t  var type = typeof val;\n\t  if (type === 'string' && val.length > 0) {\n\t    return parse(val);\n\t  } else if (type === 'number' && isFinite(val)) {\n\t    return options.long ? fmtLong(val) : fmtShort(val);\n\t  }\n\t  throw new Error(\n\t    'val is not a non-empty string or a valid number. val=' +\n\t      JSON.stringify(val)\n\t  );\n\t};\n\t\n\t/**\n\t * Parse the given `str` and return milliseconds.\n\t *\n\t * @param {String} str\n\t * @return {Number}\n\t * @api private\n\t */\n\t\n\tfunction parse(str) {\n\t  str = String(str);\n\t  if (str.length > 100) {\n\t    return;\n\t  }\n\t  var match = /^(-?(?:\\d+)?\\.?\\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(\n\t    str\n\t  );\n\t  if (!match) {\n\t    return;\n\t  }\n\t  var n = parseFloat(match[1]);\n\t  var type = (match[2] || 'ms').toLowerCase();\n\t  switch (type) {\n\t    case 'years':\n\t    case 'year':\n\t    case 'yrs':\n\t    case 'yr':\n\t    case 'y':\n\t      return n * y;\n\t    case 'weeks':\n\t    case 'week':\n\t    case 'w':\n\t      return n * w;\n\t    case 'days':\n\t    case 'day':\n\t    case 'd':\n\t      return n * d;\n\t    case 'hours':\n\t    case 'hour':\n\t    case 'hrs':\n\t    case 'hr':\n\t    case 'h':\n\t      return n * h;\n\t    case 'minutes':\n\t    case 'minute':\n\t    case 'mins':\n\t    case 'min':\n\t    case 'm':\n\t      return n * m;\n\t    case 'seconds':\n\t    case 'second':\n\t    case 'secs':\n\t    case 'sec':\n\t    case 's':\n\t      return n * s;\n\t    case 'milliseconds':\n\t    case 'millisecond':\n\t    case 'msecs':\n\t    case 'msec':\n\t    case 'ms':\n\t      return n;\n\t    default:\n\t      return undefined;\n\t  }\n\t}\n\t\n\t/**\n\t * Short format for `ms`.\n\t *\n\t * @param {Number} ms\n\t * @return {String}\n\t * @api private\n\t */\n\t\n\tfunction fmtShort(ms) {\n\t  var msAbs = Math.abs(ms);\n\t  if (msAbs >= d) {\n\t    return Math.round(ms / d) + 'd';\n\t  }\n\t  if (msAbs >= h) {\n\t    return Math.round(ms / h) + 'h';\n\t  }\n\t  if (msAbs >= m) {\n\t    return Math.round(ms / m) + 'm';\n\t  }\n\t  if (msAbs >= s) {\n\t    return Math.round(ms / s) + 's';\n\t  }\n\t  return ms + 'ms';\n\t}\n\t\n\t/**\n\t * Long format for `ms`.\n\t *\n\t * @param {Number} ms\n\t * @return {String}\n\t * @api private\n\t */\n\t\n\tfunction fmtLong(ms) {\n\t  var msAbs = Math.abs(ms);\n\t  if (msAbs >= d) {\n\t    return plural(ms, msAbs, d, 'day');\n\t  }\n\t  if (msAbs >= h) {\n\t    return plural(ms, msAbs, h, 'hour');\n\t  }\n\t  if (msAbs >= m) {\n\t    return plural(ms, msAbs, m, 'minute');\n\t  }\n\t  if (msAbs >= s) {\n\t    return plural(ms, msAbs, s, 'second');\n\t  }\n\t  return ms + ' ms';\n\t}\n\t\n\t/**\n\t * Pluralization helper.\n\t */\n\t\n\tfunction plural(ms, msAbs, n, name) {\n\t  var isPlural = msAbs >= n * 1.5;\n\t  return Math.round(ms / n) + ' ' + name + (isPlural ? 's' : '');\n\t}\n\n\n/***/ }),\n/* 7 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t\n\t/**\n\t * Module dependencies.\n\t */\n\t\n\tvar debug = __webpack_require__(8)('socket.io-parser');\n\tvar Emitter = __webpack_require__(11);\n\tvar binary = __webpack_require__(12);\n\tvar isArray = __webpack_require__(13);\n\tvar isBuf = __webpack_require__(14);\n\t\n\t/**\n\t * Protocol version.\n\t *\n\t * @api public\n\t */\n\t\n\texports.protocol = 4;\n\t\n\t/**\n\t * Packet types.\n\t *\n\t * @api public\n\t */\n\t\n\texports.types = [\n\t  'CONNECT',\n\t  'DISCONNECT',\n\t  'EVENT',\n\t  'ACK',\n\t  'ERROR',\n\t  'BINARY_EVENT',\n\t  'BINARY_ACK'\n\t];\n\t\n\t/**\n\t * Packet type `connect`.\n\t *\n\t * @api public\n\t */\n\t\n\texports.CONNECT = 0;\n\t\n\t/**\n\t * Packet type `disconnect`.\n\t *\n\t * @api public\n\t */\n\t\n\texports.DISCONNECT = 1;\n\t\n\t/**\n\t * Packet type `event`.\n\t *\n\t * @api public\n\t */\n\t\n\texports.EVENT = 2;\n\t\n\t/**\n\t * Packet type `ack`.\n\t *\n\t * @api public\n\t */\n\t\n\texports.ACK = 3;\n\t\n\t/**\n\t * Packet type `error`.\n\t *\n\t * @api public\n\t */\n\t\n\texports.ERROR = 4;\n\t\n\t/**\n\t * Packet type 'binary event'\n\t *\n\t * @api public\n\t */\n\t\n\texports.BINARY_EVENT = 5;\n\t\n\t/**\n\t * Packet type `binary ack`. For acks with binary arguments.\n\t *\n\t * @api public\n\t */\n\t\n\texports.BINARY_ACK = 6;\n\t\n\t/**\n\t * Encoder constructor.\n\t *\n\t * @api public\n\t */\n\t\n\texports.Encoder = Encoder;\n\t\n\t/**\n\t * Decoder constructor.\n\t *\n\t * @api public\n\t */\n\t\n\texports.Decoder = Decoder;\n\t\n\t/**\n\t * A socket.io Encoder instance\n\t *\n\t * @api public\n\t */\n\t\n\tfunction Encoder() {}\n\t\n\tvar ERROR_PACKET = exports.ERROR + '\"encode error\"';\n\t\n\t/**\n\t * Encode a packet as a single string if non-binary, or as a\n\t * buffer sequence, depending on packet type.\n\t *\n\t * @param {Object} obj - packet object\n\t * @param {Function} callback - function to handle encodings (likely engine.write)\n\t * @return Calls callback with Array of encodings\n\t * @api public\n\t */\n\t\n\tEncoder.prototype.encode = function(obj, callback){\n\t  debug('encoding packet %j', obj);\n\t\n\t  if (exports.BINARY_EVENT === obj.type || exports.BINARY_ACK === obj.type) {\n\t    encodeAsBinary(obj, callback);\n\t  } else {\n\t    var encoding = encodeAsString(obj);\n\t    callback([encoding]);\n\t  }\n\t};\n\t\n\t/**\n\t * Encode packet as string.\n\t *\n\t * @param {Object} packet\n\t * @return {String} encoded\n\t * @api private\n\t */\n\t\n\tfunction encodeAsString(obj) {\n\t\n\t  // first is type\n\t  var str = '' + obj.type;\n\t\n\t  // attachments if we have them\n\t  if (exports.BINARY_EVENT === obj.type || exports.BINARY_ACK === obj.type) {\n\t    str += obj.attachments + '-';\n\t  }\n\t\n\t  // if we have a namespace other than `/`\n\t  // we append it followed by a comma `,`\n\t  if (obj.nsp && '/' !== obj.nsp) {\n\t    str += obj.nsp + ',';\n\t  }\n\t\n\t  // immediately followed by the id\n\t  if (null != obj.id) {\n\t    str += obj.id;\n\t  }\n\t\n\t  // json data\n\t  if (null != obj.data) {\n\t    var payload = tryStringify(obj.data);\n\t    if (payload !== false) {\n\t      str += payload;\n\t    } else {\n\t      return ERROR_PACKET;\n\t    }\n\t  }\n\t\n\t  debug('encoded %j as %s', obj, str);\n\t  return str;\n\t}\n\t\n\tfunction tryStringify(str) {\n\t  try {\n\t    return JSON.stringify(str);\n\t  } catch(e){\n\t    return false;\n\t  }\n\t}\n\t\n\t/**\n\t * Encode packet as 'buffer sequence' by removing blobs, and\n\t * deconstructing packet into object with placeholders and\n\t * a list of buffers.\n\t *\n\t * @param {Object} packet\n\t * @return {Buffer} encoded\n\t * @api private\n\t */\n\t\n\tfunction encodeAsBinary(obj, callback) {\n\t\n\t  function writeEncoding(bloblessData) {\n\t    var deconstruction = binary.deconstructPacket(bloblessData);\n\t    var pack = encodeAsString(deconstruction.packet);\n\t    var buffers = deconstruction.buffers;\n\t\n\t    buffers.unshift(pack); // add packet info to beginning of data list\n\t    callback(buffers); // write all the buffers\n\t  }\n\t\n\t  binary.removeBlobs(obj, writeEncoding);\n\t}\n\t\n\t/**\n\t * A socket.io Decoder instance\n\t *\n\t * @return {Object} decoder\n\t * @api public\n\t */\n\t\n\tfunction Decoder() {\n\t  this.reconstructor = null;\n\t}\n\t\n\t/**\n\t * Mix in `Emitter` with Decoder.\n\t */\n\t\n\tEmitter(Decoder.prototype);\n\t\n\t/**\n\t * Decodes an encoded packet string into packet JSON.\n\t *\n\t * @param {String} obj - encoded packet\n\t * @return {Object} packet\n\t * @api public\n\t */\n\t\n\tDecoder.prototype.add = function(obj) {\n\t  var packet;\n\t  if (typeof obj === 'string') {\n\t    packet = decodeString(obj);\n\t    if (exports.BINARY_EVENT === packet.type || exports.BINARY_ACK === packet.type) { // binary packet's json\n\t      this.reconstructor = new BinaryReconstructor(packet);\n\t\n\t      // no attachments, labeled binary but no binary data to follow\n\t      if (this.reconstructor.reconPack.attachments === 0) {\n\t        this.emit('decoded', packet);\n\t      }\n\t    } else { // non-binary full packet\n\t      this.emit('decoded', packet);\n\t    }\n\t  } else if (isBuf(obj) || obj.base64) { // raw binary data\n\t    if (!this.reconstructor) {\n\t      throw new Error('got binary data when not reconstructing a packet');\n\t    } else {\n\t      packet = this.reconstructor.takeBinaryData(obj);\n\t      if (packet) { // received final buffer\n\t        this.reconstructor = null;\n\t        this.emit('decoded', packet);\n\t      }\n\t    }\n\t  } else {\n\t    throw new Error('Unknown type: ' + obj);\n\t  }\n\t};\n\t\n\t/**\n\t * Decode a packet String (JSON data)\n\t *\n\t * @param {String} str\n\t * @return {Object} packet\n\t * @api private\n\t */\n\t\n\tfunction decodeString(str) {\n\t  var i = 0;\n\t  // look up type\n\t  var p = {\n\t    type: Number(str.charAt(0))\n\t  };\n\t\n\t  if (null == exports.types[p.type]) {\n\t    return error('unknown packet type ' + p.type);\n\t  }\n\t\n\t  // look up attachments if type binary\n\t  if (exports.BINARY_EVENT === p.type || exports.BINARY_ACK === p.type) {\n\t    var buf = '';\n\t    while (str.charAt(++i) !== '-') {\n\t      buf += str.charAt(i);\n\t      if (i == str.length) break;\n\t    }\n\t    if (buf != Number(buf) || str.charAt(i) !== '-') {\n\t      throw new Error('Illegal attachments');\n\t    }\n\t    p.attachments = Number(buf);\n\t  }\n\t\n\t  // look up namespace (if any)\n\t  if ('/' === str.charAt(i + 1)) {\n\t    p.nsp = '';\n\t    while (++i) {\n\t      var c = str.charAt(i);\n\t      if (',' === c) break;\n\t      p.nsp += c;\n\t      if (i === str.length) break;\n\t    }\n\t  } else {\n\t    p.nsp = '/';\n\t  }\n\t\n\t  // look up id\n\t  var next = str.charAt(i + 1);\n\t  if ('' !== next && Number(next) == next) {\n\t    p.id = '';\n\t    while (++i) {\n\t      var c = str.charAt(i);\n\t      if (null == c || Number(c) != c) {\n\t        --i;\n\t        break;\n\t      }\n\t      p.id += str.charAt(i);\n\t      if (i === str.length) break;\n\t    }\n\t    p.id = Number(p.id);\n\t  }\n\t\n\t  // look up json data\n\t  if (str.charAt(++i)) {\n\t    var payload = tryParse(str.substr(i));\n\t    var isPayloadValid = payload !== false && (p.type === exports.ERROR || isArray(payload));\n\t    if (isPayloadValid) {\n\t      p.data = payload;\n\t    } else {\n\t      return error('invalid payload');\n\t    }\n\t  }\n\t\n\t  debug('decoded %s as %j', str, p);\n\t  return p;\n\t}\n\t\n\tfunction tryParse(str) {\n\t  try {\n\t    return JSON.parse(str);\n\t  } catch(e){\n\t    return false;\n\t  }\n\t}\n\t\n\t/**\n\t * Deallocates a parser's resources\n\t *\n\t * @api public\n\t */\n\t\n\tDecoder.prototype.destroy = function() {\n\t  if (this.reconstructor) {\n\t    this.reconstructor.finishedReconstruction();\n\t  }\n\t};\n\t\n\t/**\n\t * A manager of a binary event's 'buffer sequence'. Should\n\t * be constructed whenever a packet of type BINARY_EVENT is\n\t * decoded.\n\t *\n\t * @param {Object} packet\n\t * @return {BinaryReconstructor} initialized reconstructor\n\t * @api private\n\t */\n\t\n\tfunction BinaryReconstructor(packet) {\n\t  this.reconPack = packet;\n\t  this.buffers = [];\n\t}\n\t\n\t/**\n\t * Method to be called when binary data received from connection\n\t * after a BINARY_EVENT packet.\n\t *\n\t * @param {Buffer | ArrayBuffer} binData - the raw binary data received\n\t * @return {null | Object} returns null if more binary data is expected or\n\t *   a reconstructed packet object if all buffers have been received.\n\t * @api private\n\t */\n\t\n\tBinaryReconstructor.prototype.takeBinaryData = function(binData) {\n\t  this.buffers.push(binData);\n\t  if (this.buffers.length === this.reconPack.attachments) { // done with buffer list\n\t    var packet = binary.reconstructPacket(this.reconPack, this.buffers);\n\t    this.finishedReconstruction();\n\t    return packet;\n\t  }\n\t  return null;\n\t};\n\t\n\t/**\n\t * Cleans up binary packet reconstruction variables.\n\t *\n\t * @api private\n\t */\n\t\n\tBinaryReconstructor.prototype.finishedReconstruction = function() {\n\t  this.reconPack = null;\n\t  this.buffers = [];\n\t};\n\t\n\tfunction error(msg) {\n\t  return {\n\t    type: exports.ERROR,\n\t    data: 'parser error: ' + msg\n\t  };\n\t}\n\n\n/***/ }),\n/* 8 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/* WEBPACK VAR INJECTION */(function(process) {'use strict';\n\t\n\tvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; };\n\t\n\t/**\n\t * This is the web browser implementation of `debug()`.\n\t *\n\t * Expose `debug()` as the module.\n\t */\n\t\n\texports = module.exports = __webpack_require__(9);\n\texports.log = log;\n\texports.formatArgs = formatArgs;\n\texports.save = save;\n\texports.load = load;\n\texports.useColors = useColors;\n\texports.storage = 'undefined' != typeof chrome && 'undefined' != typeof chrome.storage ? chrome.storage.local : localstorage();\n\t\n\t/**\n\t * Colors.\n\t */\n\t\n\texports.colors = ['#0000CC', '#0000FF', '#0033CC', '#0033FF', '#0066CC', '#0066FF', '#0099CC', '#0099FF', '#00CC00', '#00CC33', '#00CC66', '#00CC99', '#00CCCC', '#00CCFF', '#3300CC', '#3300FF', '#3333CC', '#3333FF', '#3366CC', '#3366FF', '#3399CC', '#3399FF', '#33CC00', '#33CC33', '#33CC66', '#33CC99', '#33CCCC', '#33CCFF', '#6600CC', '#6600FF', '#6633CC', '#6633FF', '#66CC00', '#66CC33', '#9900CC', '#9900FF', '#9933CC', '#9933FF', '#99CC00', '#99CC33', '#CC0000', '#CC0033', '#CC0066', '#CC0099', '#CC00CC', '#CC00FF', '#CC3300', '#CC3333', '#CC3366', '#CC3399', '#CC33CC', '#CC33FF', '#CC6600', '#CC6633', '#CC9900', '#CC9933', '#CCCC00', '#CCCC33', '#FF0000', '#FF0033', '#FF0066', '#FF0099', '#FF00CC', '#FF00FF', '#FF3300', '#FF3333', '#FF3366', '#FF3399', '#FF33CC', '#FF33FF', '#FF6600', '#FF6633', '#FF9900', '#FF9933', '#FFCC00', '#FFCC33'];\n\t\n\t/**\n\t * Currently only WebKit-based Web Inspectors, Firefox >= v31,\n\t * and the Firebug extension (any Firefox version) are known\n\t * to support \"%c\" CSS customizations.\n\t *\n\t * TODO: add a `localStorage` variable to explicitly enable/disable colors\n\t */\n\t\n\tfunction useColors() {\n\t  // NB: In an Electron preload script, document will be defined but not fully\n\t  // initialized. Since we know we're in Chrome, we'll just detect this case\n\t  // explicitly\n\t  if (typeof window !== 'undefined' && window.process && window.process.type === 'renderer') {\n\t    return true;\n\t  }\n\t\n\t  // Internet Explorer and Edge do not support colors.\n\t  if (typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/(edge|trident)\\/(\\d+)/)) {\n\t    return false;\n\t  }\n\t\n\t  // is webkit? http://stackoverflow.com/a/16459606/376773\n\t  // document is undefined in react-native: https://github.com/facebook/react-native/pull/1632\n\t  return typeof document !== 'undefined' && document.documentElement && document.documentElement.style && document.documentElement.style.WebkitAppearance ||\n\t  // is firebug? http://stackoverflow.com/a/398120/376773\n\t  typeof window !== 'undefined' && window.console && (window.console.firebug || window.console.exception && window.console.table) ||\n\t  // is firefox >= v31?\n\t  // https://developer.mozilla.org/en-US/docs/Tools/Web_Console#Styling_messages\n\t  typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/firefox\\/(\\d+)/) && parseInt(RegExp.$1, 10) >= 31 ||\n\t  // double check webkit in userAgent just in case we are in a worker\n\t  typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/applewebkit\\/(\\d+)/);\n\t}\n\t\n\t/**\n\t * Map %j to `JSON.stringify()`, since no Web Inspectors do that by default.\n\t */\n\t\n\texports.formatters.j = function (v) {\n\t  try {\n\t    return JSON.stringify(v);\n\t  } catch (err) {\n\t    return '[UnexpectedJSONParseError]: ' + err.message;\n\t  }\n\t};\n\t\n\t/**\n\t * Colorize log arguments if enabled.\n\t *\n\t * @api public\n\t */\n\t\n\tfunction formatArgs(args) {\n\t  var useColors = this.useColors;\n\t\n\t  args[0] = (useColors ? '%c' : '') + this.namespace + (useColors ? ' %c' : ' ') + args[0] + (useColors ? '%c ' : ' ') + '+' + exports.humanize(this.diff);\n\t\n\t  if (!useColors) return;\n\t\n\t  var c = 'color: ' + this.color;\n\t  args.splice(1, 0, c, 'color: inherit');\n\t\n\t  // the final \"%c\" is somewhat tricky, because there could be other\n\t  // arguments passed either before or after the %c, so we need to\n\t  // figure out the correct index to insert the CSS into\n\t  var index = 0;\n\t  var lastC = 0;\n\t  args[0].replace(/%[a-zA-Z%]/g, function (match) {\n\t    if ('%%' === match) return;\n\t    index++;\n\t    if ('%c' === match) {\n\t      // we only are interested in the *last* %c\n\t      // (the user may have provided their own)\n\t      lastC = index;\n\t    }\n\t  });\n\t\n\t  args.splice(lastC, 0, c);\n\t}\n\t\n\t/**\n\t * Invokes `console.log()` when available.\n\t * No-op when `console.log` is not a \"function\".\n\t *\n\t * @api public\n\t */\n\t\n\tfunction log() {\n\t  // this hackery is required for IE8/9, where\n\t  // the `console.log` function doesn't have 'apply'\n\t  return 'object' === (typeof console === 'undefined' ? 'undefined' : _typeof(console)) && console.log && Function.prototype.apply.call(console.log, console, arguments);\n\t}\n\t\n\t/**\n\t * Save `namespaces`.\n\t *\n\t * @param {String} namespaces\n\t * @api private\n\t */\n\t\n\tfunction save(namespaces) {\n\t  try {\n\t    if (null == namespaces) {\n\t      exports.storage.removeItem('debug');\n\t    } else {\n\t      exports.storage.debug = namespaces;\n\t    }\n\t  } catch (e) {}\n\t}\n\t\n\t/**\n\t * Load `namespaces`.\n\t *\n\t * @return {String} returns the previously persisted debug modes\n\t * @api private\n\t */\n\t\n\tfunction load() {\n\t  var r;\n\t  try {\n\t    r = exports.storage.debug;\n\t  } catch (e) {}\n\t\n\t  // If debug isn't set in LS, and we're in Electron, try to load $DEBUG\n\t  if (!r && typeof process !== 'undefined' && 'env' in process) {\n\t    r = process.env.DEBUG;\n\t  }\n\t\n\t  return r;\n\t}\n\t\n\t/**\n\t * Enable namespaces listed in `localStorage.debug` initially.\n\t */\n\t\n\texports.enable(load());\n\t\n\t/**\n\t * Localstorage attempts to return the localstorage.\n\t *\n\t * This is necessary because safari throws\n\t * when a user disables cookies/localstorage\n\t * and you attempt to access it.\n\t *\n\t * @return {LocalStorage}\n\t * @api private\n\t */\n\t\n\tfunction localstorage() {\n\t  try {\n\t    return window.localStorage;\n\t  } catch (e) {}\n\t}\n\t/* WEBPACK VAR INJECTION */}.call(exports, __webpack_require__(4)))\n\n/***/ }),\n/* 9 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t'use strict';\n\t\n\t/**\n\t * This is the common logic for both the Node.js and web browser\n\t * implementations of `debug()`.\n\t *\n\t * Expose `debug()` as the module.\n\t */\n\t\n\texports = module.exports = createDebug.debug = createDebug['default'] = createDebug;\n\texports.coerce = coerce;\n\texports.disable = disable;\n\texports.enable = enable;\n\texports.enabled = enabled;\n\texports.humanize = __webpack_require__(10);\n\t\n\t/**\n\t * Active `debug` instances.\n\t */\n\texports.instances = [];\n\t\n\t/**\n\t * The currently active debug mode names, and names to skip.\n\t */\n\t\n\texports.names = [];\n\texports.skips = [];\n\t\n\t/**\n\t * Map of special \"%n\" handling functions, for the debug \"format\" argument.\n\t *\n\t * Valid key names are a single, lower or upper-case letter, i.e. \"n\" and \"N\".\n\t */\n\t\n\texports.formatters = {};\n\t\n\t/**\n\t * Select a color.\n\t * @param {String} namespace\n\t * @return {Number}\n\t * @api private\n\t */\n\t\n\tfunction selectColor(namespace) {\n\t  var hash = 0,\n\t      i;\n\t\n\t  for (i in namespace) {\n\t    hash = (hash << 5) - hash + namespace.charCodeAt(i);\n\t    hash |= 0; // Convert to 32bit integer\n\t  }\n\t\n\t  return exports.colors[Math.abs(hash) % exports.colors.length];\n\t}\n\t\n\t/**\n\t * Create a debugger with the given `namespace`.\n\t *\n\t * @param {String} namespace\n\t * @return {Function}\n\t * @api public\n\t */\n\t\n\tfunction createDebug(namespace) {\n\t\n\t  var prevTime;\n\t\n\t  function debug() {\n\t    // disabled?\n\t    if (!debug.enabled) return;\n\t\n\t    var self = debug;\n\t\n\t    // set `diff` timestamp\n\t    var curr = +new Date();\n\t    var ms = curr - (prevTime || curr);\n\t    self.diff = ms;\n\t    self.prev = prevTime;\n\t    self.curr = curr;\n\t    prevTime = curr;\n\t\n\t    // turn the `arguments` into a proper Array\n\t    var args = new Array(arguments.length);\n\t    for (var i = 0; i < args.length; i++) {\n\t      args[i] = arguments[i];\n\t    }\n\t\n\t    args[0] = exports.coerce(args[0]);\n\t\n\t    if ('string' !== typeof args[0]) {\n\t      // anything else let's inspect with %O\n\t      args.unshift('%O');\n\t    }\n\t\n\t    // apply any `formatters` transformations\n\t    var index = 0;\n\t    args[0] = args[0].replace(/%([a-zA-Z%])/g, function (match, format) {\n\t      // if we encounter an escaped % then don't increase the array index\n\t      if (match === '%%') return match;\n\t      index++;\n\t      var formatter = exports.formatters[format];\n\t      if ('function' === typeof formatter) {\n\t        var val = args[index];\n\t        match = formatter.call(self, val);\n\t\n\t        // now we need to remove `args[index]` since it's inlined in the `format`\n\t        args.splice(index, 1);\n\t        index--;\n\t      }\n\t      return match;\n\t    });\n\t\n\t    // apply env-specific formatting (colors, etc.)\n\t    exports.formatArgs.call(self, args);\n\t\n\t    var logFn = debug.log || exports.log || console.log.bind(console);\n\t    logFn.apply(self, args);\n\t  }\n\t\n\t  debug.namespace = namespace;\n\t  debug.enabled = exports.enabled(namespace);\n\t  debug.useColors = exports.useColors();\n\t  debug.color = selectColor(namespace);\n\t  debug.destroy = destroy;\n\t\n\t  // env-specific initialization logic for debug instances\n\t  if ('function' === typeof exports.init) {\n\t    exports.init(debug);\n\t  }\n\t\n\t  exports.instances.push(debug);\n\t\n\t  return debug;\n\t}\n\t\n\tfunction destroy() {\n\t  var index = exports.instances.indexOf(this);\n\t  if (index !== -1) {\n\t    exports.instances.splice(index, 1);\n\t    return true;\n\t  } else {\n\t    return false;\n\t  }\n\t}\n\t\n\t/**\n\t * Enables a debug mode by namespaces. This can include modes\n\t * separated by a colon and wildcards.\n\t *\n\t * @param {String} namespaces\n\t * @api public\n\t */\n\t\n\tfunction enable(namespaces) {\n\t  exports.save(namespaces);\n\t\n\t  exports.names = [];\n\t  exports.skips = [];\n\t\n\t  var i;\n\t  var split = (typeof namespaces === 'string' ? namespaces : '').split(/[\\s,]+/);\n\t  var len = split.length;\n\t\n\t  for (i = 0; i < len; i++) {\n\t    if (!split[i]) continue; // ignore empty strings\n\t    namespaces = split[i].replace(/\\*/g, '.*?');\n\t    if (namespaces[0] === '-') {\n\t      exports.skips.push(new RegExp('^' + namespaces.substr(1) + '$'));\n\t    } else {\n\t      exports.names.push(new RegExp('^' + namespaces + '$'));\n\t    }\n\t  }\n\t\n\t  for (i = 0; i < exports.instances.length; i++) {\n\t    var instance = exports.instances[i];\n\t    instance.enabled = exports.enabled(instance.namespace);\n\t  }\n\t}\n\t\n\t/**\n\t * Disable debug output.\n\t *\n\t * @api public\n\t */\n\t\n\tfunction disable() {\n\t  exports.enable('');\n\t}\n\t\n\t/**\n\t * Returns true if the given mode name is enabled, false otherwise.\n\t *\n\t * @param {String} name\n\t * @return {Boolean}\n\t * @api public\n\t */\n\t\n\tfunction enabled(name) {\n\t  if (name[name.length - 1] === '*') {\n\t    return true;\n\t  }\n\t  var i, len;\n\t  for (i = 0, len = exports.skips.length; i < len; i++) {\n\t    if (exports.skips[i].test(name)) {\n\t      return false;\n\t    }\n\t  }\n\t  for (i = 0, len = exports.names.length; i < len; i++) {\n\t    if (exports.names[i].test(name)) {\n\t      return true;\n\t    }\n\t  }\n\t  return false;\n\t}\n\t\n\t/**\n\t * Coerce `val`.\n\t *\n\t * @param {Mixed} val\n\t * @return {Mixed}\n\t * @api private\n\t */\n\t\n\tfunction coerce(val) {\n\t  if (val instanceof Error) return val.stack || val.message;\n\t  return val;\n\t}\n\n/***/ }),\n/* 10 */\n/***/ (function(module, exports) {\n\n\t/**\n\t * Helpers.\n\t */\n\t\n\tvar s = 1000;\n\tvar m = s * 60;\n\tvar h = m * 60;\n\tvar d = h * 24;\n\tvar y = d * 365.25;\n\t\n\t/**\n\t * Parse or format the given `val`.\n\t *\n\t * Options:\n\t *\n\t *  - `long` verbose formatting [false]\n\t *\n\t * @param {String|Number} val\n\t * @param {Object} [options]\n\t * @throws {Error} throw an error if val is not a non-empty string or a number\n\t * @return {String|Number}\n\t * @api public\n\t */\n\t\n\tmodule.exports = function(val, options) {\n\t  options = options || {};\n\t  var type = typeof val;\n\t  if (type === 'string' && val.length > 0) {\n\t    return parse(val);\n\t  } else if (type === 'number' && isNaN(val) === false) {\n\t    return options.long ? fmtLong(val) : fmtShort(val);\n\t  }\n\t  throw new Error(\n\t    'val is not a non-empty string or a valid number. val=' +\n\t      JSON.stringify(val)\n\t  );\n\t};\n\t\n\t/**\n\t * Parse the given `str` and return milliseconds.\n\t *\n\t * @param {String} str\n\t * @return {Number}\n\t * @api private\n\t */\n\t\n\tfunction parse(str) {\n\t  str = String(str);\n\t  if (str.length > 100) {\n\t    return;\n\t  }\n\t  var match = /^((?:\\d+)?\\.?\\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|years?|yrs?|y)?$/i.exec(\n\t    str\n\t  );\n\t  if (!match) {\n\t    return;\n\t  }\n\t  var n = parseFloat(match[1]);\n\t  var type = (match[2] || 'ms').toLowerCase();\n\t  switch (type) {\n\t    case 'years':\n\t    case 'year':\n\t    case 'yrs':\n\t    case 'yr':\n\t    case 'y':\n\t      return n * y;\n\t    case 'days':\n\t    case 'day':\n\t    case 'd':\n\t      return n * d;\n\t    case 'hours':\n\t    case 'hour':\n\t    case 'hrs':\n\t    case 'hr':\n\t    case 'h':\n\t      return n * h;\n\t    case 'minutes':\n\t    case 'minute':\n\t    case 'mins':\n\t    case 'min':\n\t    case 'm':\n\t      return n * m;\n\t    case 'seconds':\n\t    case 'second':\n\t    case 'secs':\n\t    case 'sec':\n\t    case 's':\n\t      return n * s;\n\t    case 'milliseconds':\n\t    case 'millisecond':\n\t    case 'msecs':\n\t    case 'msec':\n\t    case 'ms':\n\t      return n;\n\t    default:\n\t      return undefined;\n\t  }\n\t}\n\t\n\t/**\n\t * Short format for `ms`.\n\t *\n\t * @param {Number} ms\n\t * @return {String}\n\t * @api private\n\t */\n\t\n\tfunction fmtShort(ms) {\n\t  if (ms >= d) {\n\t    return Math.round(ms / d) + 'd';\n\t  }\n\t  if (ms >= h) {\n\t    return Math.round(ms / h) + 'h';\n\t  }\n\t  if (ms >= m) {\n\t    return Math.round(ms / m) + 'm';\n\t  }\n\t  if (ms >= s) {\n\t    return Math.round(ms / s) + 's';\n\t  }\n\t  return ms + 'ms';\n\t}\n\t\n\t/**\n\t * Long format for `ms`.\n\t *\n\t * @param {Number} ms\n\t * @return {String}\n\t * @api private\n\t */\n\t\n\tfunction fmtLong(ms) {\n\t  return plural(ms, d, 'day') ||\n\t    plural(ms, h, 'hour') ||\n\t    plural(ms, m, 'minute') ||\n\t    plural(ms, s, 'second') ||\n\t    ms + ' ms';\n\t}\n\t\n\t/**\n\t * Pluralization helper.\n\t */\n\t\n\tfunction plural(ms, n, name) {\n\t  if (ms < n) {\n\t    return;\n\t  }\n\t  if (ms < n * 1.5) {\n\t    return Math.floor(ms / n) + ' ' + name;\n\t  }\n\t  return Math.ceil(ms / n) + ' ' + name + 's';\n\t}\n\n\n/***/ }),\n/* 11 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t\r\n\t/**\r\n\t * Expose `Emitter`.\r\n\t */\r\n\t\r\n\tif (true) {\r\n\t  module.exports = Emitter;\r\n\t}\r\n\t\r\n\t/**\r\n\t * Initialize a new `Emitter`.\r\n\t *\r\n\t * @api public\r\n\t */\r\n\t\r\n\tfunction Emitter(obj) {\r\n\t  if (obj) return mixin(obj);\r\n\t};\r\n\t\r\n\t/**\r\n\t * Mixin the emitter properties.\r\n\t *\r\n\t * @param {Object} obj\r\n\t * @return {Object}\r\n\t * @api private\r\n\t */\r\n\t\r\n\tfunction mixin(obj) {\r\n\t  for (var key in Emitter.prototype) {\r\n\t    obj[key] = Emitter.prototype[key];\r\n\t  }\r\n\t  return obj;\r\n\t}\r\n\t\r\n\t/**\r\n\t * Listen on the given `event` with `fn`.\r\n\t *\r\n\t * @param {String} event\r\n\t * @param {Function} fn\r\n\t * @return {Emitter}\r\n\t * @api public\r\n\t */\r\n\t\r\n\tEmitter.prototype.on =\r\n\tEmitter.prototype.addEventListener = function(event, fn){\r\n\t  this._callbacks = this._callbacks || {};\r\n\t  (this._callbacks['$' + event] = this._callbacks['$' + event] || [])\r\n\t    .push(fn);\r\n\t  return this;\r\n\t};\r\n\t\r\n\t/**\r\n\t * Adds an `event` listener that will be invoked a single\r\n\t * time then automatically removed.\r\n\t *\r\n\t * @param {String} event\r\n\t * @param {Function} fn\r\n\t * @return {Emitter}\r\n\t * @api public\r\n\t */\r\n\t\r\n\tEmitter.prototype.once = function(event, fn){\r\n\t  function on() {\r\n\t    this.off(event, on);\r\n\t    fn.apply(this, arguments);\r\n\t  }\r\n\t\r\n\t  on.fn = fn;\r\n\t  this.on(event, on);\r\n\t  return this;\r\n\t};\r\n\t\r\n\t/**\r\n\t * Remove the given callback for `event` or all\r\n\t * registered callbacks.\r\n\t *\r\n\t * @param {String} event\r\n\t * @param {Function} fn\r\n\t * @return {Emitter}\r\n\t * @api public\r\n\t */\r\n\t\r\n\tEmitter.prototype.off =\r\n\tEmitter.prototype.removeListener =\r\n\tEmitter.prototype.removeAllListeners =\r\n\tEmitter.prototype.removeEventListener = function(event, fn){\r\n\t  this._callbacks = this._callbacks || {};\r\n\t\r\n\t  // all\r\n\t  if (0 == arguments.length) {\r\n\t    this._callbacks = {};\r\n\t    return this;\r\n\t  }\r\n\t\r\n\t  // specific event\r\n\t  var callbacks = this._callbacks['$' + event];\r\n\t  if (!callbacks) return this;\r\n\t\r\n\t  // remove all handlers\r\n\t  if (1 == arguments.length) {\r\n\t    delete this._callbacks['$' + event];\r\n\t    return this;\r\n\t  }\r\n\t\r\n\t  // remove specific handler\r\n\t  var cb;\r\n\t  for (var i = 0; i < callbacks.length; i++) {\r\n\t    cb = callbacks[i];\r\n\t    if (cb === fn || cb.fn === fn) {\r\n\t      callbacks.splice(i, 1);\r\n\t      break;\r\n\t    }\r\n\t  }\r\n\t  return this;\r\n\t};\r\n\t\r\n\t/**\r\n\t * Emit `event` with the given args.\r\n\t *\r\n\t * @param {String} event\r\n\t * @param {Mixed} ...\r\n\t * @return {Emitter}\r\n\t */\r\n\t\r\n\tEmitter.prototype.emit = function(event){\r\n\t  this._callbacks = this._callbacks || {};\r\n\t  var args = [].slice.call(arguments, 1)\r\n\t    , callbacks = this._callbacks['$' + event];\r\n\t\r\n\t  if (callbacks) {\r\n\t    callbacks = callbacks.slice(0);\r\n\t    for (var i = 0, len = callbacks.length; i < len; ++i) {\r\n\t      callbacks[i].apply(this, args);\r\n\t    }\r\n\t  }\r\n\t\r\n\t  return this;\r\n\t};\r\n\t\r\n\t/**\r\n\t * Return array of callbacks for `event`.\r\n\t *\r\n\t * @param {String} event\r\n\t * @return {Array}\r\n\t * @api public\r\n\t */\r\n\t\r\n\tEmitter.prototype.listeners = function(event){\r\n\t  this._callbacks = this._callbacks || {};\r\n\t  return this._callbacks['$' + event] || [];\r\n\t};\r\n\t\r\n\t/**\r\n\t * Check if this emitter has `event` handlers.\r\n\t *\r\n\t * @param {String} event\r\n\t * @return {Boolean}\r\n\t * @api public\r\n\t */\r\n\t\r\n\tEmitter.prototype.hasListeners = function(event){\r\n\t  return !! this.listeners(event).length;\r\n\t};\r\n\n\n/***/ }),\n/* 12 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/*global Blob,File*/\n\t\n\t/**\n\t * Module requirements\n\t */\n\t\n\tvar isArray = __webpack_require__(13);\n\tvar isBuf = __webpack_require__(14);\n\tvar toString = Object.prototype.toString;\n\tvar withNativeBlob = typeof Blob === 'function' || (typeof Blob !== 'undefined' && toString.call(Blob) === '[object BlobConstructor]');\n\tvar withNativeFile = typeof File === 'function' || (typeof File !== 'undefined' && toString.call(File) === '[object FileConstructor]');\n\t\n\t/**\n\t * Replaces every Buffer | ArrayBuffer in packet with a numbered placeholder.\n\t * Anything with blobs or files should be fed through removeBlobs before coming\n\t * here.\n\t *\n\t * @param {Object} packet - socket.io event packet\n\t * @return {Object} with deconstructed packet and list of buffers\n\t * @api public\n\t */\n\t\n\texports.deconstructPacket = function(packet) {\n\t  var buffers = [];\n\t  var packetData = packet.data;\n\t  var pack = packet;\n\t  pack.data = _deconstructPacket(packetData, buffers);\n\t  pack.attachments = buffers.length; // number of binary 'attachments'\n\t  return {packet: pack, buffers: buffers};\n\t};\n\t\n\tfunction _deconstructPacket(data, buffers) {\n\t  if (!data) return data;\n\t\n\t  if (isBuf(data)) {\n\t    var placeholder = { _placeholder: true, num: buffers.length };\n\t    buffers.push(data);\n\t    return placeholder;\n\t  } else if (isArray(data)) {\n\t    var newData = new Array(data.length);\n\t    for (var i = 0; i < data.length; i++) {\n\t      newData[i] = _deconstructPacket(data[i], buffers);\n\t    }\n\t    return newData;\n\t  } else if (typeof data === 'object' && !(data instanceof Date)) {\n\t    var newData = {};\n\t    for (var key in data) {\n\t      newData[key] = _deconstructPacket(data[key], buffers);\n\t    }\n\t    return newData;\n\t  }\n\t  return data;\n\t}\n\t\n\t/**\n\t * Reconstructs a binary packet from its placeholder packet and buffers\n\t *\n\t * @param {Object} packet - event packet with placeholders\n\t * @param {Array} buffers - binary buffers to put in placeholder positions\n\t * @return {Object} reconstructed packet\n\t * @api public\n\t */\n\t\n\texports.reconstructPacket = function(packet, buffers) {\n\t  packet.data = _reconstructPacket(packet.data, buffers);\n\t  packet.attachments = undefined; // no longer useful\n\t  return packet;\n\t};\n\t\n\tfunction _reconstructPacket(data, buffers) {\n\t  if (!data) return data;\n\t\n\t  if (data && data._placeholder) {\n\t    return buffers[data.num]; // appropriate buffer (should be natural order anyway)\n\t  } else if (isArray(data)) {\n\t    for (var i = 0; i < data.length; i++) {\n\t      data[i] = _reconstructPacket(data[i], buffers);\n\t    }\n\t  } else if (typeof data === 'object') {\n\t    for (var key in data) {\n\t      data[key] = _reconstructPacket(data[key], buffers);\n\t    }\n\t  }\n\t\n\t  return data;\n\t}\n\t\n\t/**\n\t * Asynchronously removes Blobs or Files from data via\n\t * FileReader's readAsArrayBuffer method. Used before encoding\n\t * data as msgpack. Calls callback with the blobless data.\n\t *\n\t * @param {Object} data\n\t * @param {Function} callback\n\t * @api private\n\t */\n\t\n\texports.removeBlobs = function(data, callback) {\n\t  function _removeBlobs(obj, curKey, containingObject) {\n\t    if (!obj) return obj;\n\t\n\t    // convert any blob\n\t    if ((withNativeBlob && obj instanceof Blob) ||\n\t        (withNativeFile && obj instanceof File)) {\n\t      pendingBlobs++;\n\t\n\t      // async filereader\n\t      var fileReader = new FileReader();\n\t      fileReader.onload = function() { // this.result == arraybuffer\n\t        if (containingObject) {\n\t          containingObject[curKey] = this.result;\n\t        }\n\t        else {\n\t          bloblessData = this.result;\n\t        }\n\t\n\t        // if nothing pending its callback time\n\t        if(! --pendingBlobs) {\n\t          callback(bloblessData);\n\t        }\n\t      };\n\t\n\t      fileReader.readAsArrayBuffer(obj); // blob -> arraybuffer\n\t    } else if (isArray(obj)) { // handle array\n\t      for (var i = 0; i < obj.length; i++) {\n\t        _removeBlobs(obj[i], i, obj);\n\t      }\n\t    } else if (typeof obj === 'object' && !isBuf(obj)) { // and object\n\t      for (var key in obj) {\n\t        _removeBlobs(obj[key], key, obj);\n\t      }\n\t    }\n\t  }\n\t\n\t  var pendingBlobs = 0;\n\t  var bloblessData = data;\n\t  _removeBlobs(bloblessData);\n\t  if (!pendingBlobs) {\n\t    callback(bloblessData);\n\t  }\n\t};\n\n\n/***/ }),\n/* 13 */\n/***/ (function(module, exports) {\n\n\tvar toString = {}.toString;\n\t\n\tmodule.exports = Array.isArray || function (arr) {\n\t  return toString.call(arr) == '[object Array]';\n\t};\n\n\n/***/ }),\n/* 14 */\n/***/ (function(module, exports) {\n\n\t\n\tmodule.exports = isBuf;\n\t\n\tvar withNativeBuffer = typeof Buffer === 'function' && typeof Buffer.isBuffer === 'function';\n\tvar withNativeArrayBuffer = typeof ArrayBuffer === 'function';\n\t\n\tvar isView = function (obj) {\n\t  return typeof ArrayBuffer.isView === 'function' ? ArrayBuffer.isView(obj) : (obj.buffer instanceof ArrayBuffer);\n\t};\n\t\n\t/**\n\t * Returns true if obj is a buffer or an arraybuffer.\n\t *\n\t * @api private\n\t */\n\t\n\tfunction isBuf(obj) {\n\t  return (withNativeBuffer && Buffer.isBuffer(obj)) ||\n\t          (withNativeArrayBuffer && (obj instanceof ArrayBuffer || isView(obj)));\n\t}\n\n\n/***/ }),\n/* 15 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t\n\t/**\n\t * Module dependencies.\n\t */\n\t\n\tvar eio = __webpack_require__(16);\n\tvar Socket = __webpack_require__(39);\n\tvar Emitter = __webpack_require__(11);\n\tvar parser = __webpack_require__(7);\n\tvar on = __webpack_require__(41);\n\tvar bind = __webpack_require__(42);\n\tvar debug = __webpack_require__(3)('socket.io-client:manager');\n\tvar indexOf = __webpack_require__(38);\n\tvar Backoff = __webpack_require__(43);\n\t\n\t/**\n\t * IE6+ hasOwnProperty\n\t */\n\t\n\tvar has = Object.prototype.hasOwnProperty;\n\t\n\t/**\n\t * Module exports\n\t */\n\t\n\tmodule.exports = Manager;\n\t\n\t/**\n\t * `Manager` constructor.\n\t *\n\t * @param {String} engine instance or engine uri/opts\n\t * @param {Object} options\n\t * @api public\n\t */\n\t\n\tfunction Manager (uri, opts) {\n\t  if (!(this instanceof Manager)) return new Manager(uri, opts);\n\t  if (uri && ('object' === typeof uri)) {\n\t    opts = uri;\n\t    uri = undefined;\n\t  }\n\t  opts = opts || {};\n\t\n\t  opts.path = opts.path || '/socket.io';\n\t  this.nsps = {};\n\t  this.subs = [];\n\t  this.opts = opts;\n\t  this.reconnection(opts.reconnection !== false);\n\t  this.reconnectionAttempts(opts.reconnectionAttempts || Infinity);\n\t  this.reconnectionDelay(opts.reconnectionDelay || 1000);\n\t  this.reconnectionDelayMax(opts.reconnectionDelayMax || 5000);\n\t  this.randomizationFactor(opts.randomizationFactor || 0.5);\n\t  this.backoff = new Backoff({\n\t    min: this.reconnectionDelay(),\n\t    max: this.reconnectionDelayMax(),\n\t    jitter: this.randomizationFactor()\n\t  });\n\t  this.timeout(null == opts.timeout ? 20000 : opts.timeout);\n\t  this.readyState = 'closed';\n\t  this.uri = uri;\n\t  this.connecting = [];\n\t  this.lastPing = null;\n\t  this.encoding = false;\n\t  this.packetBuffer = [];\n\t  var _parser = opts.parser || parser;\n\t  this.encoder = new _parser.Encoder();\n\t  this.decoder = new _parser.Decoder();\n\t  this.autoConnect = opts.autoConnect !== false;\n\t  if (this.autoConnect) this.open();\n\t}\n\t\n\t/**\n\t * Propagate given event to sockets and emit on `this`\n\t *\n\t * @api private\n\t */\n\t\n\tManager.prototype.emitAll = function () {\n\t  this.emit.apply(this, arguments);\n\t  for (var nsp in this.nsps) {\n\t    if (has.call(this.nsps, nsp)) {\n\t      this.nsps[nsp].emit.apply(this.nsps[nsp], arguments);\n\t    }\n\t  }\n\t};\n\t\n\t/**\n\t * Update `socket.id` of all sockets\n\t *\n\t * @api private\n\t */\n\t\n\tManager.prototype.updateSocketIds = function () {\n\t  for (var nsp in this.nsps) {\n\t    if (has.call(this.nsps, nsp)) {\n\t      this.nsps[nsp].id = this.generateId(nsp);\n\t    }\n\t  }\n\t};\n\t\n\t/**\n\t * generate `socket.id` for the given `nsp`\n\t *\n\t * @param {String} nsp\n\t * @return {String}\n\t * @api private\n\t */\n\t\n\tManager.prototype.generateId = function (nsp) {\n\t  return (nsp === '/' ? '' : (nsp + '#')) + this.engine.id;\n\t};\n\t\n\t/**\n\t * Mix in `Emitter`.\n\t */\n\t\n\tEmitter(Manager.prototype);\n\t\n\t/**\n\t * Sets the `reconnection` config.\n\t *\n\t * @param {Boolean} true/false if it should automatically reconnect\n\t * @return {Manager} self or value\n\t * @api public\n\t */\n\t\n\tManager.prototype.reconnection = function (v) {\n\t  if (!arguments.length) return this._reconnection;\n\t  this._reconnection = !!v;\n\t  return this;\n\t};\n\t\n\t/**\n\t * Sets the reconnection attempts config.\n\t *\n\t * @param {Number} max reconnection attempts before giving up\n\t * @return {Manager} self or value\n\t * @api public\n\t */\n\t\n\tManager.prototype.reconnectionAttempts = function (v) {\n\t  if (!arguments.length) return this._reconnectionAttempts;\n\t  this._reconnectionAttempts = v;\n\t  return this;\n\t};\n\t\n\t/**\n\t * Sets the delay between reconnections.\n\t *\n\t * @param {Number} delay\n\t * @return {Manager} self or value\n\t * @api public\n\t */\n\t\n\tManager.prototype.reconnectionDelay = function (v) {\n\t  if (!arguments.length) return this._reconnectionDelay;\n\t  this._reconnectionDelay = v;\n\t  this.backoff && this.backoff.setMin(v);\n\t  return this;\n\t};\n\t\n\tManager.prototype.randomizationFactor = function (v) {\n\t  if (!arguments.length) return this._randomizationFactor;\n\t  this._randomizationFactor = v;\n\t  this.backoff && this.backoff.setJitter(v);\n\t  return this;\n\t};\n\t\n\t/**\n\t * Sets the maximum delay between reconnections.\n\t *\n\t * @param {Number} delay\n\t * @return {Manager} self or value\n\t * @api public\n\t */\n\t\n\tManager.prototype.reconnectionDelayMax = function (v) {\n\t  if (!arguments.length) return this._reconnectionDelayMax;\n\t  this._reconnectionDelayMax = v;\n\t  this.backoff && this.backoff.setMax(v);\n\t  return this;\n\t};\n\t\n\t/**\n\t * Sets the connection timeout. `false` to disable\n\t *\n\t * @return {Manager} self or value\n\t * @api public\n\t */\n\t\n\tManager.prototype.timeout = function (v) {\n\t  if (!arguments.length) return this._timeout;\n\t  this._timeout = v;\n\t  return this;\n\t};\n\t\n\t/**\n\t * Starts trying to reconnect if reconnection is enabled and we have not\n\t * started reconnecting yet\n\t *\n\t * @api private\n\t */\n\t\n\tManager.prototype.maybeReconnectOnOpen = function () {\n\t  // Only try to reconnect if it's the first time we're connecting\n\t  if (!this.reconnecting && this._reconnection && this.backoff.attempts === 0) {\n\t    // keeps reconnection from firing twice for the same reconnection loop\n\t    this.reconnect();\n\t  }\n\t};\n\t\n\t/**\n\t * Sets the current transport `socket`.\n\t *\n\t * @param {Function} optional, callback\n\t * @return {Manager} self\n\t * @api public\n\t */\n\t\n\tManager.prototype.open =\n\tManager.prototype.connect = function (fn, opts) {\n\t  debug('readyState %s', this.readyState);\n\t  if (~this.readyState.indexOf('open')) return this;\n\t\n\t  debug('opening %s', this.uri);\n\t  this.engine = eio(this.uri, this.opts);\n\t  var socket = this.engine;\n\t  var self = this;\n\t  this.readyState = 'opening';\n\t  this.skipReconnect = false;\n\t\n\t  // emit `open`\n\t  var openSub = on(socket, 'open', function () {\n\t    self.onopen();\n\t    fn && fn();\n\t  });\n\t\n\t  // emit `connect_error`\n\t  var errorSub = on(socket, 'error', function (data) {\n\t    debug('connect_error');\n\t    self.cleanup();\n\t    self.readyState = 'closed';\n\t    self.emitAll('connect_error', data);\n\t    if (fn) {\n\t      var err = new Error('Connection error');\n\t      err.data = data;\n\t      fn(err);\n\t    } else {\n\t      // Only do this if there is no fn to handle the error\n\t      self.maybeReconnectOnOpen();\n\t    }\n\t  });\n\t\n\t  // emit `connect_timeout`\n\t  if (false !== this._timeout) {\n\t    var timeout = this._timeout;\n\t    debug('connect attempt will timeout after %d', timeout);\n\t\n\t    // set timer\n\t    var timer = setTimeout(function () {\n\t      debug('connect attempt timed out after %d', timeout);\n\t      openSub.destroy();\n\t      socket.close();\n\t      socket.emit('error', 'timeout');\n\t      self.emitAll('connect_timeout', timeout);\n\t    }, timeout);\n\t\n\t    this.subs.push({\n\t      destroy: function () {\n\t        clearTimeout(timer);\n\t      }\n\t    });\n\t  }\n\t\n\t  this.subs.push(openSub);\n\t  this.subs.push(errorSub);\n\t\n\t  return this;\n\t};\n\t\n\t/**\n\t * Called upon transport open.\n\t *\n\t * @api private\n\t */\n\t\n\tManager.prototype.onopen = function () {\n\t  debug('open');\n\t\n\t  // clear old subs\n\t  this.cleanup();\n\t\n\t  // mark as open\n\t  this.readyState = 'open';\n\t  this.emit('open');\n\t\n\t  // add new subs\n\t  var socket = this.engine;\n\t  this.subs.push(on(socket, 'data', bind(this, 'ondata')));\n\t  this.subs.push(on(socket, 'ping', bind(this, 'onping')));\n\t  this.subs.push(on(socket, 'pong', bind(this, 'onpong')));\n\t  this.subs.push(on(socket, 'error', bind(this, 'onerror')));\n\t  this.subs.push(on(socket, 'close', bind(this, 'onclose')));\n\t  this.subs.push(on(this.decoder, 'decoded', bind(this, 'ondecoded')));\n\t};\n\t\n\t/**\n\t * Called upon a ping.\n\t *\n\t * @api private\n\t */\n\t\n\tManager.prototype.onping = function () {\n\t  this.lastPing = new Date();\n\t  this.emitAll('ping');\n\t};\n\t\n\t/**\n\t * Called upon a packet.\n\t *\n\t * @api private\n\t */\n\t\n\tManager.prototype.onpong = function () {\n\t  this.emitAll('pong', new Date() - this.lastPing);\n\t};\n\t\n\t/**\n\t * Called with data.\n\t *\n\t * @api private\n\t */\n\t\n\tManager.prototype.ondata = function (data) {\n\t  this.decoder.add(data);\n\t};\n\t\n\t/**\n\t * Called when parser fully decodes a packet.\n\t *\n\t * @api private\n\t */\n\t\n\tManager.prototype.ondecoded = function (packet) {\n\t  this.emit('packet', packet);\n\t};\n\t\n\t/**\n\t * Called upon socket error.\n\t *\n\t * @api private\n\t */\n\t\n\tManager.prototype.onerror = function (err) {\n\t  debug('error', err);\n\t  this.emitAll('error', err);\n\t};\n\t\n\t/**\n\t * Creates a new socket for the given `nsp`.\n\t *\n\t * @return {Socket}\n\t * @api public\n\t */\n\t\n\tManager.prototype.socket = function (nsp, opts) {\n\t  var socket = this.nsps[nsp];\n\t  if (!socket) {\n\t    socket = new Socket(this, nsp, opts);\n\t    this.nsps[nsp] = socket;\n\t    var self = this;\n\t    socket.on('connecting', onConnecting);\n\t    socket.on('connect', function () {\n\t      socket.id = self.generateId(nsp);\n\t    });\n\t\n\t    if (this.autoConnect) {\n\t      // manually call here since connecting event is fired before listening\n\t      onConnecting();\n\t    }\n\t  }\n\t\n\t  function onConnecting () {\n\t    if (!~indexOf(self.connecting, socket)) {\n\t      self.connecting.push(socket);\n\t    }\n\t  }\n\t\n\t  return socket;\n\t};\n\t\n\t/**\n\t * Called upon a socket close.\n\t *\n\t * @param {Socket} socket\n\t */\n\t\n\tManager.prototype.destroy = function (socket) {\n\t  var index = indexOf(this.connecting, socket);\n\t  if (~index) this.connecting.splice(index, 1);\n\t  if (this.connecting.length) return;\n\t\n\t  this.close();\n\t};\n\t\n\t/**\n\t * Writes a packet.\n\t *\n\t * @param {Object} packet\n\t * @api private\n\t */\n\t\n\tManager.prototype.packet = function (packet) {\n\t  debug('writing packet %j', packet);\n\t  var self = this;\n\t  if (packet.query && packet.type === 0) packet.nsp += '?' + packet.query;\n\t\n\t  if (!self.encoding) {\n\t    // encode, then write to engine with result\n\t    self.encoding = true;\n\t    this.encoder.encode(packet, function (encodedPackets) {\n\t      for (var i = 0; i < encodedPackets.length; i++) {\n\t        self.engine.write(encodedPackets[i], packet.options);\n\t      }\n\t      self.encoding = false;\n\t      self.processPacketQueue();\n\t    });\n\t  } else { // add packet to the queue\n\t    self.packetBuffer.push(packet);\n\t  }\n\t};\n\t\n\t/**\n\t * If packet buffer is non-empty, begins encoding the\n\t * next packet in line.\n\t *\n\t * @api private\n\t */\n\t\n\tManager.prototype.processPacketQueue = function () {\n\t  if (this.packetBuffer.length > 0 && !this.encoding) {\n\t    var pack = this.packetBuffer.shift();\n\t    this.packet(pack);\n\t  }\n\t};\n\t\n\t/**\n\t * Clean up transport subscriptions and packet buffer.\n\t *\n\t * @api private\n\t */\n\t\n\tManager.prototype.cleanup = function () {\n\t  debug('cleanup');\n\t\n\t  var subsLength = this.subs.length;\n\t  for (var i = 0; i < subsLength; i++) {\n\t    var sub = this.subs.shift();\n\t    sub.destroy();\n\t  }\n\t\n\t  this.packetBuffer = [];\n\t  this.encoding = false;\n\t  this.lastPing = null;\n\t\n\t  this.decoder.destroy();\n\t};\n\t\n\t/**\n\t * Close the current socket.\n\t *\n\t * @api private\n\t */\n\t\n\tManager.prototype.close =\n\tManager.prototype.disconnect = function () {\n\t  debug('disconnect');\n\t  this.skipReconnect = true;\n\t  this.reconnecting = false;\n\t  if ('opening' === this.readyState) {\n\t    // `onclose` will not fire because\n\t    // an open event never happened\n\t    this.cleanup();\n\t  }\n\t  this.backoff.reset();\n\t  this.readyState = 'closed';\n\t  if (this.engine) this.engine.close();\n\t};\n\t\n\t/**\n\t * Called upon engine close.\n\t *\n\t * @api private\n\t */\n\t\n\tManager.prototype.onclose = function (reason) {\n\t  debug('onclose');\n\t\n\t  this.cleanup();\n\t  this.backoff.reset();\n\t  this.readyState = 'closed';\n\t  this.emit('close', reason);\n\t\n\t  if (this._reconnection && !this.skipReconnect) {\n\t    this.reconnect();\n\t  }\n\t};\n\t\n\t/**\n\t * Attempt a reconnection.\n\t *\n\t * @api private\n\t */\n\t\n\tManager.prototype.reconnect = function () {\n\t  if (this.reconnecting || this.skipReconnect) return this;\n\t\n\t  var self = this;\n\t\n\t  if (this.backoff.attempts >= this._reconnectionAttempts) {\n\t    debug('reconnect failed');\n\t    this.backoff.reset();\n\t    this.emitAll('reconnect_failed');\n\t    this.reconnecting = false;\n\t  } else {\n\t    var delay = this.backoff.duration();\n\t    debug('will wait %dms before reconnect attempt', delay);\n\t\n\t    this.reconnecting = true;\n\t    var timer = setTimeout(function () {\n\t      if (self.skipReconnect) return;\n\t\n\t      debug('attempting reconnect');\n\t      self.emitAll('reconnect_attempt', self.backoff.attempts);\n\t      self.emitAll('reconnecting', self.backoff.attempts);\n\t\n\t      // check again for the case socket closed in above events\n\t      if (self.skipReconnect) return;\n\t\n\t      self.open(function (err) {\n\t        if (err) {\n\t          debug('reconnect attempt error');\n\t          self.reconnecting = false;\n\t          self.reconnect();\n\t          self.emitAll('reconnect_error', err.data);\n\t        } else {\n\t          debug('reconnect success');\n\t          self.onreconnect();\n\t        }\n\t      });\n\t    }, delay);\n\t\n\t    this.subs.push({\n\t      destroy: function () {\n\t        clearTimeout(timer);\n\t      }\n\t    });\n\t  }\n\t};\n\t\n\t/**\n\t * Called upon successful reconnect.\n\t *\n\t * @api private\n\t */\n\t\n\tManager.prototype.onreconnect = function () {\n\t  var attempt = this.backoff.attempts;\n\t  this.reconnecting = false;\n\t  this.backoff.reset();\n\t  this.updateSocketIds();\n\t  this.emitAll('reconnect', attempt);\n\t};\n\n\n/***/ }),\n/* 16 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t\n\tmodule.exports = __webpack_require__(17);\n\t\n\t/**\n\t * Exports parser\n\t *\n\t * @api public\n\t *\n\t */\n\tmodule.exports.parser = __webpack_require__(24);\n\n\n/***/ }),\n/* 17 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/**\n\t * Module dependencies.\n\t */\n\t\n\tvar transports = __webpack_require__(18);\n\tvar Emitter = __webpack_require__(11);\n\tvar debug = __webpack_require__(3)('engine.io-client:socket');\n\tvar index = __webpack_require__(38);\n\tvar parser = __webpack_require__(24);\n\tvar parseuri = __webpack_require__(2);\n\tvar parseqs = __webpack_require__(32);\n\t\n\t/**\n\t * Module exports.\n\t */\n\t\n\tmodule.exports = Socket;\n\t\n\t/**\n\t * Socket constructor.\n\t *\n\t * @param {String|Object} uri or options\n\t * @param {Object} options\n\t * @api public\n\t */\n\t\n\tfunction Socket (uri, opts) {\n\t  if (!(this instanceof Socket)) return new Socket(uri, opts);\n\t\n\t  opts = opts || {};\n\t\n\t  if (uri && 'object' === typeof uri) {\n\t    opts = uri;\n\t    uri = null;\n\t  }\n\t\n\t  if (uri) {\n\t    uri = parseuri(uri);\n\t    opts.hostname = uri.host;\n\t    opts.secure = uri.protocol === 'https' || uri.protocol === 'wss';\n\t    opts.port = uri.port;\n\t    if (uri.query) opts.query = uri.query;\n\t  } else if (opts.host) {\n\t    opts.hostname = parseuri(opts.host).host;\n\t  }\n\t\n\t  this.secure = null != opts.secure ? opts.secure\n\t    : (typeof location !== 'undefined' && 'https:' === location.protocol);\n\t\n\t  if (opts.hostname && !opts.port) {\n\t    // if no port is specified manually, use the protocol default\n\t    opts.port = this.secure ? '443' : '80';\n\t  }\n\t\n\t  this.agent = opts.agent || false;\n\t  this.hostname = opts.hostname ||\n\t    (typeof location !== 'undefined' ? location.hostname : 'localhost');\n\t  this.port = opts.port || (typeof location !== 'undefined' && location.port\n\t      ? location.port\n\t      : (this.secure ? 443 : 80));\n\t  this.query = opts.query || {};\n\t  if ('string' === typeof this.query) this.query = parseqs.decode(this.query);\n\t  this.upgrade = false !== opts.upgrade;\n\t  this.path = (opts.path || '/engine.io').replace(/\\/$/, '') + '/';\n\t  this.forceJSONP = !!opts.forceJSONP;\n\t  this.jsonp = false !== opts.jsonp;\n\t  this.forceBase64 = !!opts.forceBase64;\n\t  this.enablesXDR = !!opts.enablesXDR;\n\t  this.withCredentials = false !== opts.withCredentials;\n\t  this.timestampParam = opts.timestampParam || 't';\n\t  this.timestampRequests = opts.timestampRequests;\n\t  this.transports = opts.transports || ['polling', 'websocket'];\n\t  this.transportOptions = opts.transportOptions || {};\n\t  this.readyState = '';\n\t  this.writeBuffer = [];\n\t  this.prevBufferLen = 0;\n\t  this.policyPort = opts.policyPort || 843;\n\t  this.rememberUpgrade = opts.rememberUpgrade || false;\n\t  this.binaryType = null;\n\t  this.onlyBinaryUpgrades = opts.onlyBinaryUpgrades;\n\t  this.perMessageDeflate = false !== opts.perMessageDeflate ? (opts.perMessageDeflate || {}) : false;\n\t\n\t  if (true === this.perMessageDeflate) this.perMessageDeflate = {};\n\t  if (this.perMessageDeflate && null == this.perMessageDeflate.threshold) {\n\t    this.perMessageDeflate.threshold = 1024;\n\t  }\n\t\n\t  // SSL options for Node.js client\n\t  this.pfx = opts.pfx || null;\n\t  this.key = opts.key || null;\n\t  this.passphrase = opts.passphrase || null;\n\t  this.cert = opts.cert || null;\n\t  this.ca = opts.ca || null;\n\t  this.ciphers = opts.ciphers || null;\n\t  this.rejectUnauthorized = opts.rejectUnauthorized === undefined ? true : opts.rejectUnauthorized;\n\t  this.forceNode = !!opts.forceNode;\n\t\n\t  // detect ReactNative environment\n\t  this.isReactNative = (typeof navigator !== 'undefined' && typeof navigator.product === 'string' && navigator.product.toLowerCase() === 'reactnative');\n\t\n\t  // other options for Node.js or ReactNative client\n\t  if (typeof self === 'undefined' || this.isReactNative) {\n\t    if (opts.extraHeaders && Object.keys(opts.extraHeaders).length > 0) {\n\t      this.extraHeaders = opts.extraHeaders;\n\t    }\n\t\n\t    if (opts.localAddress) {\n\t      this.localAddress = opts.localAddress;\n\t    }\n\t  }\n\t\n\t  // set on handshake\n\t  this.id = null;\n\t  this.upgrades = null;\n\t  this.pingInterval = null;\n\t  this.pingTimeout = null;\n\t\n\t  // set on heartbeat\n\t  this.pingIntervalTimer = null;\n\t  this.pingTimeoutTimer = null;\n\t\n\t  this.open();\n\t}\n\t\n\tSocket.priorWebsocketSuccess = false;\n\t\n\t/**\n\t * Mix in `Emitter`.\n\t */\n\t\n\tEmitter(Socket.prototype);\n\t\n\t/**\n\t * Protocol version.\n\t *\n\t * @api public\n\t */\n\t\n\tSocket.protocol = parser.protocol; // this is an int\n\t\n\t/**\n\t * Expose deps for legacy compatibility\n\t * and standalone browser access.\n\t */\n\t\n\tSocket.Socket = Socket;\n\tSocket.Transport = __webpack_require__(23);\n\tSocket.transports = __webpack_require__(18);\n\tSocket.parser = __webpack_require__(24);\n\t\n\t/**\n\t * Creates transport of the given type.\n\t *\n\t * @param {String} transport name\n\t * @return {Transport}\n\t * @api private\n\t */\n\t\n\tSocket.prototype.createTransport = function (name) {\n\t  debug('creating transport \"%s\"', name);\n\t  var query = clone(this.query);\n\t\n\t  // append engine.io protocol identifier\n\t  query.EIO = parser.protocol;\n\t\n\t  // transport name\n\t  query.transport = name;\n\t\n\t  // per-transport options\n\t  var options = this.transportOptions[name] || {};\n\t\n\t  // session id if we already have one\n\t  if (this.id) query.sid = this.id;\n\t\n\t  var transport = new transports[name]({\n\t    query: query,\n\t    socket: this,\n\t    agent: options.agent || this.agent,\n\t    hostname: options.hostname || this.hostname,\n\t    port: options.port || this.port,\n\t    secure: options.secure || this.secure,\n\t    path: options.path || this.path,\n\t    forceJSONP: options.forceJSONP || this.forceJSONP,\n\t    jsonp: options.jsonp || this.jsonp,\n\t    forceBase64: options.forceBase64 || this.forceBase64,\n\t    enablesXDR: options.enablesXDR || this.enablesXDR,\n\t    withCredentials: options.withCredentials || this.withCredentials,\n\t    timestampRequests: options.timestampRequests || this.timestampRequests,\n\t    timestampParam: options.timestampParam || this.timestampParam,\n\t    policyPort: options.policyPort || this.policyPort,\n\t    pfx: options.pfx || this.pfx,\n\t    key: options.key || this.key,\n\t    passphrase: options.passphrase || this.passphrase,\n\t    cert: options.cert || this.cert,\n\t    ca: options.ca || this.ca,\n\t    ciphers: options.ciphers || this.ciphers,\n\t    rejectUnauthorized: options.rejectUnauthorized || this.rejectUnauthorized,\n\t    perMessageDeflate: options.perMessageDeflate || this.perMessageDeflate,\n\t    extraHeaders: options.extraHeaders || this.extraHeaders,\n\t    forceNode: options.forceNode || this.forceNode,\n\t    localAddress: options.localAddress || this.localAddress,\n\t    requestTimeout: options.requestTimeout || this.requestTimeout,\n\t    protocols: options.protocols || void (0),\n\t    isReactNative: this.isReactNative\n\t  });\n\t\n\t  return transport;\n\t};\n\t\n\tfunction clone (obj) {\n\t  var o = {};\n\t  for (var i in obj) {\n\t    if (obj.hasOwnProperty(i)) {\n\t      o[i] = obj[i];\n\t    }\n\t  }\n\t  return o;\n\t}\n\t\n\t/**\n\t * Initializes transport to use and starts probe.\n\t *\n\t * @api private\n\t */\n\tSocket.prototype.open = function () {\n\t  var transport;\n\t  if (this.rememberUpgrade && Socket.priorWebsocketSuccess && this.transports.indexOf('websocket') !== -1) {\n\t    transport = 'websocket';\n\t  } else if (0 === this.transports.length) {\n\t    // Emit error on next tick so it can be listened to\n\t    var self = this;\n\t    setTimeout(function () {\n\t      self.emit('error', 'No transports available');\n\t    }, 0);\n\t    return;\n\t  } else {\n\t    transport = this.transports[0];\n\t  }\n\t  this.readyState = 'opening';\n\t\n\t  // Retry with the next transport if the transport is disabled (jsonp: false)\n\t  try {\n\t    transport = this.createTransport(transport);\n\t  } catch (e) {\n\t    this.transports.shift();\n\t    this.open();\n\t    return;\n\t  }\n\t\n\t  transport.open();\n\t  this.setTransport(transport);\n\t};\n\t\n\t/**\n\t * Sets the current transport. Disables the existing one (if any).\n\t *\n\t * @api private\n\t */\n\t\n\tSocket.prototype.setTransport = function (transport) {\n\t  debug('setting transport %s', transport.name);\n\t  var self = this;\n\t\n\t  if (this.transport) {\n\t    debug('clearing existing transport %s', this.transport.name);\n\t    this.transport.removeAllListeners();\n\t  }\n\t\n\t  // set up transport\n\t  this.transport = transport;\n\t\n\t  // set up transport listeners\n\t  transport\n\t  .on('drain', function () {\n\t    self.onDrain();\n\t  })\n\t  .on('packet', function (packet) {\n\t    self.onPacket(packet);\n\t  })\n\t  .on('error', function (e) {\n\t    self.onError(e);\n\t  })\n\t  .on('close', function () {\n\t    self.onClose('transport close');\n\t  });\n\t};\n\t\n\t/**\n\t * Probes a transport.\n\t *\n\t * @param {String} transport name\n\t * @api private\n\t */\n\t\n\tSocket.prototype.probe = function (name) {\n\t  debug('probing transport \"%s\"', name);\n\t  var transport = this.createTransport(name, { probe: 1 });\n\t  var failed = false;\n\t  var self = this;\n\t\n\t  Socket.priorWebsocketSuccess = false;\n\t\n\t  function onTransportOpen () {\n\t    if (self.onlyBinaryUpgrades) {\n\t      var upgradeLosesBinary = !this.supportsBinary && self.transport.supportsBinary;\n\t      failed = failed || upgradeLosesBinary;\n\t    }\n\t    if (failed) return;\n\t\n\t    debug('probe transport \"%s\" opened', name);\n\t    transport.send([{ type: 'ping', data: 'probe' }]);\n\t    transport.once('packet', function (msg) {\n\t      if (failed) return;\n\t      if ('pong' === msg.type && 'probe' === msg.data) {\n\t        debug('probe transport \"%s\" pong', name);\n\t        self.upgrading = true;\n\t        self.emit('upgrading', transport);\n\t        if (!transport) return;\n\t        Socket.priorWebsocketSuccess = 'websocket' === transport.name;\n\t\n\t        debug('pausing current transport \"%s\"', self.transport.name);\n\t        self.transport.pause(function () {\n\t          if (failed) return;\n\t          if ('closed' === self.readyState) return;\n\t          debug('changing transport and sending upgrade packet');\n\t\n\t          cleanup();\n\t\n\t          self.setTransport(transport);\n\t          transport.send([{ type: 'upgrade' }]);\n\t          self.emit('upgrade', transport);\n\t          transport = null;\n\t          self.upgrading = false;\n\t          self.flush();\n\t        });\n\t      } else {\n\t        debug('probe transport \"%s\" failed', name);\n\t        var err = new Error('probe error');\n\t        err.transport = transport.name;\n\t        self.emit('upgradeError', err);\n\t      }\n\t    });\n\t  }\n\t\n\t  function freezeTransport () {\n\t    if (failed) return;\n\t\n\t    // Any callback called by transport should be ignored since now\n\t    failed = true;\n\t\n\t    cleanup();\n\t\n\t    transport.close();\n\t    transport = null;\n\t  }\n\t\n\t  // Handle any error that happens while probing\n\t  function onerror (err) {\n\t    var error = new Error('probe error: ' + err);\n\t    error.transport = transport.name;\n\t\n\t    freezeTransport();\n\t\n\t    debug('probe transport \"%s\" failed because of error: %s', name, err);\n\t\n\t    self.emit('upgradeError', error);\n\t  }\n\t\n\t  function onTransportClose () {\n\t    onerror('transport closed');\n\t  }\n\t\n\t  // When the socket is closed while we're probing\n\t  function onclose () {\n\t    onerror('socket closed');\n\t  }\n\t\n\t  // When the socket is upgraded while we're probing\n\t  function onupgrade (to) {\n\t    if (transport && to.name !== transport.name) {\n\t      debug('\"%s\" works - aborting \"%s\"', to.name, transport.name);\n\t      freezeTransport();\n\t    }\n\t  }\n\t\n\t  // Remove all listeners on the transport and on self\n\t  function cleanup () {\n\t    transport.removeListener('open', onTransportOpen);\n\t    transport.removeListener('error', onerror);\n\t    transport.removeListener('close', onTransportClose);\n\t    self.removeListener('close', onclose);\n\t    self.removeListener('upgrading', onupgrade);\n\t  }\n\t\n\t  transport.once('open', onTransportOpen);\n\t  transport.once('error', onerror);\n\t  transport.once('close', onTransportClose);\n\t\n\t  this.once('close', onclose);\n\t  this.once('upgrading', onupgrade);\n\t\n\t  transport.open();\n\t};\n\t\n\t/**\n\t * Called when connection is deemed open.\n\t *\n\t * @api public\n\t */\n\t\n\tSocket.prototype.onOpen = function () {\n\t  debug('socket open');\n\t  this.readyState = 'open';\n\t  Socket.priorWebsocketSuccess = 'websocket' === this.transport.name;\n\t  this.emit('open');\n\t  this.flush();\n\t\n\t  // we check for `readyState` in case an `open`\n\t  // listener already closed the socket\n\t  if ('open' === this.readyState && this.upgrade && this.transport.pause) {\n\t    debug('starting upgrade probes');\n\t    for (var i = 0, l = this.upgrades.length; i < l; i++) {\n\t      this.probe(this.upgrades[i]);\n\t    }\n\t  }\n\t};\n\t\n\t/**\n\t * Handles a packet.\n\t *\n\t * @api private\n\t */\n\t\n\tSocket.prototype.onPacket = function (packet) {\n\t  if ('opening' === this.readyState || 'open' === this.readyState ||\n\t      'closing' === this.readyState) {\n\t    debug('socket receive: type \"%s\", data \"%s\"', packet.type, packet.data);\n\t\n\t    this.emit('packet', packet);\n\t\n\t    // Socket is live - any packet counts\n\t    this.emit('heartbeat');\n\t\n\t    switch (packet.type) {\n\t      case 'open':\n\t        this.onHandshake(JSON.parse(packet.data));\n\t        break;\n\t\n\t      case 'pong':\n\t        this.setPing();\n\t        this.emit('pong');\n\t        break;\n\t\n\t      case 'error':\n\t        var err = new Error('server error');\n\t        err.code = packet.data;\n\t        this.onError(err);\n\t        break;\n\t\n\t      case 'message':\n\t        this.emit('data', packet.data);\n\t        this.emit('message', packet.data);\n\t        break;\n\t    }\n\t  } else {\n\t    debug('packet received with socket readyState \"%s\"', this.readyState);\n\t  }\n\t};\n\t\n\t/**\n\t * Called upon handshake completion.\n\t *\n\t * @param {Object} handshake obj\n\t * @api private\n\t */\n\t\n\tSocket.prototype.onHandshake = function (data) {\n\t  this.emit('handshake', data);\n\t  this.id = data.sid;\n\t  this.transport.query.sid = data.sid;\n\t  this.upgrades = this.filterUpgrades(data.upgrades);\n\t  this.pingInterval = data.pingInterval;\n\t  this.pingTimeout = data.pingTimeout;\n\t  this.onOpen();\n\t  // In case open handler closes socket\n\t  if ('closed' === this.readyState) return;\n\t  this.setPing();\n\t\n\t  // Prolong liveness of socket on heartbeat\n\t  this.removeListener('heartbeat', this.onHeartbeat);\n\t  this.on('heartbeat', this.onHeartbeat);\n\t};\n\t\n\t/**\n\t * Resets ping timeout.\n\t *\n\t * @api private\n\t */\n\t\n\tSocket.prototype.onHeartbeat = function (timeout) {\n\t  clearTimeout(this.pingTimeoutTimer);\n\t  var self = this;\n\t  self.pingTimeoutTimer = setTimeout(function () {\n\t    if ('closed' === self.readyState) return;\n\t    self.onClose('ping timeout');\n\t  }, timeout || (self.pingInterval + self.pingTimeout));\n\t};\n\t\n\t/**\n\t * Pings server every `this.pingInterval` and expects response\n\t * within `this.pingTimeout` or closes connection.\n\t *\n\t * @api private\n\t */\n\t\n\tSocket.prototype.setPing = function () {\n\t  var self = this;\n\t  clearTimeout(self.pingIntervalTimer);\n\t  self.pingIntervalTimer = setTimeout(function () {\n\t    debug('writing ping packet - expecting pong within %sms', self.pingTimeout);\n\t    self.ping();\n\t    self.onHeartbeat(self.pingTimeout);\n\t  }, self.pingInterval);\n\t};\n\t\n\t/**\n\t* Sends a ping packet.\n\t*\n\t* @api private\n\t*/\n\t\n\tSocket.prototype.ping = function () {\n\t  var self = this;\n\t  this.sendPacket('ping', function () {\n\t    self.emit('ping');\n\t  });\n\t};\n\t\n\t/**\n\t * Called on `drain` event\n\t *\n\t * @api private\n\t */\n\t\n\tSocket.prototype.onDrain = function () {\n\t  this.writeBuffer.splice(0, this.prevBufferLen);\n\t\n\t  // setting prevBufferLen = 0 is very important\n\t  // for example, when upgrading, upgrade packet is sent over,\n\t  // and a nonzero prevBufferLen could cause problems on `drain`\n\t  this.prevBufferLen = 0;\n\t\n\t  if (0 === this.writeBuffer.length) {\n\t    this.emit('drain');\n\t  } else {\n\t    this.flush();\n\t  }\n\t};\n\t\n\t/**\n\t * Flush write buffers.\n\t *\n\t * @api private\n\t */\n\t\n\tSocket.prototype.flush = function () {\n\t  if ('closed' !== this.readyState && this.transport.writable &&\n\t    !this.upgrading && this.writeBuffer.length) {\n\t    debug('flushing %d packets in socket', this.writeBuffer.length);\n\t    this.transport.send(this.writeBuffer);\n\t    // keep track of current length of writeBuffer\n\t    // splice writeBuffer and callbackBuffer on `drain`\n\t    this.prevBufferLen = this.writeBuffer.length;\n\t    this.emit('flush');\n\t  }\n\t};\n\t\n\t/**\n\t * Sends a message.\n\t *\n\t * @param {String} message.\n\t * @param {Function} callback function.\n\t * @param {Object} options.\n\t * @return {Socket} for chaining.\n\t * @api public\n\t */\n\t\n\tSocket.prototype.write =\n\tSocket.prototype.send = function (msg, options, fn) {\n\t  this.sendPacket('message', msg, options, fn);\n\t  return this;\n\t};\n\t\n\t/**\n\t * Sends a packet.\n\t *\n\t * @param {String} packet type.\n\t * @param {String} data.\n\t * @param {Object} options.\n\t * @param {Function} callback function.\n\t * @api private\n\t */\n\t\n\tSocket.prototype.sendPacket = function (type, data, options, fn) {\n\t  if ('function' === typeof data) {\n\t    fn = data;\n\t    data = undefined;\n\t  }\n\t\n\t  if ('function' === typeof options) {\n\t    fn = options;\n\t    options = null;\n\t  }\n\t\n\t  if ('closing' === this.readyState || 'closed' === this.readyState) {\n\t    return;\n\t  }\n\t\n\t  options = options || {};\n\t  options.compress = false !== options.compress;\n\t\n\t  var packet = {\n\t    type: type,\n\t    data: data,\n\t    options: options\n\t  };\n\t  this.emit('packetCreate', packet);\n\t  this.writeBuffer.push(packet);\n\t  if (fn) this.once('flush', fn);\n\t  this.flush();\n\t};\n\t\n\t/**\n\t * Closes the connection.\n\t *\n\t * @api private\n\t */\n\t\n\tSocket.prototype.close = function () {\n\t  if ('opening' === this.readyState || 'open' === this.readyState) {\n\t    this.readyState = 'closing';\n\t\n\t    var self = this;\n\t\n\t    if (this.writeBuffer.length) {\n\t      this.once('drain', function () {\n\t        if (this.upgrading) {\n\t          waitForUpgrade();\n\t        } else {\n\t          close();\n\t        }\n\t      });\n\t    } else if (this.upgrading) {\n\t      waitForUpgrade();\n\t    } else {\n\t      close();\n\t    }\n\t  }\n\t\n\t  function close () {\n\t    self.onClose('forced close');\n\t    debug('socket closing - telling transport to close');\n\t    self.transport.close();\n\t  }\n\t\n\t  function cleanupAndClose () {\n\t    self.removeListener('upgrade', cleanupAndClose);\n\t    self.removeListener('upgradeError', cleanupAndClose);\n\t    close();\n\t  }\n\t\n\t  function waitForUpgrade () {\n\t    // wait for upgrade to finish since we can't send packets while pausing a transport\n\t    self.once('upgrade', cleanupAndClose);\n\t    self.once('upgradeError', cleanupAndClose);\n\t  }\n\t\n\t  return this;\n\t};\n\t\n\t/**\n\t * Called upon transport error\n\t *\n\t * @api private\n\t */\n\t\n\tSocket.prototype.onError = function (err) {\n\t  debug('socket error %j', err);\n\t  Socket.priorWebsocketSuccess = false;\n\t  this.emit('error', err);\n\t  this.onClose('transport error', err);\n\t};\n\t\n\t/**\n\t * Called upon transport close.\n\t *\n\t * @api private\n\t */\n\t\n\tSocket.prototype.onClose = function (reason, desc) {\n\t  if ('opening' === this.readyState || 'open' === this.readyState || 'closing' === this.readyState) {\n\t    debug('socket close with reason: \"%s\"', reason);\n\t    var self = this;\n\t\n\t    // clear timers\n\t    clearTimeout(this.pingIntervalTimer);\n\t    clearTimeout(this.pingTimeoutTimer);\n\t\n\t    // stop event from firing again for transport\n\t    this.transport.removeAllListeners('close');\n\t\n\t    // ensure transport won't stay open\n\t    this.transport.close();\n\t\n\t    // ignore further transport communication\n\t    this.transport.removeAllListeners();\n\t\n\t    // set ready state\n\t    this.readyState = 'closed';\n\t\n\t    // clear session id\n\t    this.id = null;\n\t\n\t    // emit close event\n\t    this.emit('close', reason, desc);\n\t\n\t    // clean buffers after, so users can still\n\t    // grab the buffers on `close` event\n\t    self.writeBuffer = [];\n\t    self.prevBufferLen = 0;\n\t  }\n\t};\n\t\n\t/**\n\t * Filters upgrades, returning only those matching client transports.\n\t *\n\t * @param {Array} server upgrades\n\t * @api private\n\t *\n\t */\n\t\n\tSocket.prototype.filterUpgrades = function (upgrades) {\n\t  var filteredUpgrades = [];\n\t  for (var i = 0, j = upgrades.length; i < j; i++) {\n\t    if (~index(this.transports, upgrades[i])) filteredUpgrades.push(upgrades[i]);\n\t  }\n\t  return filteredUpgrades;\n\t};\n\n\n/***/ }),\n/* 18 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/**\n\t * Module dependencies\n\t */\n\t\n\tvar XMLHttpRequest = __webpack_require__(19);\n\tvar XHR = __webpack_require__(21);\n\tvar JSONP = __webpack_require__(35);\n\tvar websocket = __webpack_require__(36);\n\t\n\t/**\n\t * Export transports.\n\t */\n\t\n\texports.polling = polling;\n\texports.websocket = websocket;\n\t\n\t/**\n\t * Polling transport polymorphic constructor.\n\t * Decides on xhr vs jsonp based on feature detection.\n\t *\n\t * @api private\n\t */\n\t\n\tfunction polling (opts) {\n\t  var xhr;\n\t  var xd = false;\n\t  var xs = false;\n\t  var jsonp = false !== opts.jsonp;\n\t\n\t  if (typeof location !== 'undefined') {\n\t    var isSSL = 'https:' === location.protocol;\n\t    var port = location.port;\n\t\n\t    // some user agents have empty `location.port`\n\t    if (!port) {\n\t      port = isSSL ? 443 : 80;\n\t    }\n\t\n\t    xd = opts.hostname !== location.hostname || port !== opts.port;\n\t    xs = opts.secure !== isSSL;\n\t  }\n\t\n\t  opts.xdomain = xd;\n\t  opts.xscheme = xs;\n\t  xhr = new XMLHttpRequest(opts);\n\t\n\t  if ('open' in xhr && !opts.forceJSONP) {\n\t    return new XHR(opts);\n\t  } else {\n\t    if (!jsonp) throw new Error('JSONP disabled');\n\t    return new JSONP(opts);\n\t  }\n\t}\n\n\n/***/ }),\n/* 19 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t// browser shim for xmlhttprequest module\n\t\n\tvar hasCORS = __webpack_require__(20);\n\t\n\tmodule.exports = function (opts) {\n\t  var xdomain = opts.xdomain;\n\t\n\t  // scheme must be same when usign XDomainRequest\n\t  // http://blogs.msdn.com/b/ieinternals/archive/2010/05/13/xdomainrequest-restrictions-limitations-and-workarounds.aspx\n\t  var xscheme = opts.xscheme;\n\t\n\t  // XDomainRequest has a flow of not sending cookie, therefore it should be disabled as a default.\n\t  // https://github.com/Automattic/engine.io-client/pull/217\n\t  var enablesXDR = opts.enablesXDR;\n\t\n\t  // XMLHttpRequest can be disabled on IE\n\t  try {\n\t    if ('undefined' !== typeof XMLHttpRequest && (!xdomain || hasCORS)) {\n\t      return new XMLHttpRequest();\n\t    }\n\t  } catch (e) { }\n\t\n\t  // Use XDomainRequest for IE8 if enablesXDR is true\n\t  // because loading bar keeps flashing when using jsonp-polling\n\t  // https://github.com/yujiosaka/socke.io-ie8-loading-example\n\t  try {\n\t    if ('undefined' !== typeof XDomainRequest && !xscheme && enablesXDR) {\n\t      return new XDomainRequest();\n\t    }\n\t  } catch (e) { }\n\t\n\t  if (!xdomain) {\n\t    try {\n\t      return new self[['Active'].concat('Object').join('X')]('Microsoft.XMLHTTP');\n\t    } catch (e) { }\n\t  }\n\t};\n\n\n/***/ }),\n/* 20 */\n/***/ (function(module, exports) {\n\n\t\n\t/**\n\t * Module exports.\n\t *\n\t * Logic borrowed from Modernizr:\n\t *\n\t *   - https://github.com/Modernizr/Modernizr/blob/master/feature-detects/cors.js\n\t */\n\t\n\ttry {\n\t  module.exports = typeof XMLHttpRequest !== 'undefined' &&\n\t    'withCredentials' in new XMLHttpRequest();\n\t} catch (err) {\n\t  // if XMLHttp support is disabled in IE then it will throw\n\t  // when trying to create\n\t  module.exports = false;\n\t}\n\n\n/***/ }),\n/* 21 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/* global attachEvent */\n\t\n\t/**\n\t * Module requirements.\n\t */\n\t\n\tvar XMLHttpRequest = __webpack_require__(19);\n\tvar Polling = __webpack_require__(22);\n\tvar Emitter = __webpack_require__(11);\n\tvar inherit = __webpack_require__(33);\n\tvar debug = __webpack_require__(3)('engine.io-client:polling-xhr');\n\t\n\t/**\n\t * Module exports.\n\t */\n\t\n\tmodule.exports = XHR;\n\tmodule.exports.Request = Request;\n\t\n\t/**\n\t * Empty function\n\t */\n\t\n\tfunction empty () {}\n\t\n\t/**\n\t * XHR Polling constructor.\n\t *\n\t * @param {Object} opts\n\t * @api public\n\t */\n\t\n\tfunction XHR (opts) {\n\t  Polling.call(this, opts);\n\t  this.requestTimeout = opts.requestTimeout;\n\t  this.extraHeaders = opts.extraHeaders;\n\t\n\t  if (typeof location !== 'undefined') {\n\t    var isSSL = 'https:' === location.protocol;\n\t    var port = location.port;\n\t\n\t    // some user agents have empty `location.port`\n\t    if (!port) {\n\t      port = isSSL ? 443 : 80;\n\t    }\n\t\n\t    this.xd = (typeof location !== 'undefined' && opts.hostname !== location.hostname) ||\n\t      port !== opts.port;\n\t    this.xs = opts.secure !== isSSL;\n\t  }\n\t}\n\t\n\t/**\n\t * Inherits from Polling.\n\t */\n\t\n\tinherit(XHR, Polling);\n\t\n\t/**\n\t * XHR supports binary\n\t */\n\t\n\tXHR.prototype.supportsBinary = true;\n\t\n\t/**\n\t * Creates a request.\n\t *\n\t * @param {String} method\n\t * @api private\n\t */\n\t\n\tXHR.prototype.request = function (opts) {\n\t  opts = opts || {};\n\t  opts.uri = this.uri();\n\t  opts.xd = this.xd;\n\t  opts.xs = this.xs;\n\t  opts.agent = this.agent || false;\n\t  opts.supportsBinary = this.supportsBinary;\n\t  opts.enablesXDR = this.enablesXDR;\n\t  opts.withCredentials = this.withCredentials;\n\t\n\t  // SSL options for Node.js client\n\t  opts.pfx = this.pfx;\n\t  opts.key = this.key;\n\t  opts.passphrase = this.passphrase;\n\t  opts.cert = this.cert;\n\t  opts.ca = this.ca;\n\t  opts.ciphers = this.ciphers;\n\t  opts.rejectUnauthorized = this.rejectUnauthorized;\n\t  opts.requestTimeout = this.requestTimeout;\n\t\n\t  // other options for Node.js client\n\t  opts.extraHeaders = this.extraHeaders;\n\t\n\t  return new Request(opts);\n\t};\n\t\n\t/**\n\t * Sends data.\n\t *\n\t * @param {String} data to send.\n\t * @param {Function} called upon flush.\n\t * @api private\n\t */\n\t\n\tXHR.prototype.doWrite = function (data, fn) {\n\t  var isBinary = typeof data !== 'string' && data !== undefined;\n\t  var req = this.request({ method: 'POST', data: data, isBinary: isBinary });\n\t  var self = this;\n\t  req.on('success', fn);\n\t  req.on('error', function (err) {\n\t    self.onError('xhr post error', err);\n\t  });\n\t  this.sendXhr = req;\n\t};\n\t\n\t/**\n\t * Starts a poll cycle.\n\t *\n\t * @api private\n\t */\n\t\n\tXHR.prototype.doPoll = function () {\n\t  debug('xhr poll');\n\t  var req = this.request();\n\t  var self = this;\n\t  req.on('data', function (data) {\n\t    self.onData(data);\n\t  });\n\t  req.on('error', function (err) {\n\t    self.onError('xhr poll error', err);\n\t  });\n\t  this.pollXhr = req;\n\t};\n\t\n\t/**\n\t * Request constructor\n\t *\n\t * @param {Object} options\n\t * @api public\n\t */\n\t\n\tfunction Request (opts) {\n\t  this.method = opts.method || 'GET';\n\t  this.uri = opts.uri;\n\t  this.xd = !!opts.xd;\n\t  this.xs = !!opts.xs;\n\t  this.async = false !== opts.async;\n\t  this.data = undefined !== opts.data ? opts.data : null;\n\t  this.agent = opts.agent;\n\t  this.isBinary = opts.isBinary;\n\t  this.supportsBinary = opts.supportsBinary;\n\t  this.enablesXDR = opts.enablesXDR;\n\t  this.withCredentials = opts.withCredentials;\n\t  this.requestTimeout = opts.requestTimeout;\n\t\n\t  // SSL options for Node.js client\n\t  this.pfx = opts.pfx;\n\t  this.key = opts.key;\n\t  this.passphrase = opts.passphrase;\n\t  this.cert = opts.cert;\n\t  this.ca = opts.ca;\n\t  this.ciphers = opts.ciphers;\n\t  this.rejectUnauthorized = opts.rejectUnauthorized;\n\t\n\t  // other options for Node.js client\n\t  this.extraHeaders = opts.extraHeaders;\n\t\n\t  this.create();\n\t}\n\t\n\t/**\n\t * Mix in `Emitter`.\n\t */\n\t\n\tEmitter(Request.prototype);\n\t\n\t/**\n\t * Creates the XHR object and sends the request.\n\t *\n\t * @api private\n\t */\n\t\n\tRequest.prototype.create = function () {\n\t  var opts = { agent: this.agent, xdomain: this.xd, xscheme: this.xs, enablesXDR: this.enablesXDR };\n\t\n\t  // SSL options for Node.js client\n\t  opts.pfx = this.pfx;\n\t  opts.key = this.key;\n\t  opts.passphrase = this.passphrase;\n\t  opts.cert = this.cert;\n\t  opts.ca = this.ca;\n\t  opts.ciphers = this.ciphers;\n\t  opts.rejectUnauthorized = this.rejectUnauthorized;\n\t\n\t  var xhr = this.xhr = new XMLHttpRequest(opts);\n\t  var self = this;\n\t\n\t  try {\n\t    debug('xhr open %s: %s', this.method, this.uri);\n\t    xhr.open(this.method, this.uri, this.async);\n\t    try {\n\t      if (this.extraHeaders) {\n\t        xhr.setDisableHeaderCheck && xhr.setDisableHeaderCheck(true);\n\t        for (var i in this.extraHeaders) {\n\t          if (this.extraHeaders.hasOwnProperty(i)) {\n\t            xhr.setRequestHeader(i, this.extraHeaders[i]);\n\t          }\n\t        }\n\t      }\n\t    } catch (e) {}\n\t\n\t    if ('POST' === this.method) {\n\t      try {\n\t        if (this.isBinary) {\n\t          xhr.setRequestHeader('Content-type', 'application/octet-stream');\n\t        } else {\n\t          xhr.setRequestHeader('Content-type', 'text/plain;charset=UTF-8');\n\t        }\n\t      } catch (e) {}\n\t    }\n\t\n\t    try {\n\t      xhr.setRequestHeader('Accept', '*/*');\n\t    } catch (e) {}\n\t\n\t    // ie6 check\n\t    if ('withCredentials' in xhr) {\n\t      xhr.withCredentials = this.withCredentials;\n\t    }\n\t\n\t    if (this.requestTimeout) {\n\t      xhr.timeout = this.requestTimeout;\n\t    }\n\t\n\t    if (this.hasXDR()) {\n\t      xhr.onload = function () {\n\t        self.onLoad();\n\t      };\n\t      xhr.onerror = function () {\n\t        self.onError(xhr.responseText);\n\t      };\n\t    } else {\n\t      xhr.onreadystatechange = function () {\n\t        if (xhr.readyState === 2) {\n\t          try {\n\t            var contentType = xhr.getResponseHeader('Content-Type');\n\t            if (self.supportsBinary && contentType === 'application/octet-stream' || contentType === 'application/octet-stream; charset=UTF-8') {\n\t              xhr.responseType = 'arraybuffer';\n\t            }\n\t          } catch (e) {}\n\t        }\n\t        if (4 !== xhr.readyState) return;\n\t        if (200 === xhr.status || 1223 === xhr.status) {\n\t          self.onLoad();\n\t        } else {\n\t          // make sure the `error` event handler that's user-set\n\t          // does not throw in the same tick and gets caught here\n\t          setTimeout(function () {\n\t            self.onError(typeof xhr.status === 'number' ? xhr.status : 0);\n\t          }, 0);\n\t        }\n\t      };\n\t    }\n\t\n\t    debug('xhr data %s', this.data);\n\t    xhr.send(this.data);\n\t  } catch (e) {\n\t    // Need to defer since .create() is called directly fhrom the constructor\n\t    // and thus the 'error' event can only be only bound *after* this exception\n\t    // occurs.  Therefore, also, we cannot throw here at all.\n\t    setTimeout(function () {\n\t      self.onError(e);\n\t    }, 0);\n\t    return;\n\t  }\n\t\n\t  if (typeof document !== 'undefined') {\n\t    this.index = Request.requestsCount++;\n\t    Request.requests[this.index] = this;\n\t  }\n\t};\n\t\n\t/**\n\t * Called upon successful response.\n\t *\n\t * @api private\n\t */\n\t\n\tRequest.prototype.onSuccess = function () {\n\t  this.emit('success');\n\t  this.cleanup();\n\t};\n\t\n\t/**\n\t * Called if we have data.\n\t *\n\t * @api private\n\t */\n\t\n\tRequest.prototype.onData = function (data) {\n\t  this.emit('data', data);\n\t  this.onSuccess();\n\t};\n\t\n\t/**\n\t * Called upon error.\n\t *\n\t * @api private\n\t */\n\t\n\tRequest.prototype.onError = function (err) {\n\t  this.emit('error', err);\n\t  this.cleanup(true);\n\t};\n\t\n\t/**\n\t * Cleans up house.\n\t *\n\t * @api private\n\t */\n\t\n\tRequest.prototype.cleanup = function (fromError) {\n\t  if ('undefined' === typeof this.xhr || null === this.xhr) {\n\t    return;\n\t  }\n\t  // xmlhttprequest\n\t  if (this.hasXDR()) {\n\t    this.xhr.onload = this.xhr.onerror = empty;\n\t  } else {\n\t    this.xhr.onreadystatechange = empty;\n\t  }\n\t\n\t  if (fromError) {\n\t    try {\n\t      this.xhr.abort();\n\t    } catch (e) {}\n\t  }\n\t\n\t  if (typeof document !== 'undefined') {\n\t    delete Request.requests[this.index];\n\t  }\n\t\n\t  this.xhr = null;\n\t};\n\t\n\t/**\n\t * Called upon load.\n\t *\n\t * @api private\n\t */\n\t\n\tRequest.prototype.onLoad = function () {\n\t  var data;\n\t  try {\n\t    var contentType;\n\t    try {\n\t      contentType = this.xhr.getResponseHeader('Content-Type');\n\t    } catch (e) {}\n\t    if (contentType === 'application/octet-stream' || contentType === 'application/octet-stream; charset=UTF-8') {\n\t      data = this.xhr.response || this.xhr.responseText;\n\t    } else {\n\t      data = this.xhr.responseText;\n\t    }\n\t  } catch (e) {\n\t    this.onError(e);\n\t  }\n\t  if (null != data) {\n\t    this.onData(data);\n\t  }\n\t};\n\t\n\t/**\n\t * Check if it has XDomainRequest.\n\t *\n\t * @api private\n\t */\n\t\n\tRequest.prototype.hasXDR = function () {\n\t  return typeof XDomainRequest !== 'undefined' && !this.xs && this.enablesXDR;\n\t};\n\t\n\t/**\n\t * Aborts the request.\n\t *\n\t * @api public\n\t */\n\t\n\tRequest.prototype.abort = function () {\n\t  this.cleanup();\n\t};\n\t\n\t/**\n\t * Aborts pending requests when unloading the window. This is needed to prevent\n\t * memory leaks (e.g. when using IE) and to ensure that no spurious error is\n\t * emitted.\n\t */\n\t\n\tRequest.requestsCount = 0;\n\tRequest.requests = {};\n\t\n\tif (typeof document !== 'undefined') {\n\t  if (typeof attachEvent === 'function') {\n\t    attachEvent('onunload', unloadHandler);\n\t  } else if (typeof addEventListener === 'function') {\n\t    var terminationEvent = 'onpagehide' in self ? 'pagehide' : 'unload';\n\t    addEventListener(terminationEvent, unloadHandler, false);\n\t  }\n\t}\n\t\n\tfunction unloadHandler () {\n\t  for (var i in Request.requests) {\n\t    if (Request.requests.hasOwnProperty(i)) {\n\t      Request.requests[i].abort();\n\t    }\n\t  }\n\t}\n\n\n/***/ }),\n/* 22 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/**\n\t * Module dependencies.\n\t */\n\t\n\tvar Transport = __webpack_require__(23);\n\tvar parseqs = __webpack_require__(32);\n\tvar parser = __webpack_require__(24);\n\tvar inherit = __webpack_require__(33);\n\tvar yeast = __webpack_require__(34);\n\tvar debug = __webpack_require__(3)('engine.io-client:polling');\n\t\n\t/**\n\t * Module exports.\n\t */\n\t\n\tmodule.exports = Polling;\n\t\n\t/**\n\t * Is XHR2 supported?\n\t */\n\t\n\tvar hasXHR2 = (function () {\n\t  var XMLHttpRequest = __webpack_require__(19);\n\t  var xhr = new XMLHttpRequest({ xdomain: false });\n\t  return null != xhr.responseType;\n\t})();\n\t\n\t/**\n\t * Polling interface.\n\t *\n\t * @param {Object} opts\n\t * @api private\n\t */\n\t\n\tfunction Polling (opts) {\n\t  var forceBase64 = (opts && opts.forceBase64);\n\t  if (!hasXHR2 || forceBase64) {\n\t    this.supportsBinary = false;\n\t  }\n\t  Transport.call(this, opts);\n\t}\n\t\n\t/**\n\t * Inherits from Transport.\n\t */\n\t\n\tinherit(Polling, Transport);\n\t\n\t/**\n\t * Transport name.\n\t */\n\t\n\tPolling.prototype.name = 'polling';\n\t\n\t/**\n\t * Opens the socket (triggers polling). We write a PING message to determine\n\t * when the transport is open.\n\t *\n\t * @api private\n\t */\n\t\n\tPolling.prototype.doOpen = function () {\n\t  this.poll();\n\t};\n\t\n\t/**\n\t * Pauses polling.\n\t *\n\t * @param {Function} callback upon buffers are flushed and transport is paused\n\t * @api private\n\t */\n\t\n\tPolling.prototype.pause = function (onPause) {\n\t  var self = this;\n\t\n\t  this.readyState = 'pausing';\n\t\n\t  function pause () {\n\t    debug('paused');\n\t    self.readyState = 'paused';\n\t    onPause();\n\t  }\n\t\n\t  if (this.polling || !this.writable) {\n\t    var total = 0;\n\t\n\t    if (this.polling) {\n\t      debug('we are currently polling - waiting to pause');\n\t      total++;\n\t      this.once('pollComplete', function () {\n\t        debug('pre-pause polling complete');\n\t        --total || pause();\n\t      });\n\t    }\n\t\n\t    if (!this.writable) {\n\t      debug('we are currently writing - waiting to pause');\n\t      total++;\n\t      this.once('drain', function () {\n\t        debug('pre-pause writing complete');\n\t        --total || pause();\n\t      });\n\t    }\n\t  } else {\n\t    pause();\n\t  }\n\t};\n\t\n\t/**\n\t * Starts polling cycle.\n\t *\n\t * @api public\n\t */\n\t\n\tPolling.prototype.poll = function () {\n\t  debug('polling');\n\t  this.polling = true;\n\t  this.doPoll();\n\t  this.emit('poll');\n\t};\n\t\n\t/**\n\t * Overloads onData to detect payloads.\n\t *\n\t * @api private\n\t */\n\t\n\tPolling.prototype.onData = function (data) {\n\t  var self = this;\n\t  debug('polling got data %s', data);\n\t  var callback = function (packet, index, total) {\n\t    // if its the first message we consider the transport open\n\t    if ('opening' === self.readyState) {\n\t      self.onOpen();\n\t    }\n\t\n\t    // if its a close packet, we close the ongoing requests\n\t    if ('close' === packet.type) {\n\t      self.onClose();\n\t      return false;\n\t    }\n\t\n\t    // otherwise bypass onData and handle the message\n\t    self.onPacket(packet);\n\t  };\n\t\n\t  // decode payload\n\t  parser.decodePayload(data, this.socket.binaryType, callback);\n\t\n\t  // if an event did not trigger closing\n\t  if ('closed' !== this.readyState) {\n\t    // if we got data we're not polling\n\t    this.polling = false;\n\t    this.emit('pollComplete');\n\t\n\t    if ('open' === this.readyState) {\n\t      this.poll();\n\t    } else {\n\t      debug('ignoring poll - transport state \"%s\"', this.readyState);\n\t    }\n\t  }\n\t};\n\t\n\t/**\n\t * For polling, send a close packet.\n\t *\n\t * @api private\n\t */\n\t\n\tPolling.prototype.doClose = function () {\n\t  var self = this;\n\t\n\t  function close () {\n\t    debug('writing close packet');\n\t    self.write([{ type: 'close' }]);\n\t  }\n\t\n\t  if ('open' === this.readyState) {\n\t    debug('transport open - closing');\n\t    close();\n\t  } else {\n\t    // in case we're trying to close while\n\t    // handshaking is in progress (GH-164)\n\t    debug('transport not open - deferring close');\n\t    this.once('open', close);\n\t  }\n\t};\n\t\n\t/**\n\t * Writes a packets payload.\n\t *\n\t * @param {Array} data packets\n\t * @param {Function} drain callback\n\t * @api private\n\t */\n\t\n\tPolling.prototype.write = function (packets) {\n\t  var self = this;\n\t  this.writable = false;\n\t  var callbackfn = function () {\n\t    self.writable = true;\n\t    self.emit('drain');\n\t  };\n\t\n\t  parser.encodePayload(packets, this.supportsBinary, function (data) {\n\t    self.doWrite(data, callbackfn);\n\t  });\n\t};\n\t\n\t/**\n\t * Generates uri for connection.\n\t *\n\t * @api private\n\t */\n\t\n\tPolling.prototype.uri = function () {\n\t  var query = this.query || {};\n\t  var schema = this.secure ? 'https' : 'http';\n\t  var port = '';\n\t\n\t  // cache busting is forced\n\t  if (false !== this.timestampRequests) {\n\t    query[this.timestampParam] = yeast();\n\t  }\n\t\n\t  if (!this.supportsBinary && !query.sid) {\n\t    query.b64 = 1;\n\t  }\n\t\n\t  query = parseqs.encode(query);\n\t\n\t  // avoid port if default for schema\n\t  if (this.port && (('https' === schema && Number(this.port) !== 443) ||\n\t     ('http' === schema && Number(this.port) !== 80))) {\n\t    port = ':' + this.port;\n\t  }\n\t\n\t  // prepend ? to query\n\t  if (query.length) {\n\t    query = '?' + query;\n\t  }\n\t\n\t  var ipv6 = this.hostname.indexOf(':') !== -1;\n\t  return schema + '://' + (ipv6 ? '[' + this.hostname + ']' : this.hostname) + port + this.path + query;\n\t};\n\n\n/***/ }),\n/* 23 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/**\n\t * Module dependencies.\n\t */\n\t\n\tvar parser = __webpack_require__(24);\n\tvar Emitter = __webpack_require__(11);\n\t\n\t/**\n\t * Module exports.\n\t */\n\t\n\tmodule.exports = Transport;\n\t\n\t/**\n\t * Transport abstract constructor.\n\t *\n\t * @param {Object} options.\n\t * @api private\n\t */\n\t\n\tfunction Transport (opts) {\n\t  this.path = opts.path;\n\t  this.hostname = opts.hostname;\n\t  this.port = opts.port;\n\t  this.secure = opts.secure;\n\t  this.query = opts.query;\n\t  this.timestampParam = opts.timestampParam;\n\t  this.timestampRequests = opts.timestampRequests;\n\t  this.readyState = '';\n\t  this.agent = opts.agent || false;\n\t  this.socket = opts.socket;\n\t  this.enablesXDR = opts.enablesXDR;\n\t  this.withCredentials = opts.withCredentials;\n\t\n\t  // SSL options for Node.js client\n\t  this.pfx = opts.pfx;\n\t  this.key = opts.key;\n\t  this.passphrase = opts.passphrase;\n\t  this.cert = opts.cert;\n\t  this.ca = opts.ca;\n\t  this.ciphers = opts.ciphers;\n\t  this.rejectUnauthorized = opts.rejectUnauthorized;\n\t  this.forceNode = opts.forceNode;\n\t\n\t  // results of ReactNative environment detection\n\t  this.isReactNative = opts.isReactNative;\n\t\n\t  // other options for Node.js client\n\t  this.extraHeaders = opts.extraHeaders;\n\t  this.localAddress = opts.localAddress;\n\t}\n\t\n\t/**\n\t * Mix in `Emitter`.\n\t */\n\t\n\tEmitter(Transport.prototype);\n\t\n\t/**\n\t * Emits an error.\n\t *\n\t * @param {String} str\n\t * @return {Transport} for chaining\n\t * @api public\n\t */\n\t\n\tTransport.prototype.onError = function (msg, desc) {\n\t  var err = new Error(msg);\n\t  err.type = 'TransportError';\n\t  err.description = desc;\n\t  this.emit('error', err);\n\t  return this;\n\t};\n\t\n\t/**\n\t * Opens the transport.\n\t *\n\t * @api public\n\t */\n\t\n\tTransport.prototype.open = function () {\n\t  if ('closed' === this.readyState || '' === this.readyState) {\n\t    this.readyState = 'opening';\n\t    this.doOpen();\n\t  }\n\t\n\t  return this;\n\t};\n\t\n\t/**\n\t * Closes the transport.\n\t *\n\t * @api private\n\t */\n\t\n\tTransport.prototype.close = function () {\n\t  if ('opening' === this.readyState || 'open' === this.readyState) {\n\t    this.doClose();\n\t    this.onClose();\n\t  }\n\t\n\t  return this;\n\t};\n\t\n\t/**\n\t * Sends multiple packets.\n\t *\n\t * @param {Array} packets\n\t * @api private\n\t */\n\t\n\tTransport.prototype.send = function (packets) {\n\t  if ('open' === this.readyState) {\n\t    this.write(packets);\n\t  } else {\n\t    throw new Error('Transport not open');\n\t  }\n\t};\n\t\n\t/**\n\t * Called upon open\n\t *\n\t * @api private\n\t */\n\t\n\tTransport.prototype.onOpen = function () {\n\t  this.readyState = 'open';\n\t  this.writable = true;\n\t  this.emit('open');\n\t};\n\t\n\t/**\n\t * Called with data.\n\t *\n\t * @param {String} data\n\t * @api private\n\t */\n\t\n\tTransport.prototype.onData = function (data) {\n\t  var packet = parser.decodePacket(data, this.socket.binaryType);\n\t  this.onPacket(packet);\n\t};\n\t\n\t/**\n\t * Called with a decoded packet.\n\t */\n\t\n\tTransport.prototype.onPacket = function (packet) {\n\t  this.emit('packet', packet);\n\t};\n\t\n\t/**\n\t * Called upon close.\n\t *\n\t * @api private\n\t */\n\t\n\tTransport.prototype.onClose = function () {\n\t  this.readyState = 'closed';\n\t  this.emit('close');\n\t};\n\n\n/***/ }),\n/* 24 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/**\n\t * Module dependencies.\n\t */\n\t\n\tvar keys = __webpack_require__(25);\n\tvar hasBinary = __webpack_require__(26);\n\tvar sliceBuffer = __webpack_require__(27);\n\tvar after = __webpack_require__(28);\n\tvar utf8 = __webpack_require__(29);\n\t\n\tvar base64encoder;\n\tif (typeof ArrayBuffer !== 'undefined') {\n\t  base64encoder = __webpack_require__(30);\n\t}\n\t\n\t/**\n\t * Check if we are running an android browser. That requires us to use\n\t * ArrayBuffer with polling transports...\n\t *\n\t * http://ghinda.net/jpeg-blob-ajax-android/\n\t */\n\t\n\tvar isAndroid = typeof navigator !== 'undefined' && /Android/i.test(navigator.userAgent);\n\t\n\t/**\n\t * Check if we are running in PhantomJS.\n\t * Uploading a Blob with PhantomJS does not work correctly, as reported here:\n\t * https://github.com/ariya/phantomjs/issues/11395\n\t * @type boolean\n\t */\n\tvar isPhantomJS = typeof navigator !== 'undefined' && /PhantomJS/i.test(navigator.userAgent);\n\t\n\t/**\n\t * When true, avoids using Blobs to encode payloads.\n\t * @type boolean\n\t */\n\tvar dontSendBlobs = isAndroid || isPhantomJS;\n\t\n\t/**\n\t * Current protocol version.\n\t */\n\t\n\texports.protocol = 3;\n\t\n\t/**\n\t * Packet types.\n\t */\n\t\n\tvar packets = exports.packets = {\n\t    open:     0    // non-ws\n\t  , close:    1    // non-ws\n\t  , ping:     2\n\t  , pong:     3\n\t  , message:  4\n\t  , upgrade:  5\n\t  , noop:     6\n\t};\n\t\n\tvar packetslist = keys(packets);\n\t\n\t/**\n\t * Premade error packet.\n\t */\n\t\n\tvar err = { type: 'error', data: 'parser error' };\n\t\n\t/**\n\t * Create a blob api even for blob builder when vendor prefixes exist\n\t */\n\t\n\tvar Blob = __webpack_require__(31);\n\t\n\t/**\n\t * Encodes a packet.\n\t *\n\t *     <packet type id> [ <data> ]\n\t *\n\t * Example:\n\t *\n\t *     5hello world\n\t *     3\n\t *     4\n\t *\n\t * Binary is encoded in an identical principle\n\t *\n\t * @api private\n\t */\n\t\n\texports.encodePacket = function (packet, supportsBinary, utf8encode, callback) {\n\t  if (typeof supportsBinary === 'function') {\n\t    callback = supportsBinary;\n\t    supportsBinary = false;\n\t  }\n\t\n\t  if (typeof utf8encode === 'function') {\n\t    callback = utf8encode;\n\t    utf8encode = null;\n\t  }\n\t\n\t  var data = (packet.data === undefined)\n\t    ? undefined\n\t    : packet.data.buffer || packet.data;\n\t\n\t  if (typeof ArrayBuffer !== 'undefined' && data instanceof ArrayBuffer) {\n\t    return encodeArrayBuffer(packet, supportsBinary, callback);\n\t  } else if (typeof Blob !== 'undefined' && data instanceof Blob) {\n\t    return encodeBlob(packet, supportsBinary, callback);\n\t  }\n\t\n\t  // might be an object with { base64: true, data: dataAsBase64String }\n\t  if (data && data.base64) {\n\t    return encodeBase64Object(packet, callback);\n\t  }\n\t\n\t  // Sending data as a utf-8 string\n\t  var encoded = packets[packet.type];\n\t\n\t  // data fragment is optional\n\t  if (undefined !== packet.data) {\n\t    encoded += utf8encode ? utf8.encode(String(packet.data), { strict: false }) : String(packet.data);\n\t  }\n\t\n\t  return callback('' + encoded);\n\t\n\t};\n\t\n\tfunction encodeBase64Object(packet, callback) {\n\t  // packet data is an object { base64: true, data: dataAsBase64String }\n\t  var message = 'b' + exports.packets[packet.type] + packet.data.data;\n\t  return callback(message);\n\t}\n\t\n\t/**\n\t * Encode packet helpers for binary types\n\t */\n\t\n\tfunction encodeArrayBuffer(packet, supportsBinary, callback) {\n\t  if (!supportsBinary) {\n\t    return exports.encodeBase64Packet(packet, callback);\n\t  }\n\t\n\t  var data = packet.data;\n\t  var contentArray = new Uint8Array(data);\n\t  var resultBuffer = new Uint8Array(1 + data.byteLength);\n\t\n\t  resultBuffer[0] = packets[packet.type];\n\t  for (var i = 0; i < contentArray.length; i++) {\n\t    resultBuffer[i+1] = contentArray[i];\n\t  }\n\t\n\t  return callback(resultBuffer.buffer);\n\t}\n\t\n\tfunction encodeBlobAsArrayBuffer(packet, supportsBinary, callback) {\n\t  if (!supportsBinary) {\n\t    return exports.encodeBase64Packet(packet, callback);\n\t  }\n\t\n\t  var fr = new FileReader();\n\t  fr.onload = function() {\n\t    exports.encodePacket({ type: packet.type, data: fr.result }, supportsBinary, true, callback);\n\t  };\n\t  return fr.readAsArrayBuffer(packet.data);\n\t}\n\t\n\tfunction encodeBlob(packet, supportsBinary, callback) {\n\t  if (!supportsBinary) {\n\t    return exports.encodeBase64Packet(packet, callback);\n\t  }\n\t\n\t  if (dontSendBlobs) {\n\t    return encodeBlobAsArrayBuffer(packet, supportsBinary, callback);\n\t  }\n\t\n\t  var length = new Uint8Array(1);\n\t  length[0] = packets[packet.type];\n\t  var blob = new Blob([length.buffer, packet.data]);\n\t\n\t  return callback(blob);\n\t}\n\t\n\t/**\n\t * Encodes a packet with binary data in a base64 string\n\t *\n\t * @param {Object} packet, has `type` and `data`\n\t * @return {String} base64 encoded message\n\t */\n\t\n\texports.encodeBase64Packet = function(packet, callback) {\n\t  var message = 'b' + exports.packets[packet.type];\n\t  if (typeof Blob !== 'undefined' && packet.data instanceof Blob) {\n\t    var fr = new FileReader();\n\t    fr.onload = function() {\n\t      var b64 = fr.result.split(',')[1];\n\t      callback(message + b64);\n\t    };\n\t    return fr.readAsDataURL(packet.data);\n\t  }\n\t\n\t  var b64data;\n\t  try {\n\t    b64data = String.fromCharCode.apply(null, new Uint8Array(packet.data));\n\t  } catch (e) {\n\t    // iPhone Safari doesn't let you apply with typed arrays\n\t    var typed = new Uint8Array(packet.data);\n\t    var basic = new Array(typed.length);\n\t    for (var i = 0; i < typed.length; i++) {\n\t      basic[i] = typed[i];\n\t    }\n\t    b64data = String.fromCharCode.apply(null, basic);\n\t  }\n\t  message += btoa(b64data);\n\t  return callback(message);\n\t};\n\t\n\t/**\n\t * Decodes a packet. Changes format to Blob if requested.\n\t *\n\t * @return {Object} with `type` and `data` (if any)\n\t * @api private\n\t */\n\t\n\texports.decodePacket = function (data, binaryType, utf8decode) {\n\t  if (data === undefined) {\n\t    return err;\n\t  }\n\t  // String data\n\t  if (typeof data === 'string') {\n\t    if (data.charAt(0) === 'b') {\n\t      return exports.decodeBase64Packet(data.substr(1), binaryType);\n\t    }\n\t\n\t    if (utf8decode) {\n\t      data = tryDecode(data);\n\t      if (data === false) {\n\t        return err;\n\t      }\n\t    }\n\t    var type = data.charAt(0);\n\t\n\t    if (Number(type) != type || !packetslist[type]) {\n\t      return err;\n\t    }\n\t\n\t    if (data.length > 1) {\n\t      return { type: packetslist[type], data: data.substring(1) };\n\t    } else {\n\t      return { type: packetslist[type] };\n\t    }\n\t  }\n\t\n\t  var asArray = new Uint8Array(data);\n\t  var type = asArray[0];\n\t  var rest = sliceBuffer(data, 1);\n\t  if (Blob && binaryType === 'blob') {\n\t    rest = new Blob([rest]);\n\t  }\n\t  return { type: packetslist[type], data: rest };\n\t};\n\t\n\tfunction tryDecode(data) {\n\t  try {\n\t    data = utf8.decode(data, { strict: false });\n\t  } catch (e) {\n\t    return false;\n\t  }\n\t  return data;\n\t}\n\t\n\t/**\n\t * Decodes a packet encoded in a base64 string\n\t *\n\t * @param {String} base64 encoded message\n\t * @return {Object} with `type` and `data` (if any)\n\t */\n\t\n\texports.decodeBase64Packet = function(msg, binaryType) {\n\t  var type = packetslist[msg.charAt(0)];\n\t  if (!base64encoder) {\n\t    return { type: type, data: { base64: true, data: msg.substr(1) } };\n\t  }\n\t\n\t  var data = base64encoder.decode(msg.substr(1));\n\t\n\t  if (binaryType === 'blob' && Blob) {\n\t    data = new Blob([data]);\n\t  }\n\t\n\t  return { type: type, data: data };\n\t};\n\t\n\t/**\n\t * Encodes multiple messages (payload).\n\t *\n\t *     <length>:data\n\t *\n\t * Example:\n\t *\n\t *     11:hello world2:hi\n\t *\n\t * If any contents are binary, they will be encoded as base64 strings. Base64\n\t * encoded strings are marked with a b before the length specifier\n\t *\n\t * @param {Array} packets\n\t * @api private\n\t */\n\t\n\texports.encodePayload = function (packets, supportsBinary, callback) {\n\t  if (typeof supportsBinary === 'function') {\n\t    callback = supportsBinary;\n\t    supportsBinary = null;\n\t  }\n\t\n\t  var isBinary = hasBinary(packets);\n\t\n\t  if (supportsBinary && isBinary) {\n\t    if (Blob && !dontSendBlobs) {\n\t      return exports.encodePayloadAsBlob(packets, callback);\n\t    }\n\t\n\t    return exports.encodePayloadAsArrayBuffer(packets, callback);\n\t  }\n\t\n\t  if (!packets.length) {\n\t    return callback('0:');\n\t  }\n\t\n\t  function setLengthHeader(message) {\n\t    return message.length + ':' + message;\n\t  }\n\t\n\t  function encodeOne(packet, doneCallback) {\n\t    exports.encodePacket(packet, !isBinary ? false : supportsBinary, false, function(message) {\n\t      doneCallback(null, setLengthHeader(message));\n\t    });\n\t  }\n\t\n\t  map(packets, encodeOne, function(err, results) {\n\t    return callback(results.join(''));\n\t  });\n\t};\n\t\n\t/**\n\t * Async array map using after\n\t */\n\t\n\tfunction map(ary, each, done) {\n\t  var result = new Array(ary.length);\n\t  var next = after(ary.length, done);\n\t\n\t  var eachWithIndex = function(i, el, cb) {\n\t    each(el, function(error, msg) {\n\t      result[i] = msg;\n\t      cb(error, result);\n\t    });\n\t  };\n\t\n\t  for (var i = 0; i < ary.length; i++) {\n\t    eachWithIndex(i, ary[i], next);\n\t  }\n\t}\n\t\n\t/*\n\t * Decodes data when a payload is maybe expected. Possible binary contents are\n\t * decoded from their base64 representation\n\t *\n\t * @param {String} data, callback method\n\t * @api public\n\t */\n\t\n\texports.decodePayload = function (data, binaryType, callback) {\n\t  if (typeof data !== 'string') {\n\t    return exports.decodePayloadAsBinary(data, binaryType, callback);\n\t  }\n\t\n\t  if (typeof binaryType === 'function') {\n\t    callback = binaryType;\n\t    binaryType = null;\n\t  }\n\t\n\t  var packet;\n\t  if (data === '') {\n\t    // parser error - ignoring payload\n\t    return callback(err, 0, 1);\n\t  }\n\t\n\t  var length = '', n, msg;\n\t\n\t  for (var i = 0, l = data.length; i < l; i++) {\n\t    var chr = data.charAt(i);\n\t\n\t    if (chr !== ':') {\n\t      length += chr;\n\t      continue;\n\t    }\n\t\n\t    if (length === '' || (length != (n = Number(length)))) {\n\t      // parser error - ignoring payload\n\t      return callback(err, 0, 1);\n\t    }\n\t\n\t    msg = data.substr(i + 1, n);\n\t\n\t    if (length != msg.length) {\n\t      // parser error - ignoring payload\n\t      return callback(err, 0, 1);\n\t    }\n\t\n\t    if (msg.length) {\n\t      packet = exports.decodePacket(msg, binaryType, false);\n\t\n\t      if (err.type === packet.type && err.data === packet.data) {\n\t        // parser error in individual packet - ignoring payload\n\t        return callback(err, 0, 1);\n\t      }\n\t\n\t      var ret = callback(packet, i + n, l);\n\t      if (false === ret) return;\n\t    }\n\t\n\t    // advance cursor\n\t    i += n;\n\t    length = '';\n\t  }\n\t\n\t  if (length !== '') {\n\t    // parser error - ignoring payload\n\t    return callback(err, 0, 1);\n\t  }\n\t\n\t};\n\t\n\t/**\n\t * Encodes multiple messages (payload) as binary.\n\t *\n\t * <1 = binary, 0 = string><number from 0-9><number from 0-9>[...]<number\n\t * 255><data>\n\t *\n\t * Example:\n\t * 1 3 255 1 2 3, if the binary contents are interpreted as 8 bit integers\n\t *\n\t * @param {Array} packets\n\t * @return {ArrayBuffer} encoded payload\n\t * @api private\n\t */\n\t\n\texports.encodePayloadAsArrayBuffer = function(packets, callback) {\n\t  if (!packets.length) {\n\t    return callback(new ArrayBuffer(0));\n\t  }\n\t\n\t  function encodeOne(packet, doneCallback) {\n\t    exports.encodePacket(packet, true, true, function(data) {\n\t      return doneCallback(null, data);\n\t    });\n\t  }\n\t\n\t  map(packets, encodeOne, function(err, encodedPackets) {\n\t    var totalLength = encodedPackets.reduce(function(acc, p) {\n\t      var len;\n\t      if (typeof p === 'string'){\n\t        len = p.length;\n\t      } else {\n\t        len = p.byteLength;\n\t      }\n\t      return acc + len.toString().length + len + 2; // string/binary identifier + separator = 2\n\t    }, 0);\n\t\n\t    var resultArray = new Uint8Array(totalLength);\n\t\n\t    var bufferIndex = 0;\n\t    encodedPackets.forEach(function(p) {\n\t      var isString = typeof p === 'string';\n\t      var ab = p;\n\t      if (isString) {\n\t        var view = new Uint8Array(p.length);\n\t        for (var i = 0; i < p.length; i++) {\n\t          view[i] = p.charCodeAt(i);\n\t        }\n\t        ab = view.buffer;\n\t      }\n\t\n\t      if (isString) { // not true binary\n\t        resultArray[bufferIndex++] = 0;\n\t      } else { // true binary\n\t        resultArray[bufferIndex++] = 1;\n\t      }\n\t\n\t      var lenStr = ab.byteLength.toString();\n\t      for (var i = 0; i < lenStr.length; i++) {\n\t        resultArray[bufferIndex++] = parseInt(lenStr[i]);\n\t      }\n\t      resultArray[bufferIndex++] = 255;\n\t\n\t      var view = new Uint8Array(ab);\n\t      for (var i = 0; i < view.length; i++) {\n\t        resultArray[bufferIndex++] = view[i];\n\t      }\n\t    });\n\t\n\t    return callback(resultArray.buffer);\n\t  });\n\t};\n\t\n\t/**\n\t * Encode as Blob\n\t */\n\t\n\texports.encodePayloadAsBlob = function(packets, callback) {\n\t  function encodeOne(packet, doneCallback) {\n\t    exports.encodePacket(packet, true, true, function(encoded) {\n\t      var binaryIdentifier = new Uint8Array(1);\n\t      binaryIdentifier[0] = 1;\n\t      if (typeof encoded === 'string') {\n\t        var view = new Uint8Array(encoded.length);\n\t        for (var i = 0; i < encoded.length; i++) {\n\t          view[i] = encoded.charCodeAt(i);\n\t        }\n\t        encoded = view.buffer;\n\t        binaryIdentifier[0] = 0;\n\t      }\n\t\n\t      var len = (encoded instanceof ArrayBuffer)\n\t        ? encoded.byteLength\n\t        : encoded.size;\n\t\n\t      var lenStr = len.toString();\n\t      var lengthAry = new Uint8Array(lenStr.length + 1);\n\t      for (var i = 0; i < lenStr.length; i++) {\n\t        lengthAry[i] = parseInt(lenStr[i]);\n\t      }\n\t      lengthAry[lenStr.length] = 255;\n\t\n\t      if (Blob) {\n\t        var blob = new Blob([binaryIdentifier.buffer, lengthAry.buffer, encoded]);\n\t        doneCallback(null, blob);\n\t      }\n\t    });\n\t  }\n\t\n\t  map(packets, encodeOne, function(err, results) {\n\t    return callback(new Blob(results));\n\t  });\n\t};\n\t\n\t/*\n\t * Decodes data when a payload is maybe expected. Strings are decoded by\n\t * interpreting each byte as a key code for entries marked to start with 0. See\n\t * description of encodePayloadAsBinary\n\t *\n\t * @param {ArrayBuffer} data, callback method\n\t * @api public\n\t */\n\t\n\texports.decodePayloadAsBinary = function (data, binaryType, callback) {\n\t  if (typeof binaryType === 'function') {\n\t    callback = binaryType;\n\t    binaryType = null;\n\t  }\n\t\n\t  var bufferTail = data;\n\t  var buffers = [];\n\t\n\t  while (bufferTail.byteLength > 0) {\n\t    var tailArray = new Uint8Array(bufferTail);\n\t    var isString = tailArray[0] === 0;\n\t    var msgLength = '';\n\t\n\t    for (var i = 1; ; i++) {\n\t      if (tailArray[i] === 255) break;\n\t\n\t      // 310 = char length of Number.MAX_VALUE\n\t      if (msgLength.length > 310) {\n\t        return callback(err, 0, 1);\n\t      }\n\t\n\t      msgLength += tailArray[i];\n\t    }\n\t\n\t    bufferTail = sliceBuffer(bufferTail, 2 + msgLength.length);\n\t    msgLength = parseInt(msgLength);\n\t\n\t    var msg = sliceBuffer(bufferTail, 0, msgLength);\n\t    if (isString) {\n\t      try {\n\t        msg = String.fromCharCode.apply(null, new Uint8Array(msg));\n\t      } catch (e) {\n\t        // iPhone Safari doesn't let you apply to typed arrays\n\t        var typed = new Uint8Array(msg);\n\t        msg = '';\n\t        for (var i = 0; i < typed.length; i++) {\n\t          msg += String.fromCharCode(typed[i]);\n\t        }\n\t      }\n\t    }\n\t\n\t    buffers.push(msg);\n\t    bufferTail = sliceBuffer(bufferTail, msgLength);\n\t  }\n\t\n\t  var total = buffers.length;\n\t  buffers.forEach(function(buffer, i) {\n\t    callback(exports.decodePacket(buffer, binaryType, true), i, total);\n\t  });\n\t};\n\n\n/***/ }),\n/* 25 */\n/***/ (function(module, exports) {\n\n\t\n\t/**\n\t * Gets the keys for an object.\n\t *\n\t * @return {Array} keys\n\t * @api private\n\t */\n\t\n\tmodule.exports = Object.keys || function keys (obj){\n\t  var arr = [];\n\t  var has = Object.prototype.hasOwnProperty;\n\t\n\t  for (var i in obj) {\n\t    if (has.call(obj, i)) {\n\t      arr.push(i);\n\t    }\n\t  }\n\t  return arr;\n\t};\n\n\n/***/ }),\n/* 26 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/* global Blob File */\n\t\n\t/*\n\t * Module requirements.\n\t */\n\t\n\tvar isArray = __webpack_require__(13);\n\t\n\tvar toString = Object.prototype.toString;\n\tvar withNativeBlob = typeof Blob === 'function' ||\n\t                        typeof Blob !== 'undefined' && toString.call(Blob) === '[object BlobConstructor]';\n\tvar withNativeFile = typeof File === 'function' ||\n\t                        typeof File !== 'undefined' && toString.call(File) === '[object FileConstructor]';\n\t\n\t/**\n\t * Module exports.\n\t */\n\t\n\tmodule.exports = hasBinary;\n\t\n\t/**\n\t * Checks for binary data.\n\t *\n\t * Supports Buffer, ArrayBuffer, Blob and File.\n\t *\n\t * @param {Object} anything\n\t * @api public\n\t */\n\t\n\tfunction hasBinary (obj) {\n\t  if (!obj || typeof obj !== 'object') {\n\t    return false;\n\t  }\n\t\n\t  if (isArray(obj)) {\n\t    for (var i = 0, l = obj.length; i < l; i++) {\n\t      if (hasBinary(obj[i])) {\n\t        return true;\n\t      }\n\t    }\n\t    return false;\n\t  }\n\t\n\t  if ((typeof Buffer === 'function' && Buffer.isBuffer && Buffer.isBuffer(obj)) ||\n\t    (typeof ArrayBuffer === 'function' && obj instanceof ArrayBuffer) ||\n\t    (withNativeBlob && obj instanceof Blob) ||\n\t    (withNativeFile && obj instanceof File)\n\t  ) {\n\t    return true;\n\t  }\n\t\n\t  // see: https://github.com/Automattic/has-binary/pull/4\n\t  if (obj.toJSON && typeof obj.toJSON === 'function' && arguments.length === 1) {\n\t    return hasBinary(obj.toJSON(), true);\n\t  }\n\t\n\t  for (var key in obj) {\n\t    if (Object.prototype.hasOwnProperty.call(obj, key) && hasBinary(obj[key])) {\n\t      return true;\n\t    }\n\t  }\n\t\n\t  return false;\n\t}\n\n\n/***/ }),\n/* 27 */\n/***/ (function(module, exports) {\n\n\t/**\n\t * An abstraction for slicing an arraybuffer even when\n\t * ArrayBuffer.prototype.slice is not supported\n\t *\n\t * @api public\n\t */\n\t\n\tmodule.exports = function(arraybuffer, start, end) {\n\t  var bytes = arraybuffer.byteLength;\n\t  start = start || 0;\n\t  end = end || bytes;\n\t\n\t  if (arraybuffer.slice) { return arraybuffer.slice(start, end); }\n\t\n\t  if (start < 0) { start += bytes; }\n\t  if (end < 0) { end += bytes; }\n\t  if (end > bytes) { end = bytes; }\n\t\n\t  if (start >= bytes || start >= end || bytes === 0) {\n\t    return new ArrayBuffer(0);\n\t  }\n\t\n\t  var abv = new Uint8Array(arraybuffer);\n\t  var result = new Uint8Array(end - start);\n\t  for (var i = start, ii = 0; i < end; i++, ii++) {\n\t    result[ii] = abv[i];\n\t  }\n\t  return result.buffer;\n\t};\n\n\n/***/ }),\n/* 28 */\n/***/ (function(module, exports) {\n\n\tmodule.exports = after\n\t\n\tfunction after(count, callback, err_cb) {\n\t    var bail = false\n\t    err_cb = err_cb || noop\n\t    proxy.count = count\n\t\n\t    return (count === 0) ? callback() : proxy\n\t\n\t    function proxy(err, result) {\n\t        if (proxy.count <= 0) {\n\t            throw new Error('after called too many times')\n\t        }\n\t        --proxy.count\n\t\n\t        // after first error, rest are passed to err_cb\n\t        if (err) {\n\t            bail = true\n\t            callback(err)\n\t            // future error callbacks will go to error handler\n\t            callback = err_cb\n\t        } else if (proxy.count === 0 && !bail) {\n\t            callback(null, result)\n\t        }\n\t    }\n\t}\n\t\n\tfunction noop() {}\n\n\n/***/ }),\n/* 29 */\n/***/ (function(module, exports) {\n\n\t/*! https://mths.be/utf8js v2.1.2 by @mathias */\n\t\n\tvar stringFromCharCode = String.fromCharCode;\n\t\n\t// Taken from https://mths.be/punycode\n\tfunction ucs2decode(string) {\n\t\tvar output = [];\n\t\tvar counter = 0;\n\t\tvar length = string.length;\n\t\tvar value;\n\t\tvar extra;\n\t\twhile (counter < length) {\n\t\t\tvalue = string.charCodeAt(counter++);\n\t\t\tif (value >= 0xD800 && value <= 0xDBFF && counter < length) {\n\t\t\t\t// high surrogate, and there is a next character\n\t\t\t\textra = string.charCodeAt(counter++);\n\t\t\t\tif ((extra & 0xFC00) == 0xDC00) { // low surrogate\n\t\t\t\t\toutput.push(((value & 0x3FF) << 10) + (extra & 0x3FF) + 0x10000);\n\t\t\t\t} else {\n\t\t\t\t\t// unmatched surrogate; only append this code unit, in case the next\n\t\t\t\t\t// code unit is the high surrogate of a surrogate pair\n\t\t\t\t\toutput.push(value);\n\t\t\t\t\tcounter--;\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\toutput.push(value);\n\t\t\t}\n\t\t}\n\t\treturn output;\n\t}\n\t\n\t// Taken from https://mths.be/punycode\n\tfunction ucs2encode(array) {\n\t\tvar length = array.length;\n\t\tvar index = -1;\n\t\tvar value;\n\t\tvar output = '';\n\t\twhile (++index < length) {\n\t\t\tvalue = array[index];\n\t\t\tif (value > 0xFFFF) {\n\t\t\t\tvalue -= 0x10000;\n\t\t\t\toutput += stringFromCharCode(value >>> 10 & 0x3FF | 0xD800);\n\t\t\t\tvalue = 0xDC00 | value & 0x3FF;\n\t\t\t}\n\t\t\toutput += stringFromCharCode(value);\n\t\t}\n\t\treturn output;\n\t}\n\t\n\tfunction checkScalarValue(codePoint, strict) {\n\t\tif (codePoint >= 0xD800 && codePoint <= 0xDFFF) {\n\t\t\tif (strict) {\n\t\t\t\tthrow Error(\n\t\t\t\t\t'Lone surrogate U+' + codePoint.toString(16).toUpperCase() +\n\t\t\t\t\t' is not a scalar value'\n\t\t\t\t);\n\t\t\t}\n\t\t\treturn false;\n\t\t}\n\t\treturn true;\n\t}\n\t/*--------------------------------------------------------------------------*/\n\t\n\tfunction createByte(codePoint, shift) {\n\t\treturn stringFromCharCode(((codePoint >> shift) & 0x3F) | 0x80);\n\t}\n\t\n\tfunction encodeCodePoint(codePoint, strict) {\n\t\tif ((codePoint & 0xFFFFFF80) == 0) { // 1-byte sequence\n\t\t\treturn stringFromCharCode(codePoint);\n\t\t}\n\t\tvar symbol = '';\n\t\tif ((codePoint & 0xFFFFF800) == 0) { // 2-byte sequence\n\t\t\tsymbol = stringFromCharCode(((codePoint >> 6) & 0x1F) | 0xC0);\n\t\t}\n\t\telse if ((codePoint & 0xFFFF0000) == 0) { // 3-byte sequence\n\t\t\tif (!checkScalarValue(codePoint, strict)) {\n\t\t\t\tcodePoint = 0xFFFD;\n\t\t\t}\n\t\t\tsymbol = stringFromCharCode(((codePoint >> 12) & 0x0F) | 0xE0);\n\t\t\tsymbol += createByte(codePoint, 6);\n\t\t}\n\t\telse if ((codePoint & 0xFFE00000) == 0) { // 4-byte sequence\n\t\t\tsymbol = stringFromCharCode(((codePoint >> 18) & 0x07) | 0xF0);\n\t\t\tsymbol += createByte(codePoint, 12);\n\t\t\tsymbol += createByte(codePoint, 6);\n\t\t}\n\t\tsymbol += stringFromCharCode((codePoint & 0x3F) | 0x80);\n\t\treturn symbol;\n\t}\n\t\n\tfunction utf8encode(string, opts) {\n\t\topts = opts || {};\n\t\tvar strict = false !== opts.strict;\n\t\n\t\tvar codePoints = ucs2decode(string);\n\t\tvar length = codePoints.length;\n\t\tvar index = -1;\n\t\tvar codePoint;\n\t\tvar byteString = '';\n\t\twhile (++index < length) {\n\t\t\tcodePoint = codePoints[index];\n\t\t\tbyteString += encodeCodePoint(codePoint, strict);\n\t\t}\n\t\treturn byteString;\n\t}\n\t\n\t/*--------------------------------------------------------------------------*/\n\t\n\tfunction readContinuationByte() {\n\t\tif (byteIndex >= byteCount) {\n\t\t\tthrow Error('Invalid byte index');\n\t\t}\n\t\n\t\tvar continuationByte = byteArray[byteIndex] & 0xFF;\n\t\tbyteIndex++;\n\t\n\t\tif ((continuationByte & 0xC0) == 0x80) {\n\t\t\treturn continuationByte & 0x3F;\n\t\t}\n\t\n\t\t// If we end up here, it’s not a continuation byte\n\t\tthrow Error('Invalid continuation byte');\n\t}\n\t\n\tfunction decodeSymbol(strict) {\n\t\tvar byte1;\n\t\tvar byte2;\n\t\tvar byte3;\n\t\tvar byte4;\n\t\tvar codePoint;\n\t\n\t\tif (byteIndex > byteCount) {\n\t\t\tthrow Error('Invalid byte index');\n\t\t}\n\t\n\t\tif (byteIndex == byteCount) {\n\t\t\treturn false;\n\t\t}\n\t\n\t\t// Read first byte\n\t\tbyte1 = byteArray[byteIndex] & 0xFF;\n\t\tbyteIndex++;\n\t\n\t\t// 1-byte sequence (no continuation bytes)\n\t\tif ((byte1 & 0x80) == 0) {\n\t\t\treturn byte1;\n\t\t}\n\t\n\t\t// 2-byte sequence\n\t\tif ((byte1 & 0xE0) == 0xC0) {\n\t\t\tbyte2 = readContinuationByte();\n\t\t\tcodePoint = ((byte1 & 0x1F) << 6) | byte2;\n\t\t\tif (codePoint >= 0x80) {\n\t\t\t\treturn codePoint;\n\t\t\t} else {\n\t\t\t\tthrow Error('Invalid continuation byte');\n\t\t\t}\n\t\t}\n\t\n\t\t// 3-byte sequence (may include unpaired surrogates)\n\t\tif ((byte1 & 0xF0) == 0xE0) {\n\t\t\tbyte2 = readContinuationByte();\n\t\t\tbyte3 = readContinuationByte();\n\t\t\tcodePoint = ((byte1 & 0x0F) << 12) | (byte2 << 6) | byte3;\n\t\t\tif (codePoint >= 0x0800) {\n\t\t\t\treturn checkScalarValue(codePoint, strict) ? codePoint : 0xFFFD;\n\t\t\t} else {\n\t\t\t\tthrow Error('Invalid continuation byte');\n\t\t\t}\n\t\t}\n\t\n\t\t// 4-byte sequence\n\t\tif ((byte1 & 0xF8) == 0xF0) {\n\t\t\tbyte2 = readContinuationByte();\n\t\t\tbyte3 = readContinuationByte();\n\t\t\tbyte4 = readContinuationByte();\n\t\t\tcodePoint = ((byte1 & 0x07) << 0x12) | (byte2 << 0x0C) |\n\t\t\t\t(byte3 << 0x06) | byte4;\n\t\t\tif (codePoint >= 0x010000 && codePoint <= 0x10FFFF) {\n\t\t\t\treturn codePoint;\n\t\t\t}\n\t\t}\n\t\n\t\tthrow Error('Invalid UTF-8 detected');\n\t}\n\t\n\tvar byteArray;\n\tvar byteCount;\n\tvar byteIndex;\n\tfunction utf8decode(byteString, opts) {\n\t\topts = opts || {};\n\t\tvar strict = false !== opts.strict;\n\t\n\t\tbyteArray = ucs2decode(byteString);\n\t\tbyteCount = byteArray.length;\n\t\tbyteIndex = 0;\n\t\tvar codePoints = [];\n\t\tvar tmp;\n\t\twhile ((tmp = decodeSymbol(strict)) !== false) {\n\t\t\tcodePoints.push(tmp);\n\t\t}\n\t\treturn ucs2encode(codePoints);\n\t}\n\t\n\tmodule.exports = {\n\t\tversion: '2.1.2',\n\t\tencode: utf8encode,\n\t\tdecode: utf8decode\n\t};\n\n\n/***/ }),\n/* 30 */\n/***/ (function(module, exports) {\n\n\t/*\n\t * base64-arraybuffer\n\t * https://github.com/niklasvh/base64-arraybuffer\n\t *\n\t * Copyright (c) 2012 Niklas von Hertzen\n\t * Licensed under the MIT license.\n\t */\n\t(function(){\n\t  \"use strict\";\n\t\n\t  var chars = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\";\n\t\n\t  // Use a lookup table to find the index.\n\t  var lookup = new Uint8Array(256);\n\t  for (var i = 0; i < chars.length; i++) {\n\t    lookup[chars.charCodeAt(i)] = i;\n\t  }\n\t\n\t  exports.encode = function(arraybuffer) {\n\t    var bytes = new Uint8Array(arraybuffer),\n\t    i, len = bytes.length, base64 = \"\";\n\t\n\t    for (i = 0; i < len; i+=3) {\n\t      base64 += chars[bytes[i] >> 2];\n\t      base64 += chars[((bytes[i] & 3) << 4) | (bytes[i + 1] >> 4)];\n\t      base64 += chars[((bytes[i + 1] & 15) << 2) | (bytes[i + 2] >> 6)];\n\t      base64 += chars[bytes[i + 2] & 63];\n\t    }\n\t\n\t    if ((len % 3) === 2) {\n\t      base64 = base64.substring(0, base64.length - 1) + \"=\";\n\t    } else if (len % 3 === 1) {\n\t      base64 = base64.substring(0, base64.length - 2) + \"==\";\n\t    }\n\t\n\t    return base64;\n\t  };\n\t\n\t  exports.decode =  function(base64) {\n\t    var bufferLength = base64.length * 0.75,\n\t    len = base64.length, i, p = 0,\n\t    encoded1, encoded2, encoded3, encoded4;\n\t\n\t    if (base64[base64.length - 1] === \"=\") {\n\t      bufferLength--;\n\t      if (base64[base64.length - 2] === \"=\") {\n\t        bufferLength--;\n\t      }\n\t    }\n\t\n\t    var arraybuffer = new ArrayBuffer(bufferLength),\n\t    bytes = new Uint8Array(arraybuffer);\n\t\n\t    for (i = 0; i < len; i+=4) {\n\t      encoded1 = lookup[base64.charCodeAt(i)];\n\t      encoded2 = lookup[base64.charCodeAt(i+1)];\n\t      encoded3 = lookup[base64.charCodeAt(i+2)];\n\t      encoded4 = lookup[base64.charCodeAt(i+3)];\n\t\n\t      bytes[p++] = (encoded1 << 2) | (encoded2 >> 4);\n\t      bytes[p++] = ((encoded2 & 15) << 4) | (encoded3 >> 2);\n\t      bytes[p++] = ((encoded3 & 3) << 6) | (encoded4 & 63);\n\t    }\n\t\n\t    return arraybuffer;\n\t  };\n\t})();\n\n\n/***/ }),\n/* 31 */\n/***/ (function(module, exports) {\n\n\t/**\r\n\t * Create a blob builder even when vendor prefixes exist\r\n\t */\r\n\t\r\n\tvar BlobBuilder = typeof BlobBuilder !== 'undefined' ? BlobBuilder :\r\n\t  typeof WebKitBlobBuilder !== 'undefined' ? WebKitBlobBuilder :\r\n\t  typeof MSBlobBuilder !== 'undefined' ? MSBlobBuilder :\r\n\t  typeof MozBlobBuilder !== 'undefined' ? MozBlobBuilder : \r\n\t  false;\r\n\t\r\n\t/**\r\n\t * Check if Blob constructor is supported\r\n\t */\r\n\t\r\n\tvar blobSupported = (function() {\r\n\t  try {\r\n\t    var a = new Blob(['hi']);\r\n\t    return a.size === 2;\r\n\t  } catch(e) {\r\n\t    return false;\r\n\t  }\r\n\t})();\r\n\t\r\n\t/**\r\n\t * Check if Blob constructor supports ArrayBufferViews\r\n\t * Fails in Safari 6, so we need to map to ArrayBuffers there.\r\n\t */\r\n\t\r\n\tvar blobSupportsArrayBufferView = blobSupported && (function() {\r\n\t  try {\r\n\t    var b = new Blob([new Uint8Array([1,2])]);\r\n\t    return b.size === 2;\r\n\t  } catch(e) {\r\n\t    return false;\r\n\t  }\r\n\t})();\r\n\t\r\n\t/**\r\n\t * Check if BlobBuilder is supported\r\n\t */\r\n\t\r\n\tvar blobBuilderSupported = BlobBuilder\r\n\t  && BlobBuilder.prototype.append\r\n\t  && BlobBuilder.prototype.getBlob;\r\n\t\r\n\t/**\r\n\t * Helper function that maps ArrayBufferViews to ArrayBuffers\r\n\t * Used by BlobBuilder constructor and old browsers that didn't\r\n\t * support it in the Blob constructor.\r\n\t */\r\n\t\r\n\tfunction mapArrayBufferViews(ary) {\r\n\t  return ary.map(function(chunk) {\r\n\t    if (chunk.buffer instanceof ArrayBuffer) {\r\n\t      var buf = chunk.buffer;\r\n\t\r\n\t      // if this is a subarray, make a copy so we only\r\n\t      // include the subarray region from the underlying buffer\r\n\t      if (chunk.byteLength !== buf.byteLength) {\r\n\t        var copy = new Uint8Array(chunk.byteLength);\r\n\t        copy.set(new Uint8Array(buf, chunk.byteOffset, chunk.byteLength));\r\n\t        buf = copy.buffer;\r\n\t      }\r\n\t\r\n\t      return buf;\r\n\t    }\r\n\t\r\n\t    return chunk;\r\n\t  });\r\n\t}\r\n\t\r\n\tfunction BlobBuilderConstructor(ary, options) {\r\n\t  options = options || {};\r\n\t\r\n\t  var bb = new BlobBuilder();\r\n\t  mapArrayBufferViews(ary).forEach(function(part) {\r\n\t    bb.append(part);\r\n\t  });\r\n\t\r\n\t  return (options.type) ? bb.getBlob(options.type) : bb.getBlob();\r\n\t};\r\n\t\r\n\tfunction BlobConstructor(ary, options) {\r\n\t  return new Blob(mapArrayBufferViews(ary), options || {});\r\n\t};\r\n\t\r\n\tif (typeof Blob !== 'undefined') {\r\n\t  BlobBuilderConstructor.prototype = Blob.prototype;\r\n\t  BlobConstructor.prototype = Blob.prototype;\r\n\t}\r\n\t\r\n\tmodule.exports = (function() {\r\n\t  if (blobSupported) {\r\n\t    return blobSupportsArrayBufferView ? Blob : BlobConstructor;\r\n\t  } else if (blobBuilderSupported) {\r\n\t    return BlobBuilderConstructor;\r\n\t  } else {\r\n\t    return undefined;\r\n\t  }\r\n\t})();\r\n\n\n/***/ }),\n/* 32 */\n/***/ (function(module, exports) {\n\n\t/**\r\n\t * Compiles a querystring\r\n\t * Returns string representation of the object\r\n\t *\r\n\t * @param {Object}\r\n\t * @api private\r\n\t */\r\n\t\r\n\texports.encode = function (obj) {\r\n\t  var str = '';\r\n\t\r\n\t  for (var i in obj) {\r\n\t    if (obj.hasOwnProperty(i)) {\r\n\t      if (str.length) str += '&';\r\n\t      str += encodeURIComponent(i) + '=' + encodeURIComponent(obj[i]);\r\n\t    }\r\n\t  }\r\n\t\r\n\t  return str;\r\n\t};\r\n\t\r\n\t/**\r\n\t * Parses a simple querystring into an object\r\n\t *\r\n\t * @param {String} qs\r\n\t * @api private\r\n\t */\r\n\t\r\n\texports.decode = function(qs){\r\n\t  var qry = {};\r\n\t  var pairs = qs.split('&');\r\n\t  for (var i = 0, l = pairs.length; i < l; i++) {\r\n\t    var pair = pairs[i].split('=');\r\n\t    qry[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);\r\n\t  }\r\n\t  return qry;\r\n\t};\r\n\n\n/***/ }),\n/* 33 */\n/***/ (function(module, exports) {\n\n\t\n\tmodule.exports = function(a, b){\n\t  var fn = function(){};\n\t  fn.prototype = b.prototype;\n\t  a.prototype = new fn;\n\t  a.prototype.constructor = a;\n\t};\n\n/***/ }),\n/* 34 */\n/***/ (function(module, exports) {\n\n\t'use strict';\n\t\n\tvar alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_'.split('')\n\t  , length = 64\n\t  , map = {}\n\t  , seed = 0\n\t  , i = 0\n\t  , prev;\n\t\n\t/**\n\t * Return a string representing the specified number.\n\t *\n\t * @param {Number} num The number to convert.\n\t * @returns {String} The string representation of the number.\n\t * @api public\n\t */\n\tfunction encode(num) {\n\t  var encoded = '';\n\t\n\t  do {\n\t    encoded = alphabet[num % length] + encoded;\n\t    num = Math.floor(num / length);\n\t  } while (num > 0);\n\t\n\t  return encoded;\n\t}\n\t\n\t/**\n\t * Return the integer value specified by the given string.\n\t *\n\t * @param {String} str The string to convert.\n\t * @returns {Number} The integer value represented by the string.\n\t * @api public\n\t */\n\tfunction decode(str) {\n\t  var decoded = 0;\n\t\n\t  for (i = 0; i < str.length; i++) {\n\t    decoded = decoded * length + map[str.charAt(i)];\n\t  }\n\t\n\t  return decoded;\n\t}\n\t\n\t/**\n\t * Yeast: A tiny growing id generator.\n\t *\n\t * @returns {String} A unique id.\n\t * @api public\n\t */\n\tfunction yeast() {\n\t  var now = encode(+new Date());\n\t\n\t  if (now !== prev) return seed = 0, prev = now;\n\t  return now +'.'+ encode(seed++);\n\t}\n\t\n\t//\n\t// Map each character to its index.\n\t//\n\tfor (; i < length; i++) map[alphabet[i]] = i;\n\t\n\t//\n\t// Expose the `yeast`, `encode` and `decode` functions.\n\t//\n\tyeast.encode = encode;\n\tyeast.decode = decode;\n\tmodule.exports = yeast;\n\n\n/***/ }),\n/* 35 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/* WEBPACK VAR INJECTION */(function(global) {/**\n\t * Module requirements.\n\t */\n\t\n\tvar Polling = __webpack_require__(22);\n\tvar inherit = __webpack_require__(33);\n\t\n\t/**\n\t * Module exports.\n\t */\n\t\n\tmodule.exports = JSONPPolling;\n\t\n\t/**\n\t * Cached regular expressions.\n\t */\n\t\n\tvar rNewline = /\\n/g;\n\tvar rEscapedNewline = /\\\\n/g;\n\t\n\t/**\n\t * Global JSONP callbacks.\n\t */\n\t\n\tvar callbacks;\n\t\n\t/**\n\t * Noop.\n\t */\n\t\n\tfunction empty () { }\n\t\n\t/**\n\t * Until https://github.com/tc39/proposal-global is shipped.\n\t */\n\tfunction glob () {\n\t  return typeof self !== 'undefined' ? self\n\t      : typeof window !== 'undefined' ? window\n\t      : typeof global !== 'undefined' ? global : {};\n\t}\n\t\n\t/**\n\t * JSONP Polling constructor.\n\t *\n\t * @param {Object} opts.\n\t * @api public\n\t */\n\t\n\tfunction JSONPPolling (opts) {\n\t  Polling.call(this, opts);\n\t\n\t  this.query = this.query || {};\n\t\n\t  // define global callbacks array if not present\n\t  // we do this here (lazily) to avoid unneeded global pollution\n\t  if (!callbacks) {\n\t    // we need to consider multiple engines in the same page\n\t    var global = glob();\n\t    callbacks = global.___eio = (global.___eio || []);\n\t  }\n\t\n\t  // callback identifier\n\t  this.index = callbacks.length;\n\t\n\t  // add callback to jsonp global\n\t  var self = this;\n\t  callbacks.push(function (msg) {\n\t    self.onData(msg);\n\t  });\n\t\n\t  // append to query string\n\t  this.query.j = this.index;\n\t\n\t  // prevent spurious errors from being emitted when the window is unloaded\n\t  if (typeof addEventListener === 'function') {\n\t    addEventListener('beforeunload', function () {\n\t      if (self.script) self.script.onerror = empty;\n\t    }, false);\n\t  }\n\t}\n\t\n\t/**\n\t * Inherits from Polling.\n\t */\n\t\n\tinherit(JSONPPolling, Polling);\n\t\n\t/*\n\t * JSONP only supports binary as base64 encoded strings\n\t */\n\t\n\tJSONPPolling.prototype.supportsBinary = false;\n\t\n\t/**\n\t * Closes the socket.\n\t *\n\t * @api private\n\t */\n\t\n\tJSONPPolling.prototype.doClose = function () {\n\t  if (this.script) {\n\t    this.script.parentNode.removeChild(this.script);\n\t    this.script = null;\n\t  }\n\t\n\t  if (this.form) {\n\t    this.form.parentNode.removeChild(this.form);\n\t    this.form = null;\n\t    this.iframe = null;\n\t  }\n\t\n\t  Polling.prototype.doClose.call(this);\n\t};\n\t\n\t/**\n\t * Starts a poll cycle.\n\t *\n\t * @api private\n\t */\n\t\n\tJSONPPolling.prototype.doPoll = function () {\n\t  var self = this;\n\t  var script = document.createElement('script');\n\t\n\t  if (this.script) {\n\t    this.script.parentNode.removeChild(this.script);\n\t    this.script = null;\n\t  }\n\t\n\t  script.async = true;\n\t  script.src = this.uri();\n\t  script.onerror = function (e) {\n\t    self.onError('jsonp poll error', e);\n\t  };\n\t\n\t  var insertAt = document.getElementsByTagName('script')[0];\n\t  if (insertAt) {\n\t    insertAt.parentNode.insertBefore(script, insertAt);\n\t  } else {\n\t    (document.head || document.body).appendChild(script);\n\t  }\n\t  this.script = script;\n\t\n\t  var isUAgecko = 'undefined' !== typeof navigator && /gecko/i.test(navigator.userAgent);\n\t\n\t  if (isUAgecko) {\n\t    setTimeout(function () {\n\t      var iframe = document.createElement('iframe');\n\t      document.body.appendChild(iframe);\n\t      document.body.removeChild(iframe);\n\t    }, 100);\n\t  }\n\t};\n\t\n\t/**\n\t * Writes with a hidden iframe.\n\t *\n\t * @param {String} data to send\n\t * @param {Function} called upon flush.\n\t * @api private\n\t */\n\t\n\tJSONPPolling.prototype.doWrite = function (data, fn) {\n\t  var self = this;\n\t\n\t  if (!this.form) {\n\t    var form = document.createElement('form');\n\t    var area = document.createElement('textarea');\n\t    var id = this.iframeId = 'eio_iframe_' + this.index;\n\t    var iframe;\n\t\n\t    form.className = 'socketio';\n\t    form.style.position = 'absolute';\n\t    form.style.top = '-1000px';\n\t    form.style.left = '-1000px';\n\t    form.target = id;\n\t    form.method = 'POST';\n\t    form.setAttribute('accept-charset', 'utf-8');\n\t    area.name = 'd';\n\t    form.appendChild(area);\n\t    document.body.appendChild(form);\n\t\n\t    this.form = form;\n\t    this.area = area;\n\t  }\n\t\n\t  this.form.action = this.uri();\n\t\n\t  function complete () {\n\t    initIframe();\n\t    fn();\n\t  }\n\t\n\t  function initIframe () {\n\t    if (self.iframe) {\n\t      try {\n\t        self.form.removeChild(self.iframe);\n\t      } catch (e) {\n\t        self.onError('jsonp polling iframe removal error', e);\n\t      }\n\t    }\n\t\n\t    try {\n\t      // ie6 dynamic iframes with target=\"\" support (thanks Chris Lambacher)\n\t      var html = '<iframe src=\"javascript:0\" name=\"' + self.iframeId + '\">';\n\t      iframe = document.createElement(html);\n\t    } catch (e) {\n\t      iframe = document.createElement('iframe');\n\t      iframe.name = self.iframeId;\n\t      iframe.src = 'javascript:0';\n\t    }\n\t\n\t    iframe.id = self.iframeId;\n\t\n\t    self.form.appendChild(iframe);\n\t    self.iframe = iframe;\n\t  }\n\t\n\t  initIframe();\n\t\n\t  // escape \\n to prevent it from being converted into \\r\\n by some UAs\n\t  // double escaping is required for escaped new lines because unescaping of new lines can be done safely on server-side\n\t  data = data.replace(rEscapedNewline, '\\\\\\n');\n\t  this.area.value = data.replace(rNewline, '\\\\n');\n\t\n\t  try {\n\t    this.form.submit();\n\t  } catch (e) {}\n\t\n\t  if (this.iframe.attachEvent) {\n\t    this.iframe.onreadystatechange = function () {\n\t      if (self.iframe.readyState === 'complete') {\n\t        complete();\n\t      }\n\t    };\n\t  } else {\n\t    this.iframe.onload = complete;\n\t  }\n\t};\n\t\n\t/* WEBPACK VAR INJECTION */}.call(exports, (function() { return this; }())))\n\n/***/ }),\n/* 36 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/**\n\t * Module dependencies.\n\t */\n\t\n\tvar Transport = __webpack_require__(23);\n\tvar parser = __webpack_require__(24);\n\tvar parseqs = __webpack_require__(32);\n\tvar inherit = __webpack_require__(33);\n\tvar yeast = __webpack_require__(34);\n\tvar debug = __webpack_require__(3)('engine.io-client:websocket');\n\t\n\tvar BrowserWebSocket, NodeWebSocket;\n\t\n\tif (typeof WebSocket !== 'undefined') {\n\t  BrowserWebSocket = WebSocket;\n\t} else if (typeof self !== 'undefined') {\n\t  BrowserWebSocket = self.WebSocket || self.MozWebSocket;\n\t}\n\t\n\tif (typeof window === 'undefined') {\n\t  try {\n\t    NodeWebSocket = __webpack_require__(37);\n\t  } catch (e) { }\n\t}\n\t\n\t/**\n\t * Get either the `WebSocket` or `MozWebSocket` globals\n\t * in the browser or try to resolve WebSocket-compatible\n\t * interface exposed by `ws` for Node-like environment.\n\t */\n\t\n\tvar WebSocketImpl = BrowserWebSocket || NodeWebSocket;\n\t\n\t/**\n\t * Module exports.\n\t */\n\t\n\tmodule.exports = WS;\n\t\n\t/**\n\t * WebSocket transport constructor.\n\t *\n\t * @api {Object} connection options\n\t * @api public\n\t */\n\t\n\tfunction WS (opts) {\n\t  var forceBase64 = (opts && opts.forceBase64);\n\t  if (forceBase64) {\n\t    this.supportsBinary = false;\n\t  }\n\t  this.perMessageDeflate = opts.perMessageDeflate;\n\t  this.usingBrowserWebSocket = BrowserWebSocket && !opts.forceNode;\n\t  this.protocols = opts.protocols;\n\t  if (!this.usingBrowserWebSocket) {\n\t    WebSocketImpl = NodeWebSocket;\n\t  }\n\t  Transport.call(this, opts);\n\t}\n\t\n\t/**\n\t * Inherits from Transport.\n\t */\n\t\n\tinherit(WS, Transport);\n\t\n\t/**\n\t * Transport name.\n\t *\n\t * @api public\n\t */\n\t\n\tWS.prototype.name = 'websocket';\n\t\n\t/*\n\t * WebSockets support binary\n\t */\n\t\n\tWS.prototype.supportsBinary = true;\n\t\n\t/**\n\t * Opens socket.\n\t *\n\t * @api private\n\t */\n\t\n\tWS.prototype.doOpen = function () {\n\t  if (!this.check()) {\n\t    // let probe timeout\n\t    return;\n\t  }\n\t\n\t  var uri = this.uri();\n\t  var protocols = this.protocols;\n\t  var opts = {\n\t    agent: this.agent,\n\t    perMessageDeflate: this.perMessageDeflate\n\t  };\n\t\n\t  // SSL options for Node.js client\n\t  opts.pfx = this.pfx;\n\t  opts.key = this.key;\n\t  opts.passphrase = this.passphrase;\n\t  opts.cert = this.cert;\n\t  opts.ca = this.ca;\n\t  opts.ciphers = this.ciphers;\n\t  opts.rejectUnauthorized = this.rejectUnauthorized;\n\t  if (this.extraHeaders) {\n\t    opts.headers = this.extraHeaders;\n\t  }\n\t  if (this.localAddress) {\n\t    opts.localAddress = this.localAddress;\n\t  }\n\t\n\t  try {\n\t    this.ws =\n\t      this.usingBrowserWebSocket && !this.isReactNative\n\t        ? protocols\n\t          ? new WebSocketImpl(uri, protocols)\n\t          : new WebSocketImpl(uri)\n\t        : new WebSocketImpl(uri, protocols, opts);\n\t  } catch (err) {\n\t    return this.emit('error', err);\n\t  }\n\t\n\t  if (this.ws.binaryType === undefined) {\n\t    this.supportsBinary = false;\n\t  }\n\t\n\t  if (this.ws.supports && this.ws.supports.binary) {\n\t    this.supportsBinary = true;\n\t    this.ws.binaryType = 'nodebuffer';\n\t  } else {\n\t    this.ws.binaryType = 'arraybuffer';\n\t  }\n\t\n\t  this.addEventListeners();\n\t};\n\t\n\t/**\n\t * Adds event listeners to the socket\n\t *\n\t * @api private\n\t */\n\t\n\tWS.prototype.addEventListeners = function () {\n\t  var self = this;\n\t\n\t  this.ws.onopen = function () {\n\t    self.onOpen();\n\t  };\n\t  this.ws.onclose = function () {\n\t    self.onClose();\n\t  };\n\t  this.ws.onmessage = function (ev) {\n\t    self.onData(ev.data);\n\t  };\n\t  this.ws.onerror = function (e) {\n\t    self.onError('websocket error', e);\n\t  };\n\t};\n\t\n\t/**\n\t * Writes data to socket.\n\t *\n\t * @param {Array} array of packets.\n\t * @api private\n\t */\n\t\n\tWS.prototype.write = function (packets) {\n\t  var self = this;\n\t  this.writable = false;\n\t\n\t  // encodePacket efficient as it uses WS framing\n\t  // no need for encodePayload\n\t  var total = packets.length;\n\t  for (var i = 0, l = total; i < l; i++) {\n\t    (function (packet) {\n\t      parser.encodePacket(packet, self.supportsBinary, function (data) {\n\t        if (!self.usingBrowserWebSocket) {\n\t          // always create a new object (GH-437)\n\t          var opts = {};\n\t          if (packet.options) {\n\t            opts.compress = packet.options.compress;\n\t          }\n\t\n\t          if (self.perMessageDeflate) {\n\t            var len = 'string' === typeof data ? Buffer.byteLength(data) : data.length;\n\t            if (len < self.perMessageDeflate.threshold) {\n\t              opts.compress = false;\n\t            }\n\t          }\n\t        }\n\t\n\t        // Sometimes the websocket has already been closed but the browser didn't\n\t        // have a chance of informing us about it yet, in that case send will\n\t        // throw an error\n\t        try {\n\t          if (self.usingBrowserWebSocket) {\n\t            // TypeError is thrown when passing the second argument on Safari\n\t            self.ws.send(data);\n\t          } else {\n\t            self.ws.send(data, opts);\n\t          }\n\t        } catch (e) {\n\t          debug('websocket closed before onclose event');\n\t        }\n\t\n\t        --total || done();\n\t      });\n\t    })(packets[i]);\n\t  }\n\t\n\t  function done () {\n\t    self.emit('flush');\n\t\n\t    // fake drain\n\t    // defer to next tick to allow Socket to clear writeBuffer\n\t    setTimeout(function () {\n\t      self.writable = true;\n\t      self.emit('drain');\n\t    }, 0);\n\t  }\n\t};\n\t\n\t/**\n\t * Called upon close\n\t *\n\t * @api private\n\t */\n\t\n\tWS.prototype.onClose = function () {\n\t  Transport.prototype.onClose.call(this);\n\t};\n\t\n\t/**\n\t * Closes socket.\n\t *\n\t * @api private\n\t */\n\t\n\tWS.prototype.doClose = function () {\n\t  if (typeof this.ws !== 'undefined') {\n\t    this.ws.close();\n\t  }\n\t};\n\t\n\t/**\n\t * Generates uri for connection.\n\t *\n\t * @api private\n\t */\n\t\n\tWS.prototype.uri = function () {\n\t  var query = this.query || {};\n\t  var schema = this.secure ? 'wss' : 'ws';\n\t  var port = '';\n\t\n\t  // avoid port if default for schema\n\t  if (this.port && (('wss' === schema && Number(this.port) !== 443) ||\n\t    ('ws' === schema && Number(this.port) !== 80))) {\n\t    port = ':' + this.port;\n\t  }\n\t\n\t  // append timestamp to URI\n\t  if (this.timestampRequests) {\n\t    query[this.timestampParam] = yeast();\n\t  }\n\t\n\t  // communicate binary support capabilities\n\t  if (!this.supportsBinary) {\n\t    query.b64 = 1;\n\t  }\n\t\n\t  query = parseqs.encode(query);\n\t\n\t  // prepend ? to query\n\t  if (query.length) {\n\t    query = '?' + query;\n\t  }\n\t\n\t  var ipv6 = this.hostname.indexOf(':') !== -1;\n\t  return schema + '://' + (ipv6 ? '[' + this.hostname + ']' : this.hostname) + port + this.path + query;\n\t};\n\t\n\t/**\n\t * Feature detection for WebSocket.\n\t *\n\t * @return {Boolean} whether this transport is available.\n\t * @api public\n\t */\n\t\n\tWS.prototype.check = function () {\n\t  return !!WebSocketImpl && !('__initialize' in WebSocketImpl && this.name === WS.prototype.name);\n\t};\n\n\n/***/ }),\n/* 37 */\n/***/ (function(module, exports) {\n\n\t/* (ignored) */\n\n/***/ }),\n/* 38 */\n/***/ (function(module, exports) {\n\n\t\n\tvar indexOf = [].indexOf;\n\t\n\tmodule.exports = function(arr, obj){\n\t  if (indexOf) return arr.indexOf(obj);\n\t  for (var i = 0; i < arr.length; ++i) {\n\t    if (arr[i] === obj) return i;\n\t  }\n\t  return -1;\n\t};\n\n/***/ }),\n/* 39 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t\n\t/**\n\t * Module dependencies.\n\t */\n\t\n\tvar parser = __webpack_require__(7);\n\tvar Emitter = __webpack_require__(11);\n\tvar toArray = __webpack_require__(40);\n\tvar on = __webpack_require__(41);\n\tvar bind = __webpack_require__(42);\n\tvar debug = __webpack_require__(3)('socket.io-client:socket');\n\tvar parseqs = __webpack_require__(32);\n\tvar hasBin = __webpack_require__(26);\n\t\n\t/**\n\t * Module exports.\n\t */\n\t\n\tmodule.exports = exports = Socket;\n\t\n\t/**\n\t * Internal events (blacklisted).\n\t * These events can't be emitted by the user.\n\t *\n\t * @api private\n\t */\n\t\n\tvar events = {\n\t  connect: 1,\n\t  connect_error: 1,\n\t  connect_timeout: 1,\n\t  connecting: 1,\n\t  disconnect: 1,\n\t  error: 1,\n\t  reconnect: 1,\n\t  reconnect_attempt: 1,\n\t  reconnect_failed: 1,\n\t  reconnect_error: 1,\n\t  reconnecting: 1,\n\t  ping: 1,\n\t  pong: 1\n\t};\n\t\n\t/**\n\t * Shortcut to `Emitter#emit`.\n\t */\n\t\n\tvar emit = Emitter.prototype.emit;\n\t\n\t/**\n\t * `Socket` constructor.\n\t *\n\t * @api public\n\t */\n\t\n\tfunction Socket (io, nsp, opts) {\n\t  this.io = io;\n\t  this.nsp = nsp;\n\t  this.json = this; // compat\n\t  this.ids = 0;\n\t  this.acks = {};\n\t  this.receiveBuffer = [];\n\t  this.sendBuffer = [];\n\t  this.connected = false;\n\t  this.disconnected = true;\n\t  this.flags = {};\n\t  if (opts && opts.query) {\n\t    this.query = opts.query;\n\t  }\n\t  if (this.io.autoConnect) this.open();\n\t}\n\t\n\t/**\n\t * Mix in `Emitter`.\n\t */\n\t\n\tEmitter(Socket.prototype);\n\t\n\t/**\n\t * Subscribe to open, close and packet events\n\t *\n\t * @api private\n\t */\n\t\n\tSocket.prototype.subEvents = function () {\n\t  if (this.subs) return;\n\t\n\t  var io = this.io;\n\t  this.subs = [\n\t    on(io, 'open', bind(this, 'onopen')),\n\t    on(io, 'packet', bind(this, 'onpacket')),\n\t    on(io, 'close', bind(this, 'onclose'))\n\t  ];\n\t};\n\t\n\t/**\n\t * \"Opens\" the socket.\n\t *\n\t * @api public\n\t */\n\t\n\tSocket.prototype.open =\n\tSocket.prototype.connect = function () {\n\t  if (this.connected) return this;\n\t\n\t  this.subEvents();\n\t  this.io.open(); // ensure open\n\t  if ('open' === this.io.readyState) this.onopen();\n\t  this.emit('connecting');\n\t  return this;\n\t};\n\t\n\t/**\n\t * Sends a `message` event.\n\t *\n\t * @return {Socket} self\n\t * @api public\n\t */\n\t\n\tSocket.prototype.send = function () {\n\t  var args = toArray(arguments);\n\t  args.unshift('message');\n\t  this.emit.apply(this, args);\n\t  return this;\n\t};\n\t\n\t/**\n\t * Override `emit`.\n\t * If the event is in `events`, it's emitted normally.\n\t *\n\t * @param {String} event name\n\t * @return {Socket} self\n\t * @api public\n\t */\n\t\n\tSocket.prototype.emit = function (ev) {\n\t  if (events.hasOwnProperty(ev)) {\n\t    emit.apply(this, arguments);\n\t    return this;\n\t  }\n\t\n\t  var args = toArray(arguments);\n\t  var packet = {\n\t    type: (this.flags.binary !== undefined ? this.flags.binary : hasBin(args)) ? parser.BINARY_EVENT : parser.EVENT,\n\t    data: args\n\t  };\n\t\n\t  packet.options = {};\n\t  packet.options.compress = !this.flags || false !== this.flags.compress;\n\t\n\t  // event ack callback\n\t  if ('function' === typeof args[args.length - 1]) {\n\t    debug('emitting packet with ack id %d', this.ids);\n\t    this.acks[this.ids] = args.pop();\n\t    packet.id = this.ids++;\n\t  }\n\t\n\t  if (this.connected) {\n\t    this.packet(packet);\n\t  } else {\n\t    this.sendBuffer.push(packet);\n\t  }\n\t\n\t  this.flags = {};\n\t\n\t  return this;\n\t};\n\t\n\t/**\n\t * Sends a packet.\n\t *\n\t * @param {Object} packet\n\t * @api private\n\t */\n\t\n\tSocket.prototype.packet = function (packet) {\n\t  packet.nsp = this.nsp;\n\t  this.io.packet(packet);\n\t};\n\t\n\t/**\n\t * Called upon engine `open`.\n\t *\n\t * @api private\n\t */\n\t\n\tSocket.prototype.onopen = function () {\n\t  debug('transport is open - connecting');\n\t\n\t  // write connect packet if necessary\n\t  if ('/' !== this.nsp) {\n\t    if (this.query) {\n\t      var query = typeof this.query === 'object' ? parseqs.encode(this.query) : this.query;\n\t      debug('sending connect packet with query %s', query);\n\t      this.packet({type: parser.CONNECT, query: query});\n\t    } else {\n\t      this.packet({type: parser.CONNECT});\n\t    }\n\t  }\n\t};\n\t\n\t/**\n\t * Called upon engine `close`.\n\t *\n\t * @param {String} reason\n\t * @api private\n\t */\n\t\n\tSocket.prototype.onclose = function (reason) {\n\t  debug('close (%s)', reason);\n\t  this.connected = false;\n\t  this.disconnected = true;\n\t  delete this.id;\n\t  this.emit('disconnect', reason);\n\t};\n\t\n\t/**\n\t * Called with socket packet.\n\t *\n\t * @param {Object} packet\n\t * @api private\n\t */\n\t\n\tSocket.prototype.onpacket = function (packet) {\n\t  var sameNamespace = packet.nsp === this.nsp;\n\t  var rootNamespaceError = packet.type === parser.ERROR && packet.nsp === '/';\n\t\n\t  if (!sameNamespace && !rootNamespaceError) return;\n\t\n\t  switch (packet.type) {\n\t    case parser.CONNECT:\n\t      this.onconnect();\n\t      break;\n\t\n\t    case parser.EVENT:\n\t      this.onevent(packet);\n\t      break;\n\t\n\t    case parser.BINARY_EVENT:\n\t      this.onevent(packet);\n\t      break;\n\t\n\t    case parser.ACK:\n\t      this.onack(packet);\n\t      break;\n\t\n\t    case parser.BINARY_ACK:\n\t      this.onack(packet);\n\t      break;\n\t\n\t    case parser.DISCONNECT:\n\t      this.ondisconnect();\n\t      break;\n\t\n\t    case parser.ERROR:\n\t      this.emit('error', packet.data);\n\t      break;\n\t  }\n\t};\n\t\n\t/**\n\t * Called upon a server event.\n\t *\n\t * @param {Object} packet\n\t * @api private\n\t */\n\t\n\tSocket.prototype.onevent = function (packet) {\n\t  var args = packet.data || [];\n\t  debug('emitting event %j', args);\n\t\n\t  if (null != packet.id) {\n\t    debug('attaching ack callback to event');\n\t    args.push(this.ack(packet.id));\n\t  }\n\t\n\t  if (this.connected) {\n\t    emit.apply(this, args);\n\t  } else {\n\t    this.receiveBuffer.push(args);\n\t  }\n\t};\n\t\n\t/**\n\t * Produces an ack callback to emit with an event.\n\t *\n\t * @api private\n\t */\n\t\n\tSocket.prototype.ack = function (id) {\n\t  var self = this;\n\t  var sent = false;\n\t  return function () {\n\t    // prevent double callbacks\n\t    if (sent) return;\n\t    sent = true;\n\t    var args = toArray(arguments);\n\t    debug('sending ack %j', args);\n\t\n\t    self.packet({\n\t      type: hasBin(args) ? parser.BINARY_ACK : parser.ACK,\n\t      id: id,\n\t      data: args\n\t    });\n\t  };\n\t};\n\t\n\t/**\n\t * Called upon a server acknowlegement.\n\t *\n\t * @param {Object} packet\n\t * @api private\n\t */\n\t\n\tSocket.prototype.onack = function (packet) {\n\t  var ack = this.acks[packet.id];\n\t  if ('function' === typeof ack) {\n\t    debug('calling ack %s with %j', packet.id, packet.data);\n\t    ack.apply(this, packet.data);\n\t    delete this.acks[packet.id];\n\t  } else {\n\t    debug('bad ack %s', packet.id);\n\t  }\n\t};\n\t\n\t/**\n\t * Called upon server connect.\n\t *\n\t * @api private\n\t */\n\t\n\tSocket.prototype.onconnect = function () {\n\t  this.connected = true;\n\t  this.disconnected = false;\n\t  this.emit('connect');\n\t  this.emitBuffered();\n\t};\n\t\n\t/**\n\t * Emit buffered events (received and emitted).\n\t *\n\t * @api private\n\t */\n\t\n\tSocket.prototype.emitBuffered = function () {\n\t  var i;\n\t  for (i = 0; i < this.receiveBuffer.length; i++) {\n\t    emit.apply(this, this.receiveBuffer[i]);\n\t  }\n\t  this.receiveBuffer = [];\n\t\n\t  for (i = 0; i < this.sendBuffer.length; i++) {\n\t    this.packet(this.sendBuffer[i]);\n\t  }\n\t  this.sendBuffer = [];\n\t};\n\t\n\t/**\n\t * Called upon server disconnect.\n\t *\n\t * @api private\n\t */\n\t\n\tSocket.prototype.ondisconnect = function () {\n\t  debug('server disconnect (%s)', this.nsp);\n\t  this.destroy();\n\t  this.onclose('io server disconnect');\n\t};\n\t\n\t/**\n\t * Called upon forced client/server side disconnections,\n\t * this method ensures the manager stops tracking us and\n\t * that reconnections don't get triggered for this.\n\t *\n\t * @api private.\n\t */\n\t\n\tSocket.prototype.destroy = function () {\n\t  if (this.subs) {\n\t    // clean subscriptions to avoid reconnections\n\t    for (var i = 0; i < this.subs.length; i++) {\n\t      this.subs[i].destroy();\n\t    }\n\t    this.subs = null;\n\t  }\n\t\n\t  this.io.destroy(this);\n\t};\n\t\n\t/**\n\t * Disconnects the socket manually.\n\t *\n\t * @return {Socket} self\n\t * @api public\n\t */\n\t\n\tSocket.prototype.close =\n\tSocket.prototype.disconnect = function () {\n\t  if (this.connected) {\n\t    debug('performing disconnect (%s)', this.nsp);\n\t    this.packet({ type: parser.DISCONNECT });\n\t  }\n\t\n\t  // remove socket from pool\n\t  this.destroy();\n\t\n\t  if (this.connected) {\n\t    // fire events\n\t    this.onclose('io client disconnect');\n\t  }\n\t  return this;\n\t};\n\t\n\t/**\n\t * Sets the compress flag.\n\t *\n\t * @param {Boolean} if `true`, compresses the sending data\n\t * @return {Socket} self\n\t * @api public\n\t */\n\t\n\tSocket.prototype.compress = function (compress) {\n\t  this.flags.compress = compress;\n\t  return this;\n\t};\n\t\n\t/**\n\t * Sets the binary flag\n\t *\n\t * @param {Boolean} whether the emitted data contains binary\n\t * @return {Socket} self\n\t * @api public\n\t */\n\t\n\tSocket.prototype.binary = function (binary) {\n\t  this.flags.binary = binary;\n\t  return this;\n\t};\n\n\n/***/ }),\n/* 40 */\n/***/ (function(module, exports) {\n\n\tmodule.exports = toArray\n\t\n\tfunction toArray(list, index) {\n\t    var array = []\n\t\n\t    index = index || 0\n\t\n\t    for (var i = index || 0; i < list.length; i++) {\n\t        array[i - index] = list[i]\n\t    }\n\t\n\t    return array\n\t}\n\n\n/***/ }),\n/* 41 */\n/***/ (function(module, exports) {\n\n\t\n\t/**\n\t * Module exports.\n\t */\n\t\n\tmodule.exports = on;\n\t\n\t/**\n\t * Helper for subscriptions.\n\t *\n\t * @param {Object|EventEmitter} obj with `Emitter` mixin or `EventEmitter`\n\t * @param {String} event name\n\t * @param {Function} callback\n\t * @api public\n\t */\n\t\n\tfunction on (obj, ev, fn) {\n\t  obj.on(ev, fn);\n\t  return {\n\t    destroy: function () {\n\t      obj.removeListener(ev, fn);\n\t    }\n\t  };\n\t}\n\n\n/***/ }),\n/* 42 */\n/***/ (function(module, exports) {\n\n\t/**\n\t * Slice reference.\n\t */\n\t\n\tvar slice = [].slice;\n\t\n\t/**\n\t * Bind `obj` to `fn`.\n\t *\n\t * @param {Object} obj\n\t * @param {Function|String} fn or string\n\t * @return {Function}\n\t * @api public\n\t */\n\t\n\tmodule.exports = function(obj, fn){\n\t  if ('string' == typeof fn) fn = obj[fn];\n\t  if ('function' != typeof fn) throw new Error('bind() requires a function');\n\t  var args = slice.call(arguments, 2);\n\t  return function(){\n\t    return fn.apply(obj, args.concat(slice.call(arguments)));\n\t  }\n\t};\n\n\n/***/ }),\n/* 43 */\n/***/ (function(module, exports) {\n\n\t\n\t/**\n\t * Expose `Backoff`.\n\t */\n\t\n\tmodule.exports = Backoff;\n\t\n\t/**\n\t * Initialize backoff timer with `opts`.\n\t *\n\t * - `min` initial timeout in milliseconds [100]\n\t * - `max` max timeout [10000]\n\t * - `jitter` [0]\n\t * - `factor` [2]\n\t *\n\t * @param {Object} opts\n\t * @api public\n\t */\n\t\n\tfunction Backoff(opts) {\n\t  opts = opts || {};\n\t  this.ms = opts.min || 100;\n\t  this.max = opts.max || 10000;\n\t  this.factor = opts.factor || 2;\n\t  this.jitter = opts.jitter > 0 && opts.jitter <= 1 ? opts.jitter : 0;\n\t  this.attempts = 0;\n\t}\n\t\n\t/**\n\t * Return the backoff duration.\n\t *\n\t * @return {Number}\n\t * @api public\n\t */\n\t\n\tBackoff.prototype.duration = function(){\n\t  var ms = this.ms * Math.pow(this.factor, this.attempts++);\n\t  if (this.jitter) {\n\t    var rand =  Math.random();\n\t    var deviation = Math.floor(rand * this.jitter * ms);\n\t    ms = (Math.floor(rand * 10) & 1) == 0  ? ms - deviation : ms + deviation;\n\t  }\n\t  return Math.min(ms, this.max) | 0;\n\t};\n\t\n\t/**\n\t * Reset the number of attempts.\n\t *\n\t * @api public\n\t */\n\t\n\tBackoff.prototype.reset = function(){\n\t  this.attempts = 0;\n\t};\n\t\n\t/**\n\t * Set the minimum duration\n\t *\n\t * @api public\n\t */\n\t\n\tBackoff.prototype.setMin = function(min){\n\t  this.ms = min;\n\t};\n\t\n\t/**\n\t * Set the maximum duration\n\t *\n\t * @api public\n\t */\n\t\n\tBackoff.prototype.setMax = function(max){\n\t  this.max = max;\n\t};\n\t\n\t/**\n\t * Set the jitter\n\t *\n\t * @api public\n\t */\n\t\n\tBackoff.prototype.setJitter = function(jitter){\n\t  this.jitter = jitter;\n\t};\n\t\n\n\n/***/ })\n/******/ ])\n});\n;\n\n\n// WEBPACK FOOTER //\n// socket.io.js", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId])\n \t\t\treturn installedModules[moduleId].exports;\n\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\texports: {},\n \t\t\tid: moduleId,\n \t\t\tloaded: false\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.loaded = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(0);\n\n\n\n// WEBPACK FOOTER //\n// webpack/bootstrap 9e45fccfabd75c66dd47", "\n/**\n * Module dependencies.\n */\n\nvar url = require('./url');\nvar parser = require('socket.io-parser');\nvar Manager = require('./manager');\nvar debug = require('debug')('socket.io-client');\n\n/**\n * Module exports.\n */\n\nmodule.exports = exports = lookup;\n\n/**\n * Managers cache.\n */\n\nvar cache = exports.managers = {};\n\n/**\n * Looks up an existing `Manager` for multiplexing.\n * If the user summons:\n *\n *   `io('http://localhost/a');`\n *   `io('http://localhost/b');`\n *\n * We reuse the existing instance based on same scheme/port/host,\n * and we initialize sockets for each namespace.\n *\n * @api public\n */\n\nfunction lookup (uri, opts) {\n  if (typeof uri === 'object') {\n    opts = uri;\n    uri = undefined;\n  }\n\n  opts = opts || {};\n\n  var parsed = url(uri);\n  var source = parsed.source;\n  var id = parsed.id;\n  var path = parsed.path;\n  var sameNamespace = cache[id] && path in cache[id].nsps;\n  var newConnection = opts.forceNew || opts['force new connection'] ||\n                      false === opts.multiplex || sameNamespace;\n\n  var io;\n\n  if (newConnection) {\n    debug('ignoring socket cache for %s', source);\n    io = Manager(source, opts);\n  } else {\n    if (!cache[id]) {\n      debug('new io instance for %s', source);\n      cache[id] = Manager(source, opts);\n    }\n    io = cache[id];\n  }\n  if (parsed.query && !opts.query) {\n    opts.query = parsed.query;\n  }\n  return io.socket(parsed.path, opts);\n}\n\n/**\n * Protocol version.\n *\n * @api public\n */\n\nexports.protocol = parser.protocol;\n\n/**\n * `connect`.\n *\n * @param {String} uri\n * @api public\n */\n\nexports.connect = lookup;\n\n/**\n * Expose constructors for standalone build.\n *\n * @api public\n */\n\nexports.Manager = require('./manager');\nexports.Socket = require('./socket');\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./lib/index.js\n// module id = 0\n// module chunks = 0", "\n/**\n * Module dependencies.\n */\n\nvar parseuri = require('parseuri');\nvar debug = require('debug')('socket.io-client:url');\n\n/**\n * Module exports.\n */\n\nmodule.exports = url;\n\n/**\n * URL parser.\n *\n * @param {String} url\n * @param {Object} An object meant to mimic window.location.\n *                 Defaults to window.location.\n * @api public\n */\n\nfunction url (uri, loc) {\n  var obj = uri;\n\n  // default to window.location\n  loc = loc || (typeof location !== 'undefined' && location);\n  if (null == uri) uri = loc.protocol + '//' + loc.host;\n\n  // relative path support\n  if ('string' === typeof uri) {\n    if ('/' === uri.charAt(0)) {\n      if ('/' === uri.charAt(1)) {\n        uri = loc.protocol + uri;\n      } else {\n        uri = loc.host + uri;\n      }\n    }\n\n    if (!/^(https?|wss?):\\/\\//.test(uri)) {\n      debug('protocol-less url %s', uri);\n      if ('undefined' !== typeof loc) {\n        uri = loc.protocol + '//' + uri;\n      } else {\n        uri = 'https://' + uri;\n      }\n    }\n\n    // parse\n    debug('parse %s', uri);\n    obj = parseuri(uri);\n  }\n\n  // make sure we treat `localhost:80` and `localhost` equally\n  if (!obj.port) {\n    if (/^(http|ws)$/.test(obj.protocol)) {\n      obj.port = '80';\n    } else if (/^(http|ws)s$/.test(obj.protocol)) {\n      obj.port = '443';\n    }\n  }\n\n  obj.path = obj.path || '/';\n\n  var ipv6 = obj.host.indexOf(':') !== -1;\n  var host = ipv6 ? '[' + obj.host + ']' : obj.host;\n\n  // define unique id\n  obj.id = obj.protocol + '://' + host + ':' + obj.port;\n  // define href\n  obj.href = obj.protocol + '://' + host + (loc && loc.port === obj.port ? '' : (':' + obj.port));\n\n  return obj;\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./lib/url.js\n// module id = 1\n// module chunks = 0", "/**\r\n * Parses an URI\r\n *\r\n * <AUTHOR> <stevenlevithan.com> (MIT license)\r\n * @api private\r\n */\r\n\r\nvar re = /^(?:(?![^:@]+:[^:@\\/]*@)(http|https|ws|wss):\\/\\/)?((?:(([^:@]*)(?::([^:@]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\\/?#]*)(?::(\\d*))?)(((\\/(?:[^?#](?![^?#\\/]*\\.[^?#\\/.]+(?:[?#]|$)))*\\/?)?([^?#\\/]*))(?:\\?([^#]*))?(?:#(.*))?)/;\r\n\r\nvar parts = [\r\n    'source', 'protocol', 'authority', 'userInfo', 'user', 'password', 'host', 'port', 'relative', 'path', 'directory', 'file', 'query', 'anchor'\r\n];\r\n\r\nmodule.exports = function parseuri(str) {\r\n    var src = str,\r\n        b = str.indexOf('['),\r\n        e = str.indexOf(']');\r\n\r\n    if (b != -1 && e != -1) {\r\n        str = str.substring(0, b) + str.substring(b, e).replace(/:/g, ';') + str.substring(e, str.length);\r\n    }\r\n\r\n    var m = re.exec(str || ''),\r\n        uri = {},\r\n        i = 14;\r\n\r\n    while (i--) {\r\n        uri[parts[i]] = m[i] || '';\r\n    }\r\n\r\n    if (b != -1 && e != -1) {\r\n        uri.source = src;\r\n        uri.host = uri.host.substring(1, uri.host.length - 1).replace(/;/g, ':');\r\n        uri.authority = uri.authority.replace('[', '').replace(']', '').replace(/;/g, ':');\r\n        uri.ipv6uri = true;\r\n    }\r\n\r\n    return uri;\r\n};\r\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/parseuri/index.js\n// module id = 2\n// module chunks = 0", "/* eslint-env browser */\n\n/**\n * This is the web browser implementation of `debug()`.\n */\n\nexports.log = log;\nexports.formatArgs = formatArgs;\nexports.save = save;\nexports.load = load;\nexports.useColors = useColors;\nexports.storage = localstorage();\n\n/**\n * Colors.\n */\n\nexports.colors = [\n\t'#0000CC',\n\t'#0000FF',\n\t'#0033CC',\n\t'#0033FF',\n\t'#0066CC',\n\t'#0066FF',\n\t'#0099CC',\n\t'#0099FF',\n\t'#00CC00',\n\t'#00CC33',\n\t'#00CC66',\n\t'#00CC99',\n\t'#00CCCC',\n\t'#00CCFF',\n\t'#3300CC',\n\t'#3300FF',\n\t'#3333CC',\n\t'#3333FF',\n\t'#3366CC',\n\t'#3366FF',\n\t'#3399CC',\n\t'#3399FF',\n\t'#33CC00',\n\t'#33CC33',\n\t'#33CC66',\n\t'#33CC99',\n\t'#33CCCC',\n\t'#33CCFF',\n\t'#6600CC',\n\t'#6600FF',\n\t'#6633CC',\n\t'#6633FF',\n\t'#66CC00',\n\t'#66CC33',\n\t'#9900CC',\n\t'#9900FF',\n\t'#9933CC',\n\t'#9933FF',\n\t'#99CC00',\n\t'#99CC33',\n\t'#CC0000',\n\t'#CC0033',\n\t'#CC0066',\n\t'#CC0099',\n\t'#CC00CC',\n\t'#CC00FF',\n\t'#CC3300',\n\t'#CC3333',\n\t'#CC3366',\n\t'#CC3399',\n\t'#CC33CC',\n\t'#CC33FF',\n\t'#CC6600',\n\t'#CC6633',\n\t'#CC9900',\n\t'#CC9933',\n\t'#CCCC00',\n\t'#CCCC33',\n\t'#FF0000',\n\t'#FF0033',\n\t'#FF0066',\n\t'#FF0099',\n\t'#FF00CC',\n\t'#FF00FF',\n\t'#FF3300',\n\t'#FF3333',\n\t'#FF3366',\n\t'#FF3399',\n\t'#FF33CC',\n\t'#FF33FF',\n\t'#FF6600',\n\t'#FF6633',\n\t'#FF9900',\n\t'#FF9933',\n\t'#FFCC00',\n\t'#FFCC33'\n];\n\n/**\n * Currently only WebKit-based Web Inspectors, Firefox >= v31,\n * and the Firebug extension (any Firefox version) are known\n * to support \"%c\" CSS customizations.\n *\n * TODO: add a `localStorage` variable to explicitly enable/disable colors\n */\n\n// eslint-disable-next-line complexity\nfunction useColors() {\n\t// NB: In an Electron preload script, document will be defined but not fully\n\t// initialized. Since we know we're in Chrome, we'll just detect this case\n\t// explicitly\n\tif (typeof window !== 'undefined' && window.process && (window.process.type === 'renderer' || window.process.__nwjs)) {\n\t\treturn true;\n\t}\n\n\t// Internet Explorer and Edge do not support colors.\n\tif (typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/(edge|trident)\\/(\\d+)/)) {\n\t\treturn false;\n\t}\n\n\t// Is webkit? http://stackoverflow.com/a/16459606/376773\n\t// document is undefined in react-native: https://github.com/facebook/react-native/pull/1632\n\treturn (typeof document !== 'undefined' && document.documentElement && document.documentElement.style && document.documentElement.style.WebkitAppearance) ||\n\t\t// Is firebug? http://stackoverflow.com/a/398120/376773\n\t\t(typeof window !== 'undefined' && window.console && (window.console.firebug || (window.console.exception && window.console.table))) ||\n\t\t// Is firefox >= v31?\n\t\t// https://developer.mozilla.org/en-US/docs/Tools/Web_Console#Styling_messages\n\t\t(typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/firefox\\/(\\d+)/) && parseInt(RegExp.$1, 10) >= 31) ||\n\t\t// Double check webkit in userAgent just in case we are in a worker\n\t\t(typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/applewebkit\\/(\\d+)/));\n}\n\n/**\n * Colorize log arguments if enabled.\n *\n * @api public\n */\n\nfunction formatArgs(args) {\n\targs[0] = (this.useColors ? '%c' : '') +\n\t\tthis.namespace +\n\t\t(this.useColors ? ' %c' : ' ') +\n\t\targs[0] +\n\t\t(this.useColors ? '%c ' : ' ') +\n\t\t'+' + module.exports.humanize(this.diff);\n\n\tif (!this.useColors) {\n\t\treturn;\n\t}\n\n\tconst c = 'color: ' + this.color;\n\targs.splice(1, 0, c, 'color: inherit');\n\n\t// The final \"%c\" is somewhat tricky, because there could be other\n\t// arguments passed either before or after the %c, so we need to\n\t// figure out the correct index to insert the CSS into\n\tlet index = 0;\n\tlet lastC = 0;\n\targs[0].replace(/%[a-zA-Z%]/g, match => {\n\t\tif (match === '%%') {\n\t\t\treturn;\n\t\t}\n\t\tindex++;\n\t\tif (match === '%c') {\n\t\t\t// We only are interested in the *last* %c\n\t\t\t// (the user may have provided their own)\n\t\t\tlastC = index;\n\t\t}\n\t});\n\n\targs.splice(lastC, 0, c);\n}\n\n/**\n * Invokes `console.log()` when available.\n * No-op when `console.log` is not a \"function\".\n *\n * @api public\n */\nfunction log(...args) {\n\t// This hackery is required for IE8/9, where\n\t// the `console.log` function doesn't have 'apply'\n\treturn typeof console === 'object' &&\n\t\tconsole.log &&\n\t\tconsole.log(...args);\n}\n\n/**\n * Save `namespaces`.\n *\n * @param {String} namespaces\n * @api private\n */\nfunction save(namespaces) {\n\ttry {\n\t\tif (namespaces) {\n\t\t\texports.storage.setItem('debug', namespaces);\n\t\t} else {\n\t\t\texports.storage.removeItem('debug');\n\t\t}\n\t} catch (error) {\n\t\t// Swallow\n\t\t// XXX (@Qix-) should we be logging these?\n\t}\n}\n\n/**\n * Load `namespaces`.\n *\n * @return {String} returns the previously persisted debug modes\n * @api private\n */\nfunction load() {\n\tlet r;\n\ttry {\n\t\tr = exports.storage.getItem('debug');\n\t} catch (error) {\n\t\t// Swallow\n\t\t// XXX (@Qix-) should we be logging these?\n\t}\n\n\t// If debug isn't set in LS, and we're in Electron, try to load $DEBUG\n\tif (!r && typeof process !== 'undefined' && 'env' in process) {\n\t\tr = process.env.DEBUG;\n\t}\n\n\treturn r;\n}\n\n/**\n * Localstorage attempts to return the localstorage.\n *\n * This is necessary because safari throws\n * when a user disables cookies/localstorage\n * and you attempt to access it.\n *\n * @return {LocalStorage}\n * @api private\n */\n\nfunction localstorage() {\n\ttry {\n\t\t// TVMLKit (Apple TV JS Runtime) does not have a window object, just localStorage in the global context\n\t\t// The Browser also has localStorage in the global context.\n\t\treturn localStorage;\n\t} catch (error) {\n\t\t// Swallow\n\t\t// XXX (@Qix-) should we be logging these?\n\t}\n}\n\nmodule.exports = require('./common')(exports);\n\nconst {formatters} = module.exports;\n\n/**\n * Map %j to `JSON.stringify()`, since no Web Inspectors do that by default.\n */\n\nformatters.j = function (v) {\n\ttry {\n\t\treturn JSON.stringify(v);\n\t} catch (error) {\n\t\treturn '[UnexpectedJSONParseError]: ' + error.message;\n\t}\n};\n\n\n\n// WEBPACK FOOTER //\n// ./~/debug/src/browser.js", "// shim for using process in browser\nvar process = module.exports = {};\n\n// cached from whatever global is present so that test runners that stub it\n// don't break things.  But we need to wrap it in a try catch in case it is\n// wrapped in strict mode code which doesn't define any globals.  It's inside a\n// function because try/catches deoptimize in certain engines.\n\nvar cachedSetTimeout;\nvar cachedClearTimeout;\n\nfunction defaultSetTimout() {\n    throw new Error('setTimeout has not been defined');\n}\nfunction defaultClearTimeout () {\n    throw new Error('clearTimeout has not been defined');\n}\n(function () {\n    try {\n        if (typeof setTimeout === 'function') {\n            cachedSetTimeout = setTimeout;\n        } else {\n            cachedSetTimeout = defaultSetTimout;\n        }\n    } catch (e) {\n        cachedSetTimeout = defaultSetTimout;\n    }\n    try {\n        if (typeof clearTimeout === 'function') {\n            cachedClearTimeout = clearTimeout;\n        } else {\n            cachedClearTimeout = defaultClearTimeout;\n        }\n    } catch (e) {\n        cachedClearTimeout = defaultClearTimeout;\n    }\n} ())\nfunction runTimeout(fun) {\n    if (cachedSetTimeout === setTimeout) {\n        //normal enviroments in sane situations\n        return setTimeout(fun, 0);\n    }\n    // if setTimeout wasn't available but was latter defined\n    if ((cachedSetTimeout === defaultSetTimout || !cachedSetTimeout) && setTimeout) {\n        cachedSetTimeout = setTimeout;\n        return setTimeout(fun, 0);\n    }\n    try {\n        // when when somebody has screwed with setTimeout but no I.E. maddness\n        return cachedSetTimeout(fun, 0);\n    } catch(e){\n        try {\n            // When we are in I.E. but the script has been evaled so I.E. doesn't trust the global object when called normally\n            return cachedSetTimeout.call(null, fun, 0);\n        } catch(e){\n            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error\n            return cachedSetTimeout.call(this, fun, 0);\n        }\n    }\n\n\n}\nfunction runClearTimeout(marker) {\n    if (cachedClearTimeout === clearTimeout) {\n        //normal enviroments in sane situations\n        return clearTimeout(marker);\n    }\n    // if clearTimeout wasn't available but was latter defined\n    if ((cachedClearTimeout === defaultClearTimeout || !cachedClearTimeout) && clearTimeout) {\n        cachedClearTimeout = clearTimeout;\n        return clearTimeout(marker);\n    }\n    try {\n        // when when somebody has screwed with setTimeout but no I.E. maddness\n        return cachedClearTimeout(marker);\n    } catch (e){\n        try {\n            // When we are in I.E. but the script has been evaled so I.E. doesn't  trust the global object when called normally\n            return cachedClearTimeout.call(null, marker);\n        } catch (e){\n            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error.\n            // Some versions of I.E. have different rules for clearTimeout vs setTimeout\n            return cachedClearTimeout.call(this, marker);\n        }\n    }\n\n\n\n}\nvar queue = [];\nvar draining = false;\nvar currentQueue;\nvar queueIndex = -1;\n\nfunction cleanUpNextTick() {\n    if (!draining || !currentQueue) {\n        return;\n    }\n    draining = false;\n    if (currentQueue.length) {\n        queue = currentQueue.concat(queue);\n    } else {\n        queueIndex = -1;\n    }\n    if (queue.length) {\n        drainQueue();\n    }\n}\n\nfunction drainQueue() {\n    if (draining) {\n        return;\n    }\n    var timeout = runTimeout(cleanUpNextTick);\n    draining = true;\n\n    var len = queue.length;\n    while(len) {\n        currentQueue = queue;\n        queue = [];\n        while (++queueIndex < len) {\n            if (currentQueue) {\n                currentQueue[queueIndex].run();\n            }\n        }\n        queueIndex = -1;\n        len = queue.length;\n    }\n    currentQueue = null;\n    draining = false;\n    runClearTimeout(timeout);\n}\n\nprocess.nextTick = function (fun) {\n    var args = new Array(arguments.length - 1);\n    if (arguments.length > 1) {\n        for (var i = 1; i < arguments.length; i++) {\n            args[i - 1] = arguments[i];\n        }\n    }\n    queue.push(new Item(fun, args));\n    if (queue.length === 1 && !draining) {\n        runTimeout(drainQueue);\n    }\n};\n\n// v8 likes predictible objects\nfunction Item(fun, array) {\n    this.fun = fun;\n    this.array = array;\n}\nItem.prototype.run = function () {\n    this.fun.apply(null, this.array);\n};\nprocess.title = 'browser';\nprocess.browser = true;\nprocess.env = {};\nprocess.argv = [];\nprocess.version = ''; // empty string to avoid regexp issues\nprocess.versions = {};\n\nfunction noop() {}\n\nprocess.on = noop;\nprocess.addListener = noop;\nprocess.once = noop;\nprocess.off = noop;\nprocess.removeListener = noop;\nprocess.removeAllListeners = noop;\nprocess.emit = noop;\nprocess.prependListener = noop;\nprocess.prependOnceListener = noop;\n\nprocess.listeners = function (name) { return [] }\n\nprocess.binding = function (name) {\n    throw new Error('process.binding is not supported');\n};\n\nprocess.cwd = function () { return '/' };\nprocess.chdir = function (dir) {\n    throw new Error('process.chdir is not supported');\n};\nprocess.umask = function() { return 0; };\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/process/browser.js\n// module id = 4\n// module chunks = 0", "\n/**\n * This is the common logic for both the Node.js and web browser\n * implementations of `debug()`.\n */\n\nfunction setup(env) {\n\tcreateDebug.debug = createDebug;\n\tcreateDebug.default = createDebug;\n\tcreateDebug.coerce = coerce;\n\tcreateDebug.disable = disable;\n\tcreateDebug.enable = enable;\n\tcreateDebug.enabled = enabled;\n\tcreateDebug.humanize = require('ms');\n\n\tObject.keys(env).forEach(key => {\n\t\tcreateDebug[key] = env[key];\n\t});\n\n\t/**\n\t* Active `debug` instances.\n\t*/\n\tcreateDebug.instances = [];\n\n\t/**\n\t* The currently active debug mode names, and names to skip.\n\t*/\n\n\tcreateDebug.names = [];\n\tcreateDebug.skips = [];\n\n\t/**\n\t* Map of special \"%n\" handling functions, for the debug \"format\" argument.\n\t*\n\t* Valid key names are a single, lower or upper-case letter, i.e. \"n\" and \"N\".\n\t*/\n\tcreateDebug.formatters = {};\n\n\t/**\n\t* Selects a color for a debug namespace\n\t* @param {String} namespace The namespace string for the for the debug instance to be colored\n\t* @return {Number|String} An ANSI color code for the given namespace\n\t* @api private\n\t*/\n\tfunction selectColor(namespace) {\n\t\tlet hash = 0;\n\n\t\tfor (let i = 0; i < namespace.length; i++) {\n\t\t\thash = ((hash << 5) - hash) + namespace.charCodeAt(i);\n\t\t\thash |= 0; // Convert to 32bit integer\n\t\t}\n\n\t\treturn createDebug.colors[Math.abs(hash) % createDebug.colors.length];\n\t}\n\tcreateDebug.selectColor = selectColor;\n\n\t/**\n\t* Create a debugger with the given `namespace`.\n\t*\n\t* @param {String} namespace\n\t* @return {Function}\n\t* @api public\n\t*/\n\tfunction createDebug(namespace) {\n\t\tlet prevTime;\n\n\t\tfunction debug(...args) {\n\t\t\t// Disabled?\n\t\t\tif (!debug.enabled) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tconst self = debug;\n\n\t\t\t// Set `diff` timestamp\n\t\t\tconst curr = Number(new Date());\n\t\t\tconst ms = curr - (prevTime || curr);\n\t\t\tself.diff = ms;\n\t\t\tself.prev = prevTime;\n\t\t\tself.curr = curr;\n\t\t\tprevTime = curr;\n\n\t\t\targs[0] = createDebug.coerce(args[0]);\n\n\t\t\tif (typeof args[0] !== 'string') {\n\t\t\t\t// Anything else let's inspect with %O\n\t\t\t\targs.unshift('%O');\n\t\t\t}\n\n\t\t\t// Apply any `formatters` transformations\n\t\t\tlet index = 0;\n\t\t\targs[0] = args[0].replace(/%([a-zA-Z%])/g, (match, format) => {\n\t\t\t\t// If we encounter an escaped % then don't increase the array index\n\t\t\t\tif (match === '%%') {\n\t\t\t\t\treturn match;\n\t\t\t\t}\n\t\t\t\tindex++;\n\t\t\t\tconst formatter = createDebug.formatters[format];\n\t\t\t\tif (typeof formatter === 'function') {\n\t\t\t\t\tconst val = args[index];\n\t\t\t\t\tmatch = formatter.call(self, val);\n\n\t\t\t\t\t// Now we need to remove `args[index]` since it's inlined in the `format`\n\t\t\t\t\targs.splice(index, 1);\n\t\t\t\t\tindex--;\n\t\t\t\t}\n\t\t\t\treturn match;\n\t\t\t});\n\n\t\t\t// Apply env-specific formatting (colors, etc.)\n\t\t\tcreateDebug.formatArgs.call(self, args);\n\n\t\t\tconst logFn = self.log || createDebug.log;\n\t\t\tlogFn.apply(self, args);\n\t\t}\n\n\t\tdebug.namespace = namespace;\n\t\tdebug.enabled = createDebug.enabled(namespace);\n\t\tdebug.useColors = createDebug.useColors();\n\t\tdebug.color = selectColor(namespace);\n\t\tdebug.destroy = destroy;\n\t\tdebug.extend = extend;\n\t\t// Debug.formatArgs = formatArgs;\n\t\t// debug.rawLog = rawLog;\n\n\t\t// env-specific initialization logic for debug instances\n\t\tif (typeof createDebug.init === 'function') {\n\t\t\tcreateDebug.init(debug);\n\t\t}\n\n\t\tcreateDebug.instances.push(debug);\n\n\t\treturn debug;\n\t}\n\n\tfunction destroy() {\n\t\tconst index = createDebug.instances.indexOf(this);\n\t\tif (index !== -1) {\n\t\t\tcreateDebug.instances.splice(index, 1);\n\t\t\treturn true;\n\t\t}\n\t\treturn false;\n\t}\n\n\tfunction extend(namespace, delimiter) {\n\t\tconst newDebug = createDebug(this.namespace + (typeof delimiter === 'undefined' ? ':' : delimiter) + namespace);\n\t\tnewDebug.log = this.log;\n\t\treturn newDebug;\n\t}\n\n\t/**\n\t* Enables a debug mode by namespaces. This can include modes\n\t* separated by a colon and wildcards.\n\t*\n\t* @param {String} namespaces\n\t* @api public\n\t*/\n\tfunction enable(namespaces) {\n\t\tcreateDebug.save(namespaces);\n\n\t\tcreateDebug.names = [];\n\t\tcreateDebug.skips = [];\n\n\t\tlet i;\n\t\tconst split = (typeof namespaces === 'string' ? namespaces : '').split(/[\\s,]+/);\n\t\tconst len = split.length;\n\n\t\tfor (i = 0; i < len; i++) {\n\t\t\tif (!split[i]) {\n\t\t\t\t// ignore empty strings\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tnamespaces = split[i].replace(/\\*/g, '.*?');\n\n\t\t\tif (namespaces[0] === '-') {\n\t\t\t\tcreateDebug.skips.push(new RegExp('^' + namespaces.substr(1) + '$'));\n\t\t\t} else {\n\t\t\t\tcreateDebug.names.push(new RegExp('^' + namespaces + '$'));\n\t\t\t}\n\t\t}\n\n\t\tfor (i = 0; i < createDebug.instances.length; i++) {\n\t\t\tconst instance = createDebug.instances[i];\n\t\t\tinstance.enabled = createDebug.enabled(instance.namespace);\n\t\t}\n\t}\n\n\t/**\n\t* Disable debug output.\n\t*\n\t* @return {String} namespaces\n\t* @api public\n\t*/\n\tfunction disable() {\n\t\tconst namespaces = [\n\t\t\t...createDebug.names.map(toNamespace),\n\t\t\t...createDebug.skips.map(toNamespace).map(namespace => '-' + namespace)\n\t\t].join(',');\n\t\tcreateDebug.enable('');\n\t\treturn namespaces;\n\t}\n\n\t/**\n\t* Returns true if the given mode name is enabled, false otherwise.\n\t*\n\t* @param {String} name\n\t* @return {Boolean}\n\t* @api public\n\t*/\n\tfunction enabled(name) {\n\t\tif (name[name.length - 1] === '*') {\n\t\t\treturn true;\n\t\t}\n\n\t\tlet i;\n\t\tlet len;\n\n\t\tfor (i = 0, len = createDebug.skips.length; i < len; i++) {\n\t\t\tif (createDebug.skips[i].test(name)) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t}\n\n\t\tfor (i = 0, len = createDebug.names.length; i < len; i++) {\n\t\t\tif (createDebug.names[i].test(name)) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t}\n\n\t\treturn false;\n\t}\n\n\t/**\n\t* Convert regexp to namespace\n\t*\n\t* @param {RegExp} regxep\n\t* @return {String} namespace\n\t* @api private\n\t*/\n\tfunction toNamespace(regexp) {\n\t\treturn regexp.toString()\n\t\t\t.substring(2, regexp.toString().length - 2)\n\t\t\t.replace(/\\.\\*\\?$/, '*');\n\t}\n\n\t/**\n\t* Coerce `val`.\n\t*\n\t* @param {Mixed} val\n\t* @return {Mixed}\n\t* @api private\n\t*/\n\tfunction coerce(val) {\n\t\tif (val instanceof Error) {\n\t\t\treturn val.stack || val.message;\n\t\t}\n\t\treturn val;\n\t}\n\n\tcreateDebug.enable(createDebug.load());\n\n\treturn createDebug;\n}\n\nmodule.exports = setup;\n\n\n\n// WEBPACK FOOTER //\n// ./~/debug/src/common.js", "/**\n * Helpers.\n */\n\nvar s = 1000;\nvar m = s * 60;\nvar h = m * 60;\nvar d = h * 24;\nvar w = d * 7;\nvar y = d * 365.25;\n\n/**\n * Parse or format the given `val`.\n *\n * Options:\n *\n *  - `long` verbose formatting [false]\n *\n * @param {String|Number} val\n * @param {Object} [options]\n * @throws {Error} throw an error if val is not a non-empty string or a number\n * @return {String|Number}\n * @api public\n */\n\nmodule.exports = function(val, options) {\n  options = options || {};\n  var type = typeof val;\n  if (type === 'string' && val.length > 0) {\n    return parse(val);\n  } else if (type === 'number' && isFinite(val)) {\n    return options.long ? fmtLong(val) : fmtShort(val);\n  }\n  throw new Error(\n    'val is not a non-empty string or a valid number. val=' +\n      JSON.stringify(val)\n  );\n};\n\n/**\n * Parse the given `str` and return milliseconds.\n *\n * @param {String} str\n * @return {Number}\n * @api private\n */\n\nfunction parse(str) {\n  str = String(str);\n  if (str.length > 100) {\n    return;\n  }\n  var match = /^(-?(?:\\d+)?\\.?\\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(\n    str\n  );\n  if (!match) {\n    return;\n  }\n  var n = parseFloat(match[1]);\n  var type = (match[2] || 'ms').toLowerCase();\n  switch (type) {\n    case 'years':\n    case 'year':\n    case 'yrs':\n    case 'yr':\n    case 'y':\n      return n * y;\n    case 'weeks':\n    case 'week':\n    case 'w':\n      return n * w;\n    case 'days':\n    case 'day':\n    case 'd':\n      return n * d;\n    case 'hours':\n    case 'hour':\n    case 'hrs':\n    case 'hr':\n    case 'h':\n      return n * h;\n    case 'minutes':\n    case 'minute':\n    case 'mins':\n    case 'min':\n    case 'm':\n      return n * m;\n    case 'seconds':\n    case 'second':\n    case 'secs':\n    case 'sec':\n    case 's':\n      return n * s;\n    case 'milliseconds':\n    case 'millisecond':\n    case 'msecs':\n    case 'msec':\n    case 'ms':\n      return n;\n    default:\n      return undefined;\n  }\n}\n\n/**\n * Short format for `ms`.\n *\n * @param {Number} ms\n * @return {String}\n * @api private\n */\n\nfunction fmtShort(ms) {\n  var msAbs = Math.abs(ms);\n  if (msAbs >= d) {\n    return Math.round(ms / d) + 'd';\n  }\n  if (msAbs >= h) {\n    return Math.round(ms / h) + 'h';\n  }\n  if (msAbs >= m) {\n    return Math.round(ms / m) + 'm';\n  }\n  if (msAbs >= s) {\n    return Math.round(ms / s) + 's';\n  }\n  return ms + 'ms';\n}\n\n/**\n * Long format for `ms`.\n *\n * @param {Number} ms\n * @return {String}\n * @api private\n */\n\nfunction fmtLong(ms) {\n  var msAbs = Math.abs(ms);\n  if (msAbs >= d) {\n    return plural(ms, msAbs, d, 'day');\n  }\n  if (msAbs >= h) {\n    return plural(ms, msAbs, h, 'hour');\n  }\n  if (msAbs >= m) {\n    return plural(ms, msAbs, m, 'minute');\n  }\n  if (msAbs >= s) {\n    return plural(ms, msAbs, s, 'second');\n  }\n  return ms + ' ms';\n}\n\n/**\n * Pluralization helper.\n */\n\nfunction plural(ms, msAbs, n, name) {\n  var isPlural = msAbs >= n * 1.5;\n  return Math.round(ms / n) + ' ' + name + (isPlural ? 's' : '');\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/ms/index.js\n// module id = 6\n// module chunks = 0", "\n/**\n * Module dependencies.\n */\n\nvar debug = require('debug')('socket.io-parser');\nvar Emitter = require('component-emitter');\nvar binary = require('./binary');\nvar isArray = require('isarray');\nvar isBuf = require('./is-buffer');\n\n/**\n * Protocol version.\n *\n * @api public\n */\n\nexports.protocol = 4;\n\n/**\n * Packet types.\n *\n * @api public\n */\n\nexports.types = [\n  'CONNECT',\n  'DISCONNECT',\n  'EVENT',\n  'ACK',\n  'ERROR',\n  'BINARY_EVENT',\n  'BINARY_ACK'\n];\n\n/**\n * Packet type `connect`.\n *\n * @api public\n */\n\nexports.CONNECT = 0;\n\n/**\n * Packet type `disconnect`.\n *\n * @api public\n */\n\nexports.DISCONNECT = 1;\n\n/**\n * Packet type `event`.\n *\n * @api public\n */\n\nexports.EVENT = 2;\n\n/**\n * Packet type `ack`.\n *\n * @api public\n */\n\nexports.ACK = 3;\n\n/**\n * Packet type `error`.\n *\n * @api public\n */\n\nexports.ERROR = 4;\n\n/**\n * Packet type 'binary event'\n *\n * @api public\n */\n\nexports.BINARY_EVENT = 5;\n\n/**\n * Packet type `binary ack`. For acks with binary arguments.\n *\n * @api public\n */\n\nexports.BINARY_ACK = 6;\n\n/**\n * Encoder constructor.\n *\n * @api public\n */\n\nexports.Encoder = Encoder;\n\n/**\n * Decoder constructor.\n *\n * @api public\n */\n\nexports.Decoder = Decoder;\n\n/**\n * A socket.io Encoder instance\n *\n * @api public\n */\n\nfunction Encoder() {}\n\nvar ERROR_PACKET = exports.ERROR + '\"encode error\"';\n\n/**\n * Encode a packet as a single string if non-binary, or as a\n * buffer sequence, depending on packet type.\n *\n * @param {Object} obj - packet object\n * @param {Function} callback - function to handle encodings (likely engine.write)\n * @return Calls callback with Array of encodings\n * @api public\n */\n\nEncoder.prototype.encode = function(obj, callback){\n  debug('encoding packet %j', obj);\n\n  if (exports.BINARY_EVENT === obj.type || exports.BINARY_ACK === obj.type) {\n    encodeAsBinary(obj, callback);\n  } else {\n    var encoding = encodeAsString(obj);\n    callback([encoding]);\n  }\n};\n\n/**\n * Encode packet as string.\n *\n * @param {Object} packet\n * @return {String} encoded\n * @api private\n */\n\nfunction encodeAsString(obj) {\n\n  // first is type\n  var str = '' + obj.type;\n\n  // attachments if we have them\n  if (exports.BINARY_EVENT === obj.type || exports.BINARY_ACK === obj.type) {\n    str += obj.attachments + '-';\n  }\n\n  // if we have a namespace other than `/`\n  // we append it followed by a comma `,`\n  if (obj.nsp && '/' !== obj.nsp) {\n    str += obj.nsp + ',';\n  }\n\n  // immediately followed by the id\n  if (null != obj.id) {\n    str += obj.id;\n  }\n\n  // json data\n  if (null != obj.data) {\n    var payload = tryStringify(obj.data);\n    if (payload !== false) {\n      str += payload;\n    } else {\n      return ERROR_PACKET;\n    }\n  }\n\n  debug('encoded %j as %s', obj, str);\n  return str;\n}\n\nfunction tryStringify(str) {\n  try {\n    return JSON.stringify(str);\n  } catch(e){\n    return false;\n  }\n}\n\n/**\n * Encode packet as 'buffer sequence' by removing blobs, and\n * deconstructing packet into object with placeholders and\n * a list of buffers.\n *\n * @param {Object} packet\n * @return {Buffer} encoded\n * @api private\n */\n\nfunction encodeAsBinary(obj, callback) {\n\n  function writeEncoding(bloblessData) {\n    var deconstruction = binary.deconstructPacket(bloblessData);\n    var pack = encodeAsString(deconstruction.packet);\n    var buffers = deconstruction.buffers;\n\n    buffers.unshift(pack); // add packet info to beginning of data list\n    callback(buffers); // write all the buffers\n  }\n\n  binary.removeBlobs(obj, writeEncoding);\n}\n\n/**\n * A socket.io Decoder instance\n *\n * @return {Object} decoder\n * @api public\n */\n\nfunction Decoder() {\n  this.reconstructor = null;\n}\n\n/**\n * Mix in `Emitter` with Decoder.\n */\n\nEmitter(Decoder.prototype);\n\n/**\n * Decodes an encoded packet string into packet JSON.\n *\n * @param {String} obj - encoded packet\n * @return {Object} packet\n * @api public\n */\n\nDecoder.prototype.add = function(obj) {\n  var packet;\n  if (typeof obj === 'string') {\n    packet = decodeString(obj);\n    if (exports.BINARY_EVENT === packet.type || exports.BINARY_ACK === packet.type) { // binary packet's json\n      this.reconstructor = new BinaryReconstructor(packet);\n\n      // no attachments, labeled binary but no binary data to follow\n      if (this.reconstructor.reconPack.attachments === 0) {\n        this.emit('decoded', packet);\n      }\n    } else { // non-binary full packet\n      this.emit('decoded', packet);\n    }\n  } else if (isBuf(obj) || obj.base64) { // raw binary data\n    if (!this.reconstructor) {\n      throw new Error('got binary data when not reconstructing a packet');\n    } else {\n      packet = this.reconstructor.takeBinaryData(obj);\n      if (packet) { // received final buffer\n        this.reconstructor = null;\n        this.emit('decoded', packet);\n      }\n    }\n  } else {\n    throw new Error('Unknown type: ' + obj);\n  }\n};\n\n/**\n * Decode a packet String (JSON data)\n *\n * @param {String} str\n * @return {Object} packet\n * @api private\n */\n\nfunction decodeString(str) {\n  var i = 0;\n  // look up type\n  var p = {\n    type: Number(str.charAt(0))\n  };\n\n  if (null == exports.types[p.type]) {\n    return error('unknown packet type ' + p.type);\n  }\n\n  // look up attachments if type binary\n  if (exports.BINARY_EVENT === p.type || exports.BINARY_ACK === p.type) {\n    var buf = '';\n    while (str.charAt(++i) !== '-') {\n      buf += str.charAt(i);\n      if (i == str.length) break;\n    }\n    if (buf != Number(buf) || str.charAt(i) !== '-') {\n      throw new Error('Illegal attachments');\n    }\n    p.attachments = Number(buf);\n  }\n\n  // look up namespace (if any)\n  if ('/' === str.charAt(i + 1)) {\n    p.nsp = '';\n    while (++i) {\n      var c = str.charAt(i);\n      if (',' === c) break;\n      p.nsp += c;\n      if (i === str.length) break;\n    }\n  } else {\n    p.nsp = '/';\n  }\n\n  // look up id\n  var next = str.charAt(i + 1);\n  if ('' !== next && Number(next) == next) {\n    p.id = '';\n    while (++i) {\n      var c = str.charAt(i);\n      if (null == c || Number(c) != c) {\n        --i;\n        break;\n      }\n      p.id += str.charAt(i);\n      if (i === str.length) break;\n    }\n    p.id = Number(p.id);\n  }\n\n  // look up json data\n  if (str.charAt(++i)) {\n    var payload = tryParse(str.substr(i));\n    var isPayloadValid = payload !== false && (p.type === exports.ERROR || isArray(payload));\n    if (isPayloadValid) {\n      p.data = payload;\n    } else {\n      return error('invalid payload');\n    }\n  }\n\n  debug('decoded %s as %j', str, p);\n  return p;\n}\n\nfunction tryParse(str) {\n  try {\n    return JSON.parse(str);\n  } catch(e){\n    return false;\n  }\n}\n\n/**\n * Deallocates a parser's resources\n *\n * @api public\n */\n\nDecoder.prototype.destroy = function() {\n  if (this.reconstructor) {\n    this.reconstructor.finishedReconstruction();\n  }\n};\n\n/**\n * A manager of a binary event's 'buffer sequence'. Should\n * be constructed whenever a packet of type BINARY_EVENT is\n * decoded.\n *\n * @param {Object} packet\n * @return {BinaryReconstructor} initialized reconstructor\n * @api private\n */\n\nfunction BinaryReconstructor(packet) {\n  this.reconPack = packet;\n  this.buffers = [];\n}\n\n/**\n * Method to be called when binary data received from connection\n * after a BINARY_EVENT packet.\n *\n * @param {Buffer | ArrayBuffer} binData - the raw binary data received\n * @return {null | Object} returns null if more binary data is expected or\n *   a reconstructed packet object if all buffers have been received.\n * @api private\n */\n\nBinaryReconstructor.prototype.takeBinaryData = function(binData) {\n  this.buffers.push(binData);\n  if (this.buffers.length === this.reconPack.attachments) { // done with buffer list\n    var packet = binary.reconstructPacket(this.reconPack, this.buffers);\n    this.finishedReconstruction();\n    return packet;\n  }\n  return null;\n};\n\n/**\n * Cleans up binary packet reconstruction variables.\n *\n * @api private\n */\n\nBinaryReconstructor.prototype.finishedReconstruction = function() {\n  this.reconPack = null;\n  this.buffers = [];\n};\n\nfunction error(msg) {\n  return {\n    type: exports.ERROR,\n    data: 'parser error: ' + msg\n  };\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/socket.io-parser/index.js\n// module id = 7\n// module chunks = 0", "/**\n * This is the web browser implementation of `debug()`.\n *\n * Expose `debug()` as the module.\n */\n\nexports = module.exports = require('./debug');\nexports.log = log;\nexports.formatArgs = formatArgs;\nexports.save = save;\nexports.load = load;\nexports.useColors = useColors;\nexports.storage = 'undefined' != typeof chrome\n               && 'undefined' != typeof chrome.storage\n                  ? chrome.storage.local\n                  : localstorage();\n\n/**\n * Colors.\n */\n\nexports.colors = [\n  '#0000CC', '#0000FF', '#0033CC', '#0033FF', '#0066CC', '#0066FF', '#0099CC',\n  '#0099FF', '#00CC00', '#00CC33', '#00CC66', '#00CC99', '#00CCCC', '#00CCFF',\n  '#3300CC', '#3300FF', '#3333CC', '#3333FF', '#3366CC', '#3366FF', '#3399CC',\n  '#3399FF', '#33CC00', '#33CC33', '#33CC66', '#33CC99', '#33CCCC', '#33CCFF',\n  '#6600CC', '#6600FF', '#6633CC', '#6633FF', '#66CC00', '#66CC33', '#9900CC',\n  '#9900FF', '#9933CC', '#9933FF', '#99CC00', '#99CC33', '#CC0000', '#CC0033',\n  '#CC0066', '#CC0099', '#CC00CC', '#CC00FF', '#CC3300', '#CC3333', '#CC3366',\n  '#CC3399', '#CC33CC', '#CC33FF', '#CC6600', '#CC6633', '#CC9900', '#CC9933',\n  '#CCCC00', '#CCCC33', '#FF0000', '#FF0033', '#FF0066', '#FF0099', '#FF00CC',\n  '#FF00FF', '#FF3300', '#FF3333', '#FF3366', '#FF3399', '#FF33CC', '#FF33FF',\n  '#FF6600', '#FF6633', '#FF9900', '#FF9933', '#FFCC00', '#FFCC33'\n];\n\n/**\n * Currently only WebKit-based Web Inspectors, Firefox >= v31,\n * and the Firebug extension (any Firefox version) are known\n * to support \"%c\" CSS customizations.\n *\n * TODO: add a `localStorage` variable to explicitly enable/disable colors\n */\n\nfunction useColors() {\n  // NB: In an Electron preload script, document will be defined but not fully\n  // initialized. Since we know we're in Chrome, we'll just detect this case\n  // explicitly\n  if (typeof window !== 'undefined' && window.process && window.process.type === 'renderer') {\n    return true;\n  }\n\n  // Internet Explorer and Edge do not support colors.\n  if (typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/(edge|trident)\\/(\\d+)/)) {\n    return false;\n  }\n\n  // is webkit? http://stackoverflow.com/a/16459606/376773\n  // document is undefined in react-native: https://github.com/facebook/react-native/pull/1632\n  return (typeof document !== 'undefined' && document.documentElement && document.documentElement.style && document.documentElement.style.WebkitAppearance) ||\n    // is firebug? http://stackoverflow.com/a/398120/376773\n    (typeof window !== 'undefined' && window.console && (window.console.firebug || (window.console.exception && window.console.table))) ||\n    // is firefox >= v31?\n    // https://developer.mozilla.org/en-US/docs/Tools/Web_Console#Styling_messages\n    (typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/firefox\\/(\\d+)/) && parseInt(RegExp.$1, 10) >= 31) ||\n    // double check webkit in userAgent just in case we are in a worker\n    (typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/applewebkit\\/(\\d+)/));\n}\n\n/**\n * Map %j to `JSON.stringify()`, since no Web Inspectors do that by default.\n */\n\nexports.formatters.j = function(v) {\n  try {\n    return JSON.stringify(v);\n  } catch (err) {\n    return '[UnexpectedJSONParseError]: ' + err.message;\n  }\n};\n\n\n/**\n * Colorize log arguments if enabled.\n *\n * @api public\n */\n\nfunction formatArgs(args) {\n  var useColors = this.useColors;\n\n  args[0] = (useColors ? '%c' : '')\n    + this.namespace\n    + (useColors ? ' %c' : ' ')\n    + args[0]\n    + (useColors ? '%c ' : ' ')\n    + '+' + exports.humanize(this.diff);\n\n  if (!useColors) return;\n\n  var c = 'color: ' + this.color;\n  args.splice(1, 0, c, 'color: inherit')\n\n  // the final \"%c\" is somewhat tricky, because there could be other\n  // arguments passed either before or after the %c, so we need to\n  // figure out the correct index to insert the CSS into\n  var index = 0;\n  var lastC = 0;\n  args[0].replace(/%[a-zA-Z%]/g, function(match) {\n    if ('%%' === match) return;\n    index++;\n    if ('%c' === match) {\n      // we only are interested in the *last* %c\n      // (the user may have provided their own)\n      lastC = index;\n    }\n  });\n\n  args.splice(lastC, 0, c);\n}\n\n/**\n * Invokes `console.log()` when available.\n * No-op when `console.log` is not a \"function\".\n *\n * @api public\n */\n\nfunction log() {\n  // this hackery is required for IE8/9, where\n  // the `console.log` function doesn't have 'apply'\n  return 'object' === typeof console\n    && console.log\n    && Function.prototype.apply.call(console.log, console, arguments);\n}\n\n/**\n * Save `namespaces`.\n *\n * @param {String} namespaces\n * @api private\n */\n\nfunction save(namespaces) {\n  try {\n    if (null == namespaces) {\n      exports.storage.removeItem('debug');\n    } else {\n      exports.storage.debug = namespaces;\n    }\n  } catch(e) {}\n}\n\n/**\n * Load `namespaces`.\n *\n * @return {String} returns the previously persisted debug modes\n * @api private\n */\n\nfunction load() {\n  var r;\n  try {\n    r = exports.storage.debug;\n  } catch(e) {}\n\n  // If debug isn't set in LS, and we're in Electron, try to load $DEBUG\n  if (!r && typeof process !== 'undefined' && 'env' in process) {\n    r = process.env.DEBUG;\n  }\n\n  return r;\n}\n\n/**\n * Enable namespaces listed in `localStorage.debug` initially.\n */\n\nexports.enable(load());\n\n/**\n * Localstorage attempts to return the localstorage.\n *\n * This is necessary because safari throws\n * when a user disables cookies/localstorage\n * and you attempt to access it.\n *\n * @return {LocalStorage}\n * @api private\n */\n\nfunction localstorage() {\n  try {\n    return window.localStorage;\n  } catch (e) {}\n}\n\n\n\n// WEBPACK FOOTER //\n// ./~/socket.io-parser/~/debug/src/browser.js", "\n/**\n * This is the common logic for both the Node.js and web browser\n * implementations of `debug()`.\n *\n * Expose `debug()` as the module.\n */\n\nexports = module.exports = createDebug.debug = createDebug['default'] = createDebug;\nexports.coerce = coerce;\nexports.disable = disable;\nexports.enable = enable;\nexports.enabled = enabled;\nexports.humanize = require('ms');\n\n/**\n * Active `debug` instances.\n */\nexports.instances = [];\n\n/**\n * The currently active debug mode names, and names to skip.\n */\n\nexports.names = [];\nexports.skips = [];\n\n/**\n * Map of special \"%n\" handling functions, for the debug \"format\" argument.\n *\n * Valid key names are a single, lower or upper-case letter, i.e. \"n\" and \"N\".\n */\n\nexports.formatters = {};\n\n/**\n * Select a color.\n * @param {String} namespace\n * @return {Number}\n * @api private\n */\n\nfunction selectColor(namespace) {\n  var hash = 0, i;\n\n  for (i in namespace) {\n    hash  = ((hash << 5) - hash) + namespace.charCodeAt(i);\n    hash |= 0; // Convert to 32bit integer\n  }\n\n  return exports.colors[Math.abs(hash) % exports.colors.length];\n}\n\n/**\n * Create a debugger with the given `namespace`.\n *\n * @param {String} namespace\n * @return {Function}\n * @api public\n */\n\nfunction createDebug(namespace) {\n\n  var prevTime;\n\n  function debug() {\n    // disabled?\n    if (!debug.enabled) return;\n\n    var self = debug;\n\n    // set `diff` timestamp\n    var curr = +new Date();\n    var ms = curr - (prevTime || curr);\n    self.diff = ms;\n    self.prev = prevTime;\n    self.curr = curr;\n    prevTime = curr;\n\n    // turn the `arguments` into a proper Array\n    var args = new Array(arguments.length);\n    for (var i = 0; i < args.length; i++) {\n      args[i] = arguments[i];\n    }\n\n    args[0] = exports.coerce(args[0]);\n\n    if ('string' !== typeof args[0]) {\n      // anything else let's inspect with %O\n      args.unshift('%O');\n    }\n\n    // apply any `formatters` transformations\n    var index = 0;\n    args[0] = args[0].replace(/%([a-zA-Z%])/g, function(match, format) {\n      // if we encounter an escaped % then don't increase the array index\n      if (match === '%%') return match;\n      index++;\n      var formatter = exports.formatters[format];\n      if ('function' === typeof formatter) {\n        var val = args[index];\n        match = formatter.call(self, val);\n\n        // now we need to remove `args[index]` since it's inlined in the `format`\n        args.splice(index, 1);\n        index--;\n      }\n      return match;\n    });\n\n    // apply env-specific formatting (colors, etc.)\n    exports.formatArgs.call(self, args);\n\n    var logFn = debug.log || exports.log || console.log.bind(console);\n    logFn.apply(self, args);\n  }\n\n  debug.namespace = namespace;\n  debug.enabled = exports.enabled(namespace);\n  debug.useColors = exports.useColors();\n  debug.color = selectColor(namespace);\n  debug.destroy = destroy;\n\n  // env-specific initialization logic for debug instances\n  if ('function' === typeof exports.init) {\n    exports.init(debug);\n  }\n\n  exports.instances.push(debug);\n\n  return debug;\n}\n\nfunction destroy () {\n  var index = exports.instances.indexOf(this);\n  if (index !== -1) {\n    exports.instances.splice(index, 1);\n    return true;\n  } else {\n    return false;\n  }\n}\n\n/**\n * Enables a debug mode by namespaces. This can include modes\n * separated by a colon and wildcards.\n *\n * @param {String} namespaces\n * @api public\n */\n\nfunction enable(namespaces) {\n  exports.save(namespaces);\n\n  exports.names = [];\n  exports.skips = [];\n\n  var i;\n  var split = (typeof namespaces === 'string' ? namespaces : '').split(/[\\s,]+/);\n  var len = split.length;\n\n  for (i = 0; i < len; i++) {\n    if (!split[i]) continue; // ignore empty strings\n    namespaces = split[i].replace(/\\*/g, '.*?');\n    if (namespaces[0] === '-') {\n      exports.skips.push(new RegExp('^' + namespaces.substr(1) + '$'));\n    } else {\n      exports.names.push(new RegExp('^' + namespaces + '$'));\n    }\n  }\n\n  for (i = 0; i < exports.instances.length; i++) {\n    var instance = exports.instances[i];\n    instance.enabled = exports.enabled(instance.namespace);\n  }\n}\n\n/**\n * Disable debug output.\n *\n * @api public\n */\n\nfunction disable() {\n  exports.enable('');\n}\n\n/**\n * Returns true if the given mode name is enabled, false otherwise.\n *\n * @param {String} name\n * @return {Boolean}\n * @api public\n */\n\nfunction enabled(name) {\n  if (name[name.length - 1] === '*') {\n    return true;\n  }\n  var i, len;\n  for (i = 0, len = exports.skips.length; i < len; i++) {\n    if (exports.skips[i].test(name)) {\n      return false;\n    }\n  }\n  for (i = 0, len = exports.names.length; i < len; i++) {\n    if (exports.names[i].test(name)) {\n      return true;\n    }\n  }\n  return false;\n}\n\n/**\n * Coerce `val`.\n *\n * @param {Mixed} val\n * @return {Mixed}\n * @api private\n */\n\nfunction coerce(val) {\n  if (val instanceof Error) return val.stack || val.message;\n  return val;\n}\n\n\n\n// WEBPACK FOOTER //\n// ./~/socket.io-parser/~/debug/src/debug.js", "/**\n * Helpers.\n */\n\nvar s = 1000;\nvar m = s * 60;\nvar h = m * 60;\nvar d = h * 24;\nvar y = d * 365.25;\n\n/**\n * Parse or format the given `val`.\n *\n * Options:\n *\n *  - `long` verbose formatting [false]\n *\n * @param {String|Number} val\n * @param {Object} [options]\n * @throws {Error} throw an error if val is not a non-empty string or a number\n * @return {String|Number}\n * @api public\n */\n\nmodule.exports = function(val, options) {\n  options = options || {};\n  var type = typeof val;\n  if (type === 'string' && val.length > 0) {\n    return parse(val);\n  } else if (type === 'number' && isNaN(val) === false) {\n    return options.long ? fmtLong(val) : fmtShort(val);\n  }\n  throw new Error(\n    'val is not a non-empty string or a valid number. val=' +\n      JSON.stringify(val)\n  );\n};\n\n/**\n * Parse the given `str` and return milliseconds.\n *\n * @param {String} str\n * @return {Number}\n * @api private\n */\n\nfunction parse(str) {\n  str = String(str);\n  if (str.length > 100) {\n    return;\n  }\n  var match = /^((?:\\d+)?\\.?\\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|years?|yrs?|y)?$/i.exec(\n    str\n  );\n  if (!match) {\n    return;\n  }\n  var n = parseFloat(match[1]);\n  var type = (match[2] || 'ms').toLowerCase();\n  switch (type) {\n    case 'years':\n    case 'year':\n    case 'yrs':\n    case 'yr':\n    case 'y':\n      return n * y;\n    case 'days':\n    case 'day':\n    case 'd':\n      return n * d;\n    case 'hours':\n    case 'hour':\n    case 'hrs':\n    case 'hr':\n    case 'h':\n      return n * h;\n    case 'minutes':\n    case 'minute':\n    case 'mins':\n    case 'min':\n    case 'm':\n      return n * m;\n    case 'seconds':\n    case 'second':\n    case 'secs':\n    case 'sec':\n    case 's':\n      return n * s;\n    case 'milliseconds':\n    case 'millisecond':\n    case 'msecs':\n    case 'msec':\n    case 'ms':\n      return n;\n    default:\n      return undefined;\n  }\n}\n\n/**\n * Short format for `ms`.\n *\n * @param {Number} ms\n * @return {String}\n * @api private\n */\n\nfunction fmtShort(ms) {\n  if (ms >= d) {\n    return Math.round(ms / d) + 'd';\n  }\n  if (ms >= h) {\n    return Math.round(ms / h) + 'h';\n  }\n  if (ms >= m) {\n    return Math.round(ms / m) + 'm';\n  }\n  if (ms >= s) {\n    return Math.round(ms / s) + 's';\n  }\n  return ms + 'ms';\n}\n\n/**\n * Long format for `ms`.\n *\n * @param {Number} ms\n * @return {String}\n * @api private\n */\n\nfunction fmtLong(ms) {\n  return plural(ms, d, 'day') ||\n    plural(ms, h, 'hour') ||\n    plural(ms, m, 'minute') ||\n    plural(ms, s, 'second') ||\n    ms + ' ms';\n}\n\n/**\n * Pluralization helper.\n */\n\nfunction plural(ms, n, name) {\n  if (ms < n) {\n    return;\n  }\n  if (ms < n * 1.5) {\n    return Math.floor(ms / n) + ' ' + name;\n  }\n  return Math.ceil(ms / n) + ' ' + name + 's';\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/socket.io-parser/~/ms/index.js\n// module id = 10\n// module chunks = 0", "\r\n/**\r\n * Expose `Emitter`.\r\n */\r\n\r\nif (typeof module !== 'undefined') {\r\n  module.exports = Emitter;\r\n}\r\n\r\n/**\r\n * Initialize a new `Emitter`.\r\n *\r\n * @api public\r\n */\r\n\r\nfunction Emitter(obj) {\r\n  if (obj) return mixin(obj);\r\n};\r\n\r\n/**\r\n * Mixin the emitter properties.\r\n *\r\n * @param {Object} obj\r\n * @return {Object}\r\n * @api private\r\n */\r\n\r\nfunction mixin(obj) {\r\n  for (var key in Emitter.prototype) {\r\n    obj[key] = Emitter.prototype[key];\r\n  }\r\n  return obj;\r\n}\r\n\r\n/**\r\n * Listen on the given `event` with `fn`.\r\n *\r\n * @param {String} event\r\n * @param {Function} fn\r\n * @return {Emitter}\r\n * @api public\r\n */\r\n\r\nEmitter.prototype.on =\r\nEmitter.prototype.addEventListener = function(event, fn){\r\n  this._callbacks = this._callbacks || {};\r\n  (this._callbacks['$' + event] = this._callbacks['$' + event] || [])\r\n    .push(fn);\r\n  return this;\r\n};\r\n\r\n/**\r\n * Adds an `event` listener that will be invoked a single\r\n * time then automatically removed.\r\n *\r\n * @param {String} event\r\n * @param {Function} fn\r\n * @return {Emitter}\r\n * @api public\r\n */\r\n\r\nEmitter.prototype.once = function(event, fn){\r\n  function on() {\r\n    this.off(event, on);\r\n    fn.apply(this, arguments);\r\n  }\r\n\r\n  on.fn = fn;\r\n  this.on(event, on);\r\n  return this;\r\n};\r\n\r\n/**\r\n * Remove the given callback for `event` or all\r\n * registered callbacks.\r\n *\r\n * @param {String} event\r\n * @param {Function} fn\r\n * @return {Emitter}\r\n * @api public\r\n */\r\n\r\nEmitter.prototype.off =\r\nEmitter.prototype.removeListener =\r\nEmitter.prototype.removeAllListeners =\r\nEmitter.prototype.removeEventListener = function(event, fn){\r\n  this._callbacks = this._callbacks || {};\r\n\r\n  // all\r\n  if (0 == arguments.length) {\r\n    this._callbacks = {};\r\n    return this;\r\n  }\r\n\r\n  // specific event\r\n  var callbacks = this._callbacks['$' + event];\r\n  if (!callbacks) return this;\r\n\r\n  // remove all handlers\r\n  if (1 == arguments.length) {\r\n    delete this._callbacks['$' + event];\r\n    return this;\r\n  }\r\n\r\n  // remove specific handler\r\n  var cb;\r\n  for (var i = 0; i < callbacks.length; i++) {\r\n    cb = callbacks[i];\r\n    if (cb === fn || cb.fn === fn) {\r\n      callbacks.splice(i, 1);\r\n      break;\r\n    }\r\n  }\r\n  return this;\r\n};\r\n\r\n/**\r\n * Emit `event` with the given args.\r\n *\r\n * @param {String} event\r\n * @param {Mixed} ...\r\n * @return {Emitter}\r\n */\r\n\r\nEmitter.prototype.emit = function(event){\r\n  this._callbacks = this._callbacks || {};\r\n  var args = [].slice.call(arguments, 1)\r\n    , callbacks = this._callbacks['$' + event];\r\n\r\n  if (callbacks) {\r\n    callbacks = callbacks.slice(0);\r\n    for (var i = 0, len = callbacks.length; i < len; ++i) {\r\n      callbacks[i].apply(this, args);\r\n    }\r\n  }\r\n\r\n  return this;\r\n};\r\n\r\n/**\r\n * Return array of callbacks for `event`.\r\n *\r\n * @param {String} event\r\n * @return {Array}\r\n * @api public\r\n */\r\n\r\nEmitter.prototype.listeners = function(event){\r\n  this._callbacks = this._callbacks || {};\r\n  return this._callbacks['$' + event] || [];\r\n};\r\n\r\n/**\r\n * Check if this emitter has `event` handlers.\r\n *\r\n * @param {String} event\r\n * @return {Boolean}\r\n * @api public\r\n */\r\n\r\nEmitter.prototype.hasListeners = function(event){\r\n  return !! this.listeners(event).length;\r\n};\r\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/component-emitter/index.js\n// module id = 11\n// module chunks = 0", "/*global Blob,File*/\n\n/**\n * Module requirements\n */\n\nvar isArray = require('isarray');\nvar isBuf = require('./is-buffer');\nvar toString = Object.prototype.toString;\nvar withNativeBlob = typeof Blob === 'function' || (typeof Blob !== 'undefined' && toString.call(Blob) === '[object BlobConstructor]');\nvar withNativeFile = typeof File === 'function' || (typeof File !== 'undefined' && toString.call(File) === '[object FileConstructor]');\n\n/**\n * Replaces every Buffer | ArrayBuffer in packet with a numbered placeholder.\n * Anything with blobs or files should be fed through removeBlobs before coming\n * here.\n *\n * @param {Object} packet - socket.io event packet\n * @return {Object} with deconstructed packet and list of buffers\n * @api public\n */\n\nexports.deconstructPacket = function(packet) {\n  var buffers = [];\n  var packetData = packet.data;\n  var pack = packet;\n  pack.data = _deconstructPacket(packetData, buffers);\n  pack.attachments = buffers.length; // number of binary 'attachments'\n  return {packet: pack, buffers: buffers};\n};\n\nfunction _deconstructPacket(data, buffers) {\n  if (!data) return data;\n\n  if (isBuf(data)) {\n    var placeholder = { _placeholder: true, num: buffers.length };\n    buffers.push(data);\n    return placeholder;\n  } else if (isArray(data)) {\n    var newData = new Array(data.length);\n    for (var i = 0; i < data.length; i++) {\n      newData[i] = _deconstructPacket(data[i], buffers);\n    }\n    return newData;\n  } else if (typeof data === 'object' && !(data instanceof Date)) {\n    var newData = {};\n    for (var key in data) {\n      newData[key] = _deconstructPacket(data[key], buffers);\n    }\n    return newData;\n  }\n  return data;\n}\n\n/**\n * Reconstructs a binary packet from its placeholder packet and buffers\n *\n * @param {Object} packet - event packet with placeholders\n * @param {Array} buffers - binary buffers to put in placeholder positions\n * @return {Object} reconstructed packet\n * @api public\n */\n\nexports.reconstructPacket = function(packet, buffers) {\n  packet.data = _reconstructPacket(packet.data, buffers);\n  packet.attachments = undefined; // no longer useful\n  return packet;\n};\n\nfunction _reconstructPacket(data, buffers) {\n  if (!data) return data;\n\n  if (data && data._placeholder) {\n    return buffers[data.num]; // appropriate buffer (should be natural order anyway)\n  } else if (isArray(data)) {\n    for (var i = 0; i < data.length; i++) {\n      data[i] = _reconstructPacket(data[i], buffers);\n    }\n  } else if (typeof data === 'object') {\n    for (var key in data) {\n      data[key] = _reconstructPacket(data[key], buffers);\n    }\n  }\n\n  return data;\n}\n\n/**\n * Asynchronously removes Blobs or Files from data via\n * FileReader's readAsArrayBuffer method. Used before encoding\n * data as msgpack. Calls callback with the blobless data.\n *\n * @param {Object} data\n * @param {Function} callback\n * @api private\n */\n\nexports.removeBlobs = function(data, callback) {\n  function _removeBlobs(obj, curKey, containingObject) {\n    if (!obj) return obj;\n\n    // convert any blob\n    if ((withNativeBlob && obj instanceof Blob) ||\n        (withNativeFile && obj instanceof File)) {\n      pendingBlobs++;\n\n      // async filereader\n      var fileReader = new FileReader();\n      fileReader.onload = function() { // this.result == arraybuffer\n        if (containingObject) {\n          containingObject[curKey] = this.result;\n        }\n        else {\n          bloblessData = this.result;\n        }\n\n        // if nothing pending its callback time\n        if(! --pendingBlobs) {\n          callback(bloblessData);\n        }\n      };\n\n      fileReader.readAsArrayBuffer(obj); // blob -> arraybuffer\n    } else if (isArray(obj)) { // handle array\n      for (var i = 0; i < obj.length; i++) {\n        _removeBlobs(obj[i], i, obj);\n      }\n    } else if (typeof obj === 'object' && !isBuf(obj)) { // and object\n      for (var key in obj) {\n        _removeBlobs(obj[key], key, obj);\n      }\n    }\n  }\n\n  var pendingBlobs = 0;\n  var bloblessData = data;\n  _removeBlobs(bloblessData);\n  if (!pendingBlobs) {\n    callback(bloblessData);\n  }\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/socket.io-parser/binary.js\n// module id = 12\n// module chunks = 0", "var toString = {}.toString;\n\nmodule.exports = Array.isArray || function (arr) {\n  return toString.call(arr) == '[object Array]';\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/isarray/index.js\n// module id = 13\n// module chunks = 0", "\nmodule.exports = isBuf;\n\nvar withNativeBuffer = typeof Buffer === 'function' && typeof Buffer.isBuffer === 'function';\nvar withNativeArrayBuffer = typeof ArrayBuffer === 'function';\n\nvar isView = function (obj) {\n  return typeof ArrayBuffer.isView === 'function' ? ArrayBuffer.isView(obj) : (obj.buffer instanceof ArrayBuffer);\n};\n\n/**\n * Returns true if obj is a buffer or an arraybuffer.\n *\n * @api private\n */\n\nfunction isBuf(obj) {\n  return (withNativeBuffer && Buffer.isBuffer(obj)) ||\n          (withNativeArrayBuffer && (obj instanceof ArrayBuffer || isView(obj)));\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/socket.io-parser/is-buffer.js\n// module id = 14\n// module chunks = 0", "\n/**\n * Module dependencies.\n */\n\nvar eio = require('engine.io-client');\nvar Socket = require('./socket');\nvar Emitter = require('component-emitter');\nvar parser = require('socket.io-parser');\nvar on = require('./on');\nvar bind = require('component-bind');\nvar debug = require('debug')('socket.io-client:manager');\nvar indexOf = require('indexof');\nvar Backoff = require('backo2');\n\n/**\n * IE6+ hasOwnProperty\n */\n\nvar has = Object.prototype.hasOwnProperty;\n\n/**\n * Module exports\n */\n\nmodule.exports = Manager;\n\n/**\n * `Manager` constructor.\n *\n * @param {String} engine instance or engine uri/opts\n * @param {Object} options\n * @api public\n */\n\nfunction Manager (uri, opts) {\n  if (!(this instanceof Manager)) return new Manager(uri, opts);\n  if (uri && ('object' === typeof uri)) {\n    opts = uri;\n    uri = undefined;\n  }\n  opts = opts || {};\n\n  opts.path = opts.path || '/socket.io';\n  this.nsps = {};\n  this.subs = [];\n  this.opts = opts;\n  this.reconnection(opts.reconnection !== false);\n  this.reconnectionAttempts(opts.reconnectionAttempts || Infinity);\n  this.reconnectionDelay(opts.reconnectionDelay || 1000);\n  this.reconnectionDelayMax(opts.reconnectionDelayMax || 5000);\n  this.randomizationFactor(opts.randomizationFactor || 0.5);\n  this.backoff = new Backoff({\n    min: this.reconnectionDelay(),\n    max: this.reconnectionDelayMax(),\n    jitter: this.randomizationFactor()\n  });\n  this.timeout(null == opts.timeout ? 20000 : opts.timeout);\n  this.readyState = 'closed';\n  this.uri = uri;\n  this.connecting = [];\n  this.lastPing = null;\n  this.encoding = false;\n  this.packetBuffer = [];\n  var _parser = opts.parser || parser;\n  this.encoder = new _parser.Encoder();\n  this.decoder = new _parser.Decoder();\n  this.autoConnect = opts.autoConnect !== false;\n  if (this.autoConnect) this.open();\n}\n\n/**\n * Propagate given event to sockets and emit on `this`\n *\n * @api private\n */\n\nManager.prototype.emitAll = function () {\n  this.emit.apply(this, arguments);\n  for (var nsp in this.nsps) {\n    if (has.call(this.nsps, nsp)) {\n      this.nsps[nsp].emit.apply(this.nsps[nsp], arguments);\n    }\n  }\n};\n\n/**\n * Update `socket.id` of all sockets\n *\n * @api private\n */\n\nManager.prototype.updateSocketIds = function () {\n  for (var nsp in this.nsps) {\n    if (has.call(this.nsps, nsp)) {\n      this.nsps[nsp].id = this.generateId(nsp);\n    }\n  }\n};\n\n/**\n * generate `socket.id` for the given `nsp`\n *\n * @param {String} nsp\n * @return {String}\n * @api private\n */\n\nManager.prototype.generateId = function (nsp) {\n  return (nsp === '/' ? '' : (nsp + '#')) + this.engine.id;\n};\n\n/**\n * Mix in `Emitter`.\n */\n\nEmitter(Manager.prototype);\n\n/**\n * Sets the `reconnection` config.\n *\n * @param {Boolean} true/false if it should automatically reconnect\n * @return {Manager} self or value\n * @api public\n */\n\nManager.prototype.reconnection = function (v) {\n  if (!arguments.length) return this._reconnection;\n  this._reconnection = !!v;\n  return this;\n};\n\n/**\n * Sets the reconnection attempts config.\n *\n * @param {Number} max reconnection attempts before giving up\n * @return {Manager} self or value\n * @api public\n */\n\nManager.prototype.reconnectionAttempts = function (v) {\n  if (!arguments.length) return this._reconnectionAttempts;\n  this._reconnectionAttempts = v;\n  return this;\n};\n\n/**\n * Sets the delay between reconnections.\n *\n * @param {Number} delay\n * @return {Manager} self or value\n * @api public\n */\n\nManager.prototype.reconnectionDelay = function (v) {\n  if (!arguments.length) return this._reconnectionDelay;\n  this._reconnectionDelay = v;\n  this.backoff && this.backoff.setMin(v);\n  return this;\n};\n\nManager.prototype.randomizationFactor = function (v) {\n  if (!arguments.length) return this._randomizationFactor;\n  this._randomizationFactor = v;\n  this.backoff && this.backoff.setJitter(v);\n  return this;\n};\n\n/**\n * Sets the maximum delay between reconnections.\n *\n * @param {Number} delay\n * @return {Manager} self or value\n * @api public\n */\n\nManager.prototype.reconnectionDelayMax = function (v) {\n  if (!arguments.length) return this._reconnectionDelayMax;\n  this._reconnectionDelayMax = v;\n  this.backoff && this.backoff.setMax(v);\n  return this;\n};\n\n/**\n * Sets the connection timeout. `false` to disable\n *\n * @return {Manager} self or value\n * @api public\n */\n\nManager.prototype.timeout = function (v) {\n  if (!arguments.length) return this._timeout;\n  this._timeout = v;\n  return this;\n};\n\n/**\n * Starts trying to reconnect if reconnection is enabled and we have not\n * started reconnecting yet\n *\n * @api private\n */\n\nManager.prototype.maybeReconnectOnOpen = function () {\n  // Only try to reconnect if it's the first time we're connecting\n  if (!this.reconnecting && this._reconnection && this.backoff.attempts === 0) {\n    // keeps reconnection from firing twice for the same reconnection loop\n    this.reconnect();\n  }\n};\n\n/**\n * Sets the current transport `socket`.\n *\n * @param {Function} optional, callback\n * @return {Manager} self\n * @api public\n */\n\nManager.prototype.open =\nManager.prototype.connect = function (fn, opts) {\n  debug('readyState %s', this.readyState);\n  if (~this.readyState.indexOf('open')) return this;\n\n  debug('opening %s', this.uri);\n  this.engine = eio(this.uri, this.opts);\n  var socket = this.engine;\n  var self = this;\n  this.readyState = 'opening';\n  this.skipReconnect = false;\n\n  // emit `open`\n  var openSub = on(socket, 'open', function () {\n    self.onopen();\n    fn && fn();\n  });\n\n  // emit `connect_error`\n  var errorSub = on(socket, 'error', function (data) {\n    debug('connect_error');\n    self.cleanup();\n    self.readyState = 'closed';\n    self.emitAll('connect_error', data);\n    if (fn) {\n      var err = new Error('Connection error');\n      err.data = data;\n      fn(err);\n    } else {\n      // Only do this if there is no fn to handle the error\n      self.maybeReconnectOnOpen();\n    }\n  });\n\n  // emit `connect_timeout`\n  if (false !== this._timeout) {\n    var timeout = this._timeout;\n    debug('connect attempt will timeout after %d', timeout);\n\n    // set timer\n    var timer = setTimeout(function () {\n      debug('connect attempt timed out after %d', timeout);\n      openSub.destroy();\n      socket.close();\n      socket.emit('error', 'timeout');\n      self.emitAll('connect_timeout', timeout);\n    }, timeout);\n\n    this.subs.push({\n      destroy: function () {\n        clearTimeout(timer);\n      }\n    });\n  }\n\n  this.subs.push(openSub);\n  this.subs.push(errorSub);\n\n  return this;\n};\n\n/**\n * Called upon transport open.\n *\n * @api private\n */\n\nManager.prototype.onopen = function () {\n  debug('open');\n\n  // clear old subs\n  this.cleanup();\n\n  // mark as open\n  this.readyState = 'open';\n  this.emit('open');\n\n  // add new subs\n  var socket = this.engine;\n  this.subs.push(on(socket, 'data', bind(this, 'ondata')));\n  this.subs.push(on(socket, 'ping', bind(this, 'onping')));\n  this.subs.push(on(socket, 'pong', bind(this, 'onpong')));\n  this.subs.push(on(socket, 'error', bind(this, 'onerror')));\n  this.subs.push(on(socket, 'close', bind(this, 'onclose')));\n  this.subs.push(on(this.decoder, 'decoded', bind(this, 'ondecoded')));\n};\n\n/**\n * Called upon a ping.\n *\n * @api private\n */\n\nManager.prototype.onping = function () {\n  this.lastPing = new Date();\n  this.emitAll('ping');\n};\n\n/**\n * Called upon a packet.\n *\n * @api private\n */\n\nManager.prototype.onpong = function () {\n  this.emitAll('pong', new Date() - this.lastPing);\n};\n\n/**\n * Called with data.\n *\n * @api private\n */\n\nManager.prototype.ondata = function (data) {\n  this.decoder.add(data);\n};\n\n/**\n * Called when parser fully decodes a packet.\n *\n * @api private\n */\n\nManager.prototype.ondecoded = function (packet) {\n  this.emit('packet', packet);\n};\n\n/**\n * Called upon socket error.\n *\n * @api private\n */\n\nManager.prototype.onerror = function (err) {\n  debug('error', err);\n  this.emitAll('error', err);\n};\n\n/**\n * Creates a new socket for the given `nsp`.\n *\n * @return {Socket}\n * @api public\n */\n\nManager.prototype.socket = function (nsp, opts) {\n  var socket = this.nsps[nsp];\n  if (!socket) {\n    socket = new Socket(this, nsp, opts);\n    this.nsps[nsp] = socket;\n    var self = this;\n    socket.on('connecting', onConnecting);\n    socket.on('connect', function () {\n      socket.id = self.generateId(nsp);\n    });\n\n    if (this.autoConnect) {\n      // manually call here since connecting event is fired before listening\n      onConnecting();\n    }\n  }\n\n  function onConnecting () {\n    if (!~indexOf(self.connecting, socket)) {\n      self.connecting.push(socket);\n    }\n  }\n\n  return socket;\n};\n\n/**\n * Called upon a socket close.\n *\n * @param {Socket} socket\n */\n\nManager.prototype.destroy = function (socket) {\n  var index = indexOf(this.connecting, socket);\n  if (~index) this.connecting.splice(index, 1);\n  if (this.connecting.length) return;\n\n  this.close();\n};\n\n/**\n * Writes a packet.\n *\n * @param {Object} packet\n * @api private\n */\n\nManager.prototype.packet = function (packet) {\n  debug('writing packet %j', packet);\n  var self = this;\n  if (packet.query && packet.type === 0) packet.nsp += '?' + packet.query;\n\n  if (!self.encoding) {\n    // encode, then write to engine with result\n    self.encoding = true;\n    this.encoder.encode(packet, function (encodedPackets) {\n      for (var i = 0; i < encodedPackets.length; i++) {\n        self.engine.write(encodedPackets[i], packet.options);\n      }\n      self.encoding = false;\n      self.processPacketQueue();\n    });\n  } else { // add packet to the queue\n    self.packetBuffer.push(packet);\n  }\n};\n\n/**\n * If packet buffer is non-empty, begins encoding the\n * next packet in line.\n *\n * @api private\n */\n\nManager.prototype.processPacketQueue = function () {\n  if (this.packetBuffer.length > 0 && !this.encoding) {\n    var pack = this.packetBuffer.shift();\n    this.packet(pack);\n  }\n};\n\n/**\n * Clean up transport subscriptions and packet buffer.\n *\n * @api private\n */\n\nManager.prototype.cleanup = function () {\n  debug('cleanup');\n\n  var subsLength = this.subs.length;\n  for (var i = 0; i < subsLength; i++) {\n    var sub = this.subs.shift();\n    sub.destroy();\n  }\n\n  this.packetBuffer = [];\n  this.encoding = false;\n  this.lastPing = null;\n\n  this.decoder.destroy();\n};\n\n/**\n * Close the current socket.\n *\n * @api private\n */\n\nManager.prototype.close =\nManager.prototype.disconnect = function () {\n  debug('disconnect');\n  this.skipReconnect = true;\n  this.reconnecting = false;\n  if ('opening' === this.readyState) {\n    // `onclose` will not fire because\n    // an open event never happened\n    this.cleanup();\n  }\n  this.backoff.reset();\n  this.readyState = 'closed';\n  if (this.engine) this.engine.close();\n};\n\n/**\n * Called upon engine close.\n *\n * @api private\n */\n\nManager.prototype.onclose = function (reason) {\n  debug('onclose');\n\n  this.cleanup();\n  this.backoff.reset();\n  this.readyState = 'closed';\n  this.emit('close', reason);\n\n  if (this._reconnection && !this.skipReconnect) {\n    this.reconnect();\n  }\n};\n\n/**\n * Attempt a reconnection.\n *\n * @api private\n */\n\nManager.prototype.reconnect = function () {\n  if (this.reconnecting || this.skipReconnect) return this;\n\n  var self = this;\n\n  if (this.backoff.attempts >= this._reconnectionAttempts) {\n    debug('reconnect failed');\n    this.backoff.reset();\n    this.emitAll('reconnect_failed');\n    this.reconnecting = false;\n  } else {\n    var delay = this.backoff.duration();\n    debug('will wait %dms before reconnect attempt', delay);\n\n    this.reconnecting = true;\n    var timer = setTimeout(function () {\n      if (self.skipReconnect) return;\n\n      debug('attempting reconnect');\n      self.emitAll('reconnect_attempt', self.backoff.attempts);\n      self.emitAll('reconnecting', self.backoff.attempts);\n\n      // check again for the case socket closed in above events\n      if (self.skipReconnect) return;\n\n      self.open(function (err) {\n        if (err) {\n          debug('reconnect attempt error');\n          self.reconnecting = false;\n          self.reconnect();\n          self.emitAll('reconnect_error', err.data);\n        } else {\n          debug('reconnect success');\n          self.onreconnect();\n        }\n      });\n    }, delay);\n\n    this.subs.push({\n      destroy: function () {\n        clearTimeout(timer);\n      }\n    });\n  }\n};\n\n/**\n * Called upon successful reconnect.\n *\n * @api private\n */\n\nManager.prototype.onreconnect = function () {\n  var attempt = this.backoff.attempts;\n  this.reconnecting = false;\n  this.backoff.reset();\n  this.updateSocketIds();\n  this.emitAll('reconnect', attempt);\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./lib/manager.js\n// module id = 15\n// module chunks = 0", "\nmodule.exports = require('./socket');\n\n/**\n * Exports parser\n *\n * @api public\n *\n */\nmodule.exports.parser = require('engine.io-parser');\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-client/lib/index.js\n// module id = 16\n// module chunks = 0", "/**\n * Module dependencies.\n */\n\nvar transports = require('./transports/index');\nvar Emitter = require('component-emitter');\nvar debug = require('debug')('engine.io-client:socket');\nvar index = require('indexof');\nvar parser = require('engine.io-parser');\nvar parseuri = require('parseuri');\nvar parseqs = require('parseqs');\n\n/**\n * Module exports.\n */\n\nmodule.exports = Socket;\n\n/**\n * Socket constructor.\n *\n * @param {String|Object} uri or options\n * @param {Object} options\n * @api public\n */\n\nfunction Socket (uri, opts) {\n  if (!(this instanceof Socket)) return new Socket(uri, opts);\n\n  opts = opts || {};\n\n  if (uri && 'object' === typeof uri) {\n    opts = uri;\n    uri = null;\n  }\n\n  if (uri) {\n    uri = parseuri(uri);\n    opts.hostname = uri.host;\n    opts.secure = uri.protocol === 'https' || uri.protocol === 'wss';\n    opts.port = uri.port;\n    if (uri.query) opts.query = uri.query;\n  } else if (opts.host) {\n    opts.hostname = parseuri(opts.host).host;\n  }\n\n  this.secure = null != opts.secure ? opts.secure\n    : (typeof location !== 'undefined' && 'https:' === location.protocol);\n\n  if (opts.hostname && !opts.port) {\n    // if no port is specified manually, use the protocol default\n    opts.port = this.secure ? '443' : '80';\n  }\n\n  this.agent = opts.agent || false;\n  this.hostname = opts.hostname ||\n    (typeof location !== 'undefined' ? location.hostname : 'localhost');\n  this.port = opts.port || (typeof location !== 'undefined' && location.port\n      ? location.port\n      : (this.secure ? 443 : 80));\n  this.query = opts.query || {};\n  if ('string' === typeof this.query) this.query = parseqs.decode(this.query);\n  this.upgrade = false !== opts.upgrade;\n  this.path = (opts.path || '/engine.io').replace(/\\/$/, '') + '/';\n  this.forceJSONP = !!opts.forceJSONP;\n  this.jsonp = false !== opts.jsonp;\n  this.forceBase64 = !!opts.forceBase64;\n  this.enablesXDR = !!opts.enablesXDR;\n  this.withCredentials = false !== opts.withCredentials;\n  this.timestampParam = opts.timestampParam || 't';\n  this.timestampRequests = opts.timestampRequests;\n  this.transports = opts.transports || ['polling', 'websocket'];\n  this.transportOptions = opts.transportOptions || {};\n  this.readyState = '';\n  this.writeBuffer = [];\n  this.prevBufferLen = 0;\n  this.policyPort = opts.policyPort || 843;\n  this.rememberUpgrade = opts.rememberUpgrade || false;\n  this.binaryType = null;\n  this.onlyBinaryUpgrades = opts.onlyBinaryUpgrades;\n  this.perMessageDeflate = false !== opts.perMessageDeflate ? (opts.perMessageDeflate || {}) : false;\n\n  if (true === this.perMessageDeflate) this.perMessageDeflate = {};\n  if (this.perMessageDeflate && null == this.perMessageDeflate.threshold) {\n    this.perMessageDeflate.threshold = 1024;\n  }\n\n  // SSL options for Node.js client\n  this.pfx = opts.pfx || null;\n  this.key = opts.key || null;\n  this.passphrase = opts.passphrase || null;\n  this.cert = opts.cert || null;\n  this.ca = opts.ca || null;\n  this.ciphers = opts.ciphers || null;\n  this.rejectUnauthorized = opts.rejectUnauthorized === undefined ? true : opts.rejectUnauthorized;\n  this.forceNode = !!opts.forceNode;\n\n  // detect ReactNative environment\n  this.isReactNative = (typeof navigator !== 'undefined' && typeof navigator.product === 'string' && navigator.product.toLowerCase() === 'reactnative');\n\n  // other options for Node.js or ReactNative client\n  if (typeof self === 'undefined' || this.isReactNative) {\n    if (opts.extraHeaders && Object.keys(opts.extraHeaders).length > 0) {\n      this.extraHeaders = opts.extraHeaders;\n    }\n\n    if (opts.localAddress) {\n      this.localAddress = opts.localAddress;\n    }\n  }\n\n  // set on handshake\n  this.id = null;\n  this.upgrades = null;\n  this.pingInterval = null;\n  this.pingTimeout = null;\n\n  // set on heartbeat\n  this.pingIntervalTimer = null;\n  this.pingTimeoutTimer = null;\n\n  this.open();\n}\n\nSocket.priorWebsocketSuccess = false;\n\n/**\n * Mix in `Emitter`.\n */\n\nEmitter(Socket.prototype);\n\n/**\n * Protocol version.\n *\n * @api public\n */\n\nSocket.protocol = parser.protocol; // this is an int\n\n/**\n * Expose deps for legacy compatibility\n * and standalone browser access.\n */\n\nSocket.Socket = Socket;\nSocket.Transport = require('./transport');\nSocket.transports = require('./transports/index');\nSocket.parser = require('engine.io-parser');\n\n/**\n * Creates transport of the given type.\n *\n * @param {String} transport name\n * @return {Transport}\n * @api private\n */\n\nSocket.prototype.createTransport = function (name) {\n  debug('creating transport \"%s\"', name);\n  var query = clone(this.query);\n\n  // append engine.io protocol identifier\n  query.EIO = parser.protocol;\n\n  // transport name\n  query.transport = name;\n\n  // per-transport options\n  var options = this.transportOptions[name] || {};\n\n  // session id if we already have one\n  if (this.id) query.sid = this.id;\n\n  var transport = new transports[name]({\n    query: query,\n    socket: this,\n    agent: options.agent || this.agent,\n    hostname: options.hostname || this.hostname,\n    port: options.port || this.port,\n    secure: options.secure || this.secure,\n    path: options.path || this.path,\n    forceJSONP: options.forceJSONP || this.forceJSONP,\n    jsonp: options.jsonp || this.jsonp,\n    forceBase64: options.forceBase64 || this.forceBase64,\n    enablesXDR: options.enablesXDR || this.enablesXDR,\n    withCredentials: options.withCredentials || this.withCredentials,\n    timestampRequests: options.timestampRequests || this.timestampRequests,\n    timestampParam: options.timestampParam || this.timestampParam,\n    policyPort: options.policyPort || this.policyPort,\n    pfx: options.pfx || this.pfx,\n    key: options.key || this.key,\n    passphrase: options.passphrase || this.passphrase,\n    cert: options.cert || this.cert,\n    ca: options.ca || this.ca,\n    ciphers: options.ciphers || this.ciphers,\n    rejectUnauthorized: options.rejectUnauthorized || this.rejectUnauthorized,\n    perMessageDeflate: options.perMessageDeflate || this.perMessageDeflate,\n    extraHeaders: options.extraHeaders || this.extraHeaders,\n    forceNode: options.forceNode || this.forceNode,\n    localAddress: options.localAddress || this.localAddress,\n    requestTimeout: options.requestTimeout || this.requestTimeout,\n    protocols: options.protocols || void (0),\n    isReactNative: this.isReactNative\n  });\n\n  return transport;\n};\n\nfunction clone (obj) {\n  var o = {};\n  for (var i in obj) {\n    if (obj.hasOwnProperty(i)) {\n      o[i] = obj[i];\n    }\n  }\n  return o;\n}\n\n/**\n * Initializes transport to use and starts probe.\n *\n * @api private\n */\nSocket.prototype.open = function () {\n  var transport;\n  if (this.rememberUpgrade && Socket.priorWebsocketSuccess && this.transports.indexOf('websocket') !== -1) {\n    transport = 'websocket';\n  } else if (0 === this.transports.length) {\n    // Emit error on next tick so it can be listened to\n    var self = this;\n    setTimeout(function () {\n      self.emit('error', 'No transports available');\n    }, 0);\n    return;\n  } else {\n    transport = this.transports[0];\n  }\n  this.readyState = 'opening';\n\n  // Retry with the next transport if the transport is disabled (jsonp: false)\n  try {\n    transport = this.createTransport(transport);\n  } catch (e) {\n    this.transports.shift();\n    this.open();\n    return;\n  }\n\n  transport.open();\n  this.setTransport(transport);\n};\n\n/**\n * Sets the current transport. Disables the existing one (if any).\n *\n * @api private\n */\n\nSocket.prototype.setTransport = function (transport) {\n  debug('setting transport %s', transport.name);\n  var self = this;\n\n  if (this.transport) {\n    debug('clearing existing transport %s', this.transport.name);\n    this.transport.removeAllListeners();\n  }\n\n  // set up transport\n  this.transport = transport;\n\n  // set up transport listeners\n  transport\n  .on('drain', function () {\n    self.onDrain();\n  })\n  .on('packet', function (packet) {\n    self.onPacket(packet);\n  })\n  .on('error', function (e) {\n    self.onError(e);\n  })\n  .on('close', function () {\n    self.onClose('transport close');\n  });\n};\n\n/**\n * Probes a transport.\n *\n * @param {String} transport name\n * @api private\n */\n\nSocket.prototype.probe = function (name) {\n  debug('probing transport \"%s\"', name);\n  var transport = this.createTransport(name, { probe: 1 });\n  var failed = false;\n  var self = this;\n\n  Socket.priorWebsocketSuccess = false;\n\n  function onTransportOpen () {\n    if (self.onlyBinaryUpgrades) {\n      var upgradeLosesBinary = !this.supportsBinary && self.transport.supportsBinary;\n      failed = failed || upgradeLosesBinary;\n    }\n    if (failed) return;\n\n    debug('probe transport \"%s\" opened', name);\n    transport.send([{ type: 'ping', data: 'probe' }]);\n    transport.once('packet', function (msg) {\n      if (failed) return;\n      if ('pong' === msg.type && 'probe' === msg.data) {\n        debug('probe transport \"%s\" pong', name);\n        self.upgrading = true;\n        self.emit('upgrading', transport);\n        if (!transport) return;\n        Socket.priorWebsocketSuccess = 'websocket' === transport.name;\n\n        debug('pausing current transport \"%s\"', self.transport.name);\n        self.transport.pause(function () {\n          if (failed) return;\n          if ('closed' === self.readyState) return;\n          debug('changing transport and sending upgrade packet');\n\n          cleanup();\n\n          self.setTransport(transport);\n          transport.send([{ type: 'upgrade' }]);\n          self.emit('upgrade', transport);\n          transport = null;\n          self.upgrading = false;\n          self.flush();\n        });\n      } else {\n        debug('probe transport \"%s\" failed', name);\n        var err = new Error('probe error');\n        err.transport = transport.name;\n        self.emit('upgradeError', err);\n      }\n    });\n  }\n\n  function freezeTransport () {\n    if (failed) return;\n\n    // Any callback called by transport should be ignored since now\n    failed = true;\n\n    cleanup();\n\n    transport.close();\n    transport = null;\n  }\n\n  // Handle any error that happens while probing\n  function onerror (err) {\n    var error = new Error('probe error: ' + err);\n    error.transport = transport.name;\n\n    freezeTransport();\n\n    debug('probe transport \"%s\" failed because of error: %s', name, err);\n\n    self.emit('upgradeError', error);\n  }\n\n  function onTransportClose () {\n    onerror('transport closed');\n  }\n\n  // When the socket is closed while we're probing\n  function onclose () {\n    onerror('socket closed');\n  }\n\n  // When the socket is upgraded while we're probing\n  function onupgrade (to) {\n    if (transport && to.name !== transport.name) {\n      debug('\"%s\" works - aborting \"%s\"', to.name, transport.name);\n      freezeTransport();\n    }\n  }\n\n  // Remove all listeners on the transport and on self\n  function cleanup () {\n    transport.removeListener('open', onTransportOpen);\n    transport.removeListener('error', onerror);\n    transport.removeListener('close', onTransportClose);\n    self.removeListener('close', onclose);\n    self.removeListener('upgrading', onupgrade);\n  }\n\n  transport.once('open', onTransportOpen);\n  transport.once('error', onerror);\n  transport.once('close', onTransportClose);\n\n  this.once('close', onclose);\n  this.once('upgrading', onupgrade);\n\n  transport.open();\n};\n\n/**\n * Called when connection is deemed open.\n *\n * @api public\n */\n\nSocket.prototype.onOpen = function () {\n  debug('socket open');\n  this.readyState = 'open';\n  Socket.priorWebsocketSuccess = 'websocket' === this.transport.name;\n  this.emit('open');\n  this.flush();\n\n  // we check for `readyState` in case an `open`\n  // listener already closed the socket\n  if ('open' === this.readyState && this.upgrade && this.transport.pause) {\n    debug('starting upgrade probes');\n    for (var i = 0, l = this.upgrades.length; i < l; i++) {\n      this.probe(this.upgrades[i]);\n    }\n  }\n};\n\n/**\n * Handles a packet.\n *\n * @api private\n */\n\nSocket.prototype.onPacket = function (packet) {\n  if ('opening' === this.readyState || 'open' === this.readyState ||\n      'closing' === this.readyState) {\n    debug('socket receive: type \"%s\", data \"%s\"', packet.type, packet.data);\n\n    this.emit('packet', packet);\n\n    // Socket is live - any packet counts\n    this.emit('heartbeat');\n\n    switch (packet.type) {\n      case 'open':\n        this.onHandshake(JSON.parse(packet.data));\n        break;\n\n      case 'pong':\n        this.setPing();\n        this.emit('pong');\n        break;\n\n      case 'error':\n        var err = new Error('server error');\n        err.code = packet.data;\n        this.onError(err);\n        break;\n\n      case 'message':\n        this.emit('data', packet.data);\n        this.emit('message', packet.data);\n        break;\n    }\n  } else {\n    debug('packet received with socket readyState \"%s\"', this.readyState);\n  }\n};\n\n/**\n * Called upon handshake completion.\n *\n * @param {Object} handshake obj\n * @api private\n */\n\nSocket.prototype.onHandshake = function (data) {\n  this.emit('handshake', data);\n  this.id = data.sid;\n  this.transport.query.sid = data.sid;\n  this.upgrades = this.filterUpgrades(data.upgrades);\n  this.pingInterval = data.pingInterval;\n  this.pingTimeout = data.pingTimeout;\n  this.onOpen();\n  // In case open handler closes socket\n  if ('closed' === this.readyState) return;\n  this.setPing();\n\n  // Prolong liveness of socket on heartbeat\n  this.removeListener('heartbeat', this.onHeartbeat);\n  this.on('heartbeat', this.onHeartbeat);\n};\n\n/**\n * Resets ping timeout.\n *\n * @api private\n */\n\nSocket.prototype.onHeartbeat = function (timeout) {\n  clearTimeout(this.pingTimeoutTimer);\n  var self = this;\n  self.pingTimeoutTimer = setTimeout(function () {\n    if ('closed' === self.readyState) return;\n    self.onClose('ping timeout');\n  }, timeout || (self.pingInterval + self.pingTimeout));\n};\n\n/**\n * Pings server every `this.pingInterval` and expects response\n * within `this.pingTimeout` or closes connection.\n *\n * @api private\n */\n\nSocket.prototype.setPing = function () {\n  var self = this;\n  clearTimeout(self.pingIntervalTimer);\n  self.pingIntervalTimer = setTimeout(function () {\n    debug('writing ping packet - expecting pong within %sms', self.pingTimeout);\n    self.ping();\n    self.onHeartbeat(self.pingTimeout);\n  }, self.pingInterval);\n};\n\n/**\n* Sends a ping packet.\n*\n* @api private\n*/\n\nSocket.prototype.ping = function () {\n  var self = this;\n  this.sendPacket('ping', function () {\n    self.emit('ping');\n  });\n};\n\n/**\n * Called on `drain` event\n *\n * @api private\n */\n\nSocket.prototype.onDrain = function () {\n  this.writeBuffer.splice(0, this.prevBufferLen);\n\n  // setting prevBufferLen = 0 is very important\n  // for example, when upgrading, upgrade packet is sent over,\n  // and a nonzero prevBufferLen could cause problems on `drain`\n  this.prevBufferLen = 0;\n\n  if (0 === this.writeBuffer.length) {\n    this.emit('drain');\n  } else {\n    this.flush();\n  }\n};\n\n/**\n * Flush write buffers.\n *\n * @api private\n */\n\nSocket.prototype.flush = function () {\n  if ('closed' !== this.readyState && this.transport.writable &&\n    !this.upgrading && this.writeBuffer.length) {\n    debug('flushing %d packets in socket', this.writeBuffer.length);\n    this.transport.send(this.writeBuffer);\n    // keep track of current length of writeBuffer\n    // splice writeBuffer and callbackBuffer on `drain`\n    this.prevBufferLen = this.writeBuffer.length;\n    this.emit('flush');\n  }\n};\n\n/**\n * Sends a message.\n *\n * @param {String} message.\n * @param {Function} callback function.\n * @param {Object} options.\n * @return {Socket} for chaining.\n * @api public\n */\n\nSocket.prototype.write =\nSocket.prototype.send = function (msg, options, fn) {\n  this.sendPacket('message', msg, options, fn);\n  return this;\n};\n\n/**\n * Sends a packet.\n *\n * @param {String} packet type.\n * @param {String} data.\n * @param {Object} options.\n * @param {Function} callback function.\n * @api private\n */\n\nSocket.prototype.sendPacket = function (type, data, options, fn) {\n  if ('function' === typeof data) {\n    fn = data;\n    data = undefined;\n  }\n\n  if ('function' === typeof options) {\n    fn = options;\n    options = null;\n  }\n\n  if ('closing' === this.readyState || 'closed' === this.readyState) {\n    return;\n  }\n\n  options = options || {};\n  options.compress = false !== options.compress;\n\n  var packet = {\n    type: type,\n    data: data,\n    options: options\n  };\n  this.emit('packetCreate', packet);\n  this.writeBuffer.push(packet);\n  if (fn) this.once('flush', fn);\n  this.flush();\n};\n\n/**\n * Closes the connection.\n *\n * @api private\n */\n\nSocket.prototype.close = function () {\n  if ('opening' === this.readyState || 'open' === this.readyState) {\n    this.readyState = 'closing';\n\n    var self = this;\n\n    if (this.writeBuffer.length) {\n      this.once('drain', function () {\n        if (this.upgrading) {\n          waitForUpgrade();\n        } else {\n          close();\n        }\n      });\n    } else if (this.upgrading) {\n      waitForUpgrade();\n    } else {\n      close();\n    }\n  }\n\n  function close () {\n    self.onClose('forced close');\n    debug('socket closing - telling transport to close');\n    self.transport.close();\n  }\n\n  function cleanupAndClose () {\n    self.removeListener('upgrade', cleanupAndClose);\n    self.removeListener('upgradeError', cleanupAndClose);\n    close();\n  }\n\n  function waitForUpgrade () {\n    // wait for upgrade to finish since we can't send packets while pausing a transport\n    self.once('upgrade', cleanupAndClose);\n    self.once('upgradeError', cleanupAndClose);\n  }\n\n  return this;\n};\n\n/**\n * Called upon transport error\n *\n * @api private\n */\n\nSocket.prototype.onError = function (err) {\n  debug('socket error %j', err);\n  Socket.priorWebsocketSuccess = false;\n  this.emit('error', err);\n  this.onClose('transport error', err);\n};\n\n/**\n * Called upon transport close.\n *\n * @api private\n */\n\nSocket.prototype.onClose = function (reason, desc) {\n  if ('opening' === this.readyState || 'open' === this.readyState || 'closing' === this.readyState) {\n    debug('socket close with reason: \"%s\"', reason);\n    var self = this;\n\n    // clear timers\n    clearTimeout(this.pingIntervalTimer);\n    clearTimeout(this.pingTimeoutTimer);\n\n    // stop event from firing again for transport\n    this.transport.removeAllListeners('close');\n\n    // ensure transport won't stay open\n    this.transport.close();\n\n    // ignore further transport communication\n    this.transport.removeAllListeners();\n\n    // set ready state\n    this.readyState = 'closed';\n\n    // clear session id\n    this.id = null;\n\n    // emit close event\n    this.emit('close', reason, desc);\n\n    // clean buffers after, so users can still\n    // grab the buffers on `close` event\n    self.writeBuffer = [];\n    self.prevBufferLen = 0;\n  }\n};\n\n/**\n * Filters upgrades, returning only those matching client transports.\n *\n * @param {Array} server upgrades\n * @api private\n *\n */\n\nSocket.prototype.filterUpgrades = function (upgrades) {\n  var filteredUpgrades = [];\n  for (var i = 0, j = upgrades.length; i < j; i++) {\n    if (~index(this.transports, upgrades[i])) filteredUpgrades.push(upgrades[i]);\n  }\n  return filteredUpgrades;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-client/lib/socket.js\n// module id = 17\n// module chunks = 0", "/**\n * Module dependencies\n */\n\nvar XMLHttpRequest = require('xmlhttprequest-ssl');\nvar XHR = require('./polling-xhr');\nvar JSONP = require('./polling-jsonp');\nvar websocket = require('./websocket');\n\n/**\n * Export transports.\n */\n\nexports.polling = polling;\nexports.websocket = websocket;\n\n/**\n * Polling transport polymorphic constructor.\n * Decides on xhr vs jsonp based on feature detection.\n *\n * @api private\n */\n\nfunction polling (opts) {\n  var xhr;\n  var xd = false;\n  var xs = false;\n  var jsonp = false !== opts.jsonp;\n\n  if (typeof location !== 'undefined') {\n    var isSSL = 'https:' === location.protocol;\n    var port = location.port;\n\n    // some user agents have empty `location.port`\n    if (!port) {\n      port = isSSL ? 443 : 80;\n    }\n\n    xd = opts.hostname !== location.hostname || port !== opts.port;\n    xs = opts.secure !== isSSL;\n  }\n\n  opts.xdomain = xd;\n  opts.xscheme = xs;\n  xhr = new XMLHttpRequest(opts);\n\n  if ('open' in xhr && !opts.forceJSONP) {\n    return new XHR(opts);\n  } else {\n    if (!jsonp) throw new Error('JSONP disabled');\n    return new JSONP(opts);\n  }\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-client/lib/transports/index.js\n// module id = 18\n// module chunks = 0", "// browser shim for xmlhttprequest module\n\nvar hasCORS = require('has-cors');\n\nmodule.exports = function (opts) {\n  var xdomain = opts.xdomain;\n\n  // scheme must be same when usign XDomainRequest\n  // http://blogs.msdn.com/b/ieinternals/archive/2010/05/13/xdomainrequest-restrictions-limitations-and-workarounds.aspx\n  var xscheme = opts.xscheme;\n\n  // XDomainRequest has a flow of not sending cookie, therefore it should be disabled as a default.\n  // https://github.com/Automattic/engine.io-client/pull/217\n  var enablesXDR = opts.enablesXDR;\n\n  // XMLHttpRequest can be disabled on IE\n  try {\n    if ('undefined' !== typeof XMLHttpRequest && (!xdomain || hasCORS)) {\n      return new XMLHttpRequest();\n    }\n  } catch (e) { }\n\n  // Use XDomainRequest for IE8 if enablesXDR is true\n  // because loading bar keeps flashing when using jsonp-polling\n  // https://github.com/yujiosaka/socke.io-ie8-loading-example\n  try {\n    if ('undefined' !== typeof XDomainRequest && !xscheme && enablesXDR) {\n      return new XDomainRequest();\n    }\n  } catch (e) { }\n\n  if (!xdomain) {\n    try {\n      return new self[['Active'].concat('Object').join('X')]('Microsoft.XMLHTTP');\n    } catch (e) { }\n  }\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-client/lib/xmlhttprequest.js\n// module id = 19\n// module chunks = 0", "\n/**\n * Module exports.\n *\n * Logic borrowed from Modernizr:\n *\n *   - https://github.com/Modernizr/Modernizr/blob/master/feature-detects/cors.js\n */\n\ntry {\n  module.exports = typeof XMLHttpRequest !== 'undefined' &&\n    'withCredentials' in new XMLHttpRequest();\n} catch (err) {\n  // if XMLHttp support is disabled in IE then it will throw\n  // when trying to create\n  module.exports = false;\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/has-cors/index.js\n// module id = 20\n// module chunks = 0", "/* global attachEvent */\n\n/**\n * Module requirements.\n */\n\nvar XMLHttpRequest = require('xmlhttprequest-ssl');\nvar Polling = require('./polling');\nvar Emitter = require('component-emitter');\nvar inherit = require('component-inherit');\nvar debug = require('debug')('engine.io-client:polling-xhr');\n\n/**\n * Module exports.\n */\n\nmodule.exports = XHR;\nmodule.exports.Request = Request;\n\n/**\n * Empty function\n */\n\nfunction empty () {}\n\n/**\n * XHR Polling constructor.\n *\n * @param {Object} opts\n * @api public\n */\n\nfunction XHR (opts) {\n  Polling.call(this, opts);\n  this.requestTimeout = opts.requestTimeout;\n  this.extraHeaders = opts.extraHeaders;\n\n  if (typeof location !== 'undefined') {\n    var isSSL = 'https:' === location.protocol;\n    var port = location.port;\n\n    // some user agents have empty `location.port`\n    if (!port) {\n      port = isSSL ? 443 : 80;\n    }\n\n    this.xd = (typeof location !== 'undefined' && opts.hostname !== location.hostname) ||\n      port !== opts.port;\n    this.xs = opts.secure !== isSSL;\n  }\n}\n\n/**\n * Inherits from Polling.\n */\n\ninherit(XHR, Polling);\n\n/**\n * XHR supports binary\n */\n\nXHR.prototype.supportsBinary = true;\n\n/**\n * Creates a request.\n *\n * @param {String} method\n * @api private\n */\n\nXHR.prototype.request = function (opts) {\n  opts = opts || {};\n  opts.uri = this.uri();\n  opts.xd = this.xd;\n  opts.xs = this.xs;\n  opts.agent = this.agent || false;\n  opts.supportsBinary = this.supportsBinary;\n  opts.enablesXDR = this.enablesXDR;\n  opts.withCredentials = this.withCredentials;\n\n  // SSL options for Node.js client\n  opts.pfx = this.pfx;\n  opts.key = this.key;\n  opts.passphrase = this.passphrase;\n  opts.cert = this.cert;\n  opts.ca = this.ca;\n  opts.ciphers = this.ciphers;\n  opts.rejectUnauthorized = this.rejectUnauthorized;\n  opts.requestTimeout = this.requestTimeout;\n\n  // other options for Node.js client\n  opts.extraHeaders = this.extraHeaders;\n\n  return new Request(opts);\n};\n\n/**\n * Sends data.\n *\n * @param {String} data to send.\n * @param {Function} called upon flush.\n * @api private\n */\n\nXHR.prototype.doWrite = function (data, fn) {\n  var isBinary = typeof data !== 'string' && data !== undefined;\n  var req = this.request({ method: 'POST', data: data, isBinary: isBinary });\n  var self = this;\n  req.on('success', fn);\n  req.on('error', function (err) {\n    self.onError('xhr post error', err);\n  });\n  this.sendXhr = req;\n};\n\n/**\n * Starts a poll cycle.\n *\n * @api private\n */\n\nXHR.prototype.doPoll = function () {\n  debug('xhr poll');\n  var req = this.request();\n  var self = this;\n  req.on('data', function (data) {\n    self.onData(data);\n  });\n  req.on('error', function (err) {\n    self.onError('xhr poll error', err);\n  });\n  this.pollXhr = req;\n};\n\n/**\n * Request constructor\n *\n * @param {Object} options\n * @api public\n */\n\nfunction Request (opts) {\n  this.method = opts.method || 'GET';\n  this.uri = opts.uri;\n  this.xd = !!opts.xd;\n  this.xs = !!opts.xs;\n  this.async = false !== opts.async;\n  this.data = undefined !== opts.data ? opts.data : null;\n  this.agent = opts.agent;\n  this.isBinary = opts.isBinary;\n  this.supportsBinary = opts.supportsBinary;\n  this.enablesXDR = opts.enablesXDR;\n  this.withCredentials = opts.withCredentials;\n  this.requestTimeout = opts.requestTimeout;\n\n  // SSL options for Node.js client\n  this.pfx = opts.pfx;\n  this.key = opts.key;\n  this.passphrase = opts.passphrase;\n  this.cert = opts.cert;\n  this.ca = opts.ca;\n  this.ciphers = opts.ciphers;\n  this.rejectUnauthorized = opts.rejectUnauthorized;\n\n  // other options for Node.js client\n  this.extraHeaders = opts.extraHeaders;\n\n  this.create();\n}\n\n/**\n * Mix in `Emitter`.\n */\n\nEmitter(Request.prototype);\n\n/**\n * Creates the XHR object and sends the request.\n *\n * @api private\n */\n\nRequest.prototype.create = function () {\n  var opts = { agent: this.agent, xdomain: this.xd, xscheme: this.xs, enablesXDR: this.enablesXDR };\n\n  // SSL options for Node.js client\n  opts.pfx = this.pfx;\n  opts.key = this.key;\n  opts.passphrase = this.passphrase;\n  opts.cert = this.cert;\n  opts.ca = this.ca;\n  opts.ciphers = this.ciphers;\n  opts.rejectUnauthorized = this.rejectUnauthorized;\n\n  var xhr = this.xhr = new XMLHttpRequest(opts);\n  var self = this;\n\n  try {\n    debug('xhr open %s: %s', this.method, this.uri);\n    xhr.open(this.method, this.uri, this.async);\n    try {\n      if (this.extraHeaders) {\n        xhr.setDisableHeaderCheck && xhr.setDisableHeaderCheck(true);\n        for (var i in this.extraHeaders) {\n          if (this.extraHeaders.hasOwnProperty(i)) {\n            xhr.setRequestHeader(i, this.extraHeaders[i]);\n          }\n        }\n      }\n    } catch (e) {}\n\n    if ('POST' === this.method) {\n      try {\n        if (this.isBinary) {\n          xhr.setRequestHeader('Content-type', 'application/octet-stream');\n        } else {\n          xhr.setRequestHeader('Content-type', 'text/plain;charset=UTF-8');\n        }\n      } catch (e) {}\n    }\n\n    try {\n      xhr.setRequestHeader('Accept', '*/*');\n    } catch (e) {}\n\n    // ie6 check\n    if ('withCredentials' in xhr) {\n      xhr.withCredentials = this.withCredentials;\n    }\n\n    if (this.requestTimeout) {\n      xhr.timeout = this.requestTimeout;\n    }\n\n    if (this.hasXDR()) {\n      xhr.onload = function () {\n        self.onLoad();\n      };\n      xhr.onerror = function () {\n        self.onError(xhr.responseText);\n      };\n    } else {\n      xhr.onreadystatechange = function () {\n        if (xhr.readyState === 2) {\n          try {\n            var contentType = xhr.getResponseHeader('Content-Type');\n            if (self.supportsBinary && contentType === 'application/octet-stream' || contentType === 'application/octet-stream; charset=UTF-8') {\n              xhr.responseType = 'arraybuffer';\n            }\n          } catch (e) {}\n        }\n        if (4 !== xhr.readyState) return;\n        if (200 === xhr.status || 1223 === xhr.status) {\n          self.onLoad();\n        } else {\n          // make sure the `error` event handler that's user-set\n          // does not throw in the same tick and gets caught here\n          setTimeout(function () {\n            self.onError(typeof xhr.status === 'number' ? xhr.status : 0);\n          }, 0);\n        }\n      };\n    }\n\n    debug('xhr data %s', this.data);\n    xhr.send(this.data);\n  } catch (e) {\n    // Need to defer since .create() is called directly fhrom the constructor\n    // and thus the 'error' event can only be only bound *after* this exception\n    // occurs.  Therefore, also, we cannot throw here at all.\n    setTimeout(function () {\n      self.onError(e);\n    }, 0);\n    return;\n  }\n\n  if (typeof document !== 'undefined') {\n    this.index = Request.requestsCount++;\n    Request.requests[this.index] = this;\n  }\n};\n\n/**\n * Called upon successful response.\n *\n * @api private\n */\n\nRequest.prototype.onSuccess = function () {\n  this.emit('success');\n  this.cleanup();\n};\n\n/**\n * Called if we have data.\n *\n * @api private\n */\n\nRequest.prototype.onData = function (data) {\n  this.emit('data', data);\n  this.onSuccess();\n};\n\n/**\n * Called upon error.\n *\n * @api private\n */\n\nRequest.prototype.onError = function (err) {\n  this.emit('error', err);\n  this.cleanup(true);\n};\n\n/**\n * Cleans up house.\n *\n * @api private\n */\n\nRequest.prototype.cleanup = function (fromError) {\n  if ('undefined' === typeof this.xhr || null === this.xhr) {\n    return;\n  }\n  // xmlhttprequest\n  if (this.hasXDR()) {\n    this.xhr.onload = this.xhr.onerror = empty;\n  } else {\n    this.xhr.onreadystatechange = empty;\n  }\n\n  if (fromError) {\n    try {\n      this.xhr.abort();\n    } catch (e) {}\n  }\n\n  if (typeof document !== 'undefined') {\n    delete Request.requests[this.index];\n  }\n\n  this.xhr = null;\n};\n\n/**\n * Called upon load.\n *\n * @api private\n */\n\nRequest.prototype.onLoad = function () {\n  var data;\n  try {\n    var contentType;\n    try {\n      contentType = this.xhr.getResponseHeader('Content-Type');\n    } catch (e) {}\n    if (contentType === 'application/octet-stream' || contentType === 'application/octet-stream; charset=UTF-8') {\n      data = this.xhr.response || this.xhr.responseText;\n    } else {\n      data = this.xhr.responseText;\n    }\n  } catch (e) {\n    this.onError(e);\n  }\n  if (null != data) {\n    this.onData(data);\n  }\n};\n\n/**\n * Check if it has XDomainRequest.\n *\n * @api private\n */\n\nRequest.prototype.hasXDR = function () {\n  return typeof XDomainRequest !== 'undefined' && !this.xs && this.enablesXDR;\n};\n\n/**\n * Aborts the request.\n *\n * @api public\n */\n\nRequest.prototype.abort = function () {\n  this.cleanup();\n};\n\n/**\n * Aborts pending requests when unloading the window. This is needed to prevent\n * memory leaks (e.g. when using IE) and to ensure that no spurious error is\n * emitted.\n */\n\nRequest.requestsCount = 0;\nRequest.requests = {};\n\nif (typeof document !== 'undefined') {\n  if (typeof attachEvent === 'function') {\n    attachEvent('onunload', unloadHandler);\n  } else if (typeof addEventListener === 'function') {\n    var terminationEvent = 'onpagehide' in self ? 'pagehide' : 'unload';\n    addEventListener(terminationEvent, unloadHandler, false);\n  }\n}\n\nfunction unloadHandler () {\n  for (var i in Request.requests) {\n    if (Request.requests.hasOwnProperty(i)) {\n      Request.requests[i].abort();\n    }\n  }\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-client/lib/transports/polling-xhr.js\n// module id = 21\n// module chunks = 0", "/**\n * Module dependencies.\n */\n\nvar Transport = require('../transport');\nvar parseqs = require('parseqs');\nvar parser = require('engine.io-parser');\nvar inherit = require('component-inherit');\nvar yeast = require('yeast');\nvar debug = require('debug')('engine.io-client:polling');\n\n/**\n * Module exports.\n */\n\nmodule.exports = Polling;\n\n/**\n * Is XHR2 supported?\n */\n\nvar hasXHR2 = (function () {\n  var XMLHttpRequest = require('xmlhttprequest-ssl');\n  var xhr = new XMLHttpRequest({ xdomain: false });\n  return null != xhr.responseType;\n})();\n\n/**\n * Polling interface.\n *\n * @param {Object} opts\n * @api private\n */\n\nfunction Polling (opts) {\n  var forceBase64 = (opts && opts.forceBase64);\n  if (!hasXHR2 || forceBase64) {\n    this.supportsBinary = false;\n  }\n  Transport.call(this, opts);\n}\n\n/**\n * Inherits from Transport.\n */\n\ninherit(Polling, Transport);\n\n/**\n * Transport name.\n */\n\nPolling.prototype.name = 'polling';\n\n/**\n * Opens the socket (triggers polling). We write a PING message to determine\n * when the transport is open.\n *\n * @api private\n */\n\nPolling.prototype.doOpen = function () {\n  this.poll();\n};\n\n/**\n * Pauses polling.\n *\n * @param {Function} callback upon buffers are flushed and transport is paused\n * @api private\n */\n\nPolling.prototype.pause = function (onPause) {\n  var self = this;\n\n  this.readyState = 'pausing';\n\n  function pause () {\n    debug('paused');\n    self.readyState = 'paused';\n    onPause();\n  }\n\n  if (this.polling || !this.writable) {\n    var total = 0;\n\n    if (this.polling) {\n      debug('we are currently polling - waiting to pause');\n      total++;\n      this.once('pollComplete', function () {\n        debug('pre-pause polling complete');\n        --total || pause();\n      });\n    }\n\n    if (!this.writable) {\n      debug('we are currently writing - waiting to pause');\n      total++;\n      this.once('drain', function () {\n        debug('pre-pause writing complete');\n        --total || pause();\n      });\n    }\n  } else {\n    pause();\n  }\n};\n\n/**\n * Starts polling cycle.\n *\n * @api public\n */\n\nPolling.prototype.poll = function () {\n  debug('polling');\n  this.polling = true;\n  this.doPoll();\n  this.emit('poll');\n};\n\n/**\n * Overloads onData to detect payloads.\n *\n * @api private\n */\n\nPolling.prototype.onData = function (data) {\n  var self = this;\n  debug('polling got data %s', data);\n  var callback = function (packet, index, total) {\n    // if its the first message we consider the transport open\n    if ('opening' === self.readyState) {\n      self.onOpen();\n    }\n\n    // if its a close packet, we close the ongoing requests\n    if ('close' === packet.type) {\n      self.onClose();\n      return false;\n    }\n\n    // otherwise bypass onData and handle the message\n    self.onPacket(packet);\n  };\n\n  // decode payload\n  parser.decodePayload(data, this.socket.binaryType, callback);\n\n  // if an event did not trigger closing\n  if ('closed' !== this.readyState) {\n    // if we got data we're not polling\n    this.polling = false;\n    this.emit('pollComplete');\n\n    if ('open' === this.readyState) {\n      this.poll();\n    } else {\n      debug('ignoring poll - transport state \"%s\"', this.readyState);\n    }\n  }\n};\n\n/**\n * For polling, send a close packet.\n *\n * @api private\n */\n\nPolling.prototype.doClose = function () {\n  var self = this;\n\n  function close () {\n    debug('writing close packet');\n    self.write([{ type: 'close' }]);\n  }\n\n  if ('open' === this.readyState) {\n    debug('transport open - closing');\n    close();\n  } else {\n    // in case we're trying to close while\n    // handshaking is in progress (GH-164)\n    debug('transport not open - deferring close');\n    this.once('open', close);\n  }\n};\n\n/**\n * Writes a packets payload.\n *\n * @param {Array} data packets\n * @param {Function} drain callback\n * @api private\n */\n\nPolling.prototype.write = function (packets) {\n  var self = this;\n  this.writable = false;\n  var callbackfn = function () {\n    self.writable = true;\n    self.emit('drain');\n  };\n\n  parser.encodePayload(packets, this.supportsBinary, function (data) {\n    self.doWrite(data, callbackfn);\n  });\n};\n\n/**\n * Generates uri for connection.\n *\n * @api private\n */\n\nPolling.prototype.uri = function () {\n  var query = this.query || {};\n  var schema = this.secure ? 'https' : 'http';\n  var port = '';\n\n  // cache busting is forced\n  if (false !== this.timestampRequests) {\n    query[this.timestampParam] = yeast();\n  }\n\n  if (!this.supportsBinary && !query.sid) {\n    query.b64 = 1;\n  }\n\n  query = parseqs.encode(query);\n\n  // avoid port if default for schema\n  if (this.port && (('https' === schema && Number(this.port) !== 443) ||\n     ('http' === schema && Number(this.port) !== 80))) {\n    port = ':' + this.port;\n  }\n\n  // prepend ? to query\n  if (query.length) {\n    query = '?' + query;\n  }\n\n  var ipv6 = this.hostname.indexOf(':') !== -1;\n  return schema + '://' + (ipv6 ? '[' + this.hostname + ']' : this.hostname) + port + this.path + query;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-client/lib/transports/polling.js\n// module id = 22\n// module chunks = 0", "/**\n * Module dependencies.\n */\n\nvar parser = require('engine.io-parser');\nvar Emitter = require('component-emitter');\n\n/**\n * Module exports.\n */\n\nmodule.exports = Transport;\n\n/**\n * Transport abstract constructor.\n *\n * @param {Object} options.\n * @api private\n */\n\nfunction Transport (opts) {\n  this.path = opts.path;\n  this.hostname = opts.hostname;\n  this.port = opts.port;\n  this.secure = opts.secure;\n  this.query = opts.query;\n  this.timestampParam = opts.timestampParam;\n  this.timestampRequests = opts.timestampRequests;\n  this.readyState = '';\n  this.agent = opts.agent || false;\n  this.socket = opts.socket;\n  this.enablesXDR = opts.enablesXDR;\n  this.withCredentials = opts.withCredentials;\n\n  // SSL options for Node.js client\n  this.pfx = opts.pfx;\n  this.key = opts.key;\n  this.passphrase = opts.passphrase;\n  this.cert = opts.cert;\n  this.ca = opts.ca;\n  this.ciphers = opts.ciphers;\n  this.rejectUnauthorized = opts.rejectUnauthorized;\n  this.forceNode = opts.forceNode;\n\n  // results of ReactNative environment detection\n  this.isReactNative = opts.isReactNative;\n\n  // other options for Node.js client\n  this.extraHeaders = opts.extraHeaders;\n  this.localAddress = opts.localAddress;\n}\n\n/**\n * Mix in `Emitter`.\n */\n\nEmitter(Transport.prototype);\n\n/**\n * Emits an error.\n *\n * @param {String} str\n * @return {Transport} for chaining\n * @api public\n */\n\nTransport.prototype.onError = function (msg, desc) {\n  var err = new Error(msg);\n  err.type = 'TransportError';\n  err.description = desc;\n  this.emit('error', err);\n  return this;\n};\n\n/**\n * Opens the transport.\n *\n * @api public\n */\n\nTransport.prototype.open = function () {\n  if ('closed' === this.readyState || '' === this.readyState) {\n    this.readyState = 'opening';\n    this.doOpen();\n  }\n\n  return this;\n};\n\n/**\n * Closes the transport.\n *\n * @api private\n */\n\nTransport.prototype.close = function () {\n  if ('opening' === this.readyState || 'open' === this.readyState) {\n    this.doClose();\n    this.onClose();\n  }\n\n  return this;\n};\n\n/**\n * Sends multiple packets.\n *\n * @param {Array} packets\n * @api private\n */\n\nTransport.prototype.send = function (packets) {\n  if ('open' === this.readyState) {\n    this.write(packets);\n  } else {\n    throw new Error('Transport not open');\n  }\n};\n\n/**\n * Called upon open\n *\n * @api private\n */\n\nTransport.prototype.onOpen = function () {\n  this.readyState = 'open';\n  this.writable = true;\n  this.emit('open');\n};\n\n/**\n * Called with data.\n *\n * @param {String} data\n * @api private\n */\n\nTransport.prototype.onData = function (data) {\n  var packet = parser.decodePacket(data, this.socket.binaryType);\n  this.onPacket(packet);\n};\n\n/**\n * Called with a decoded packet.\n */\n\nTransport.prototype.onPacket = function (packet) {\n  this.emit('packet', packet);\n};\n\n/**\n * Called upon close.\n *\n * @api private\n */\n\nTransport.prototype.onClose = function () {\n  this.readyState = 'closed';\n  this.emit('close');\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-client/lib/transport.js\n// module id = 23\n// module chunks = 0", "/**\n * Module dependencies.\n */\n\nvar keys = require('./keys');\nvar hasBinary = require('has-binary2');\nvar sliceBuffer = require('arraybuffer.slice');\nvar after = require('after');\nvar utf8 = require('./utf8');\n\nvar base64encoder;\nif (typeof ArrayBuffer !== 'undefined') {\n  base64encoder = require('base64-arraybuffer');\n}\n\n/**\n * Check if we are running an android browser. That requires us to use\n * ArrayBuffer with polling transports...\n *\n * http://ghinda.net/jpeg-blob-ajax-android/\n */\n\nvar isAndroid = typeof navigator !== 'undefined' && /Android/i.test(navigator.userAgent);\n\n/**\n * Check if we are running in PhantomJS.\n * Uploading a Blob with PhantomJS does not work correctly, as reported here:\n * https://github.com/ariya/phantomjs/issues/11395\n * @type boolean\n */\nvar isPhantomJS = typeof navigator !== 'undefined' && /PhantomJS/i.test(navigator.userAgent);\n\n/**\n * When true, avoids using Blobs to encode payloads.\n * @type boolean\n */\nvar dontSendBlobs = isAndroid || isPhantomJS;\n\n/**\n * Current protocol version.\n */\n\nexports.protocol = 3;\n\n/**\n * Packet types.\n */\n\nvar packets = exports.packets = {\n    open:     0    // non-ws\n  , close:    1    // non-ws\n  , ping:     2\n  , pong:     3\n  , message:  4\n  , upgrade:  5\n  , noop:     6\n};\n\nvar packetslist = keys(packets);\n\n/**\n * Premade error packet.\n */\n\nvar err = { type: 'error', data: 'parser error' };\n\n/**\n * Create a blob api even for blob builder when vendor prefixes exist\n */\n\nvar Blob = require('blob');\n\n/**\n * Encodes a packet.\n *\n *     <packet type id> [ <data> ]\n *\n * Example:\n *\n *     5hello world\n *     3\n *     4\n *\n * Binary is encoded in an identical principle\n *\n * @api private\n */\n\nexports.encodePacket = function (packet, supportsBinary, utf8encode, callback) {\n  if (typeof supportsBinary === 'function') {\n    callback = supportsBinary;\n    supportsBinary = false;\n  }\n\n  if (typeof utf8encode === 'function') {\n    callback = utf8encode;\n    utf8encode = null;\n  }\n\n  var data = (packet.data === undefined)\n    ? undefined\n    : packet.data.buffer || packet.data;\n\n  if (typeof ArrayBuffer !== 'undefined' && data instanceof ArrayBuffer) {\n    return encodeArrayBuffer(packet, supportsBinary, callback);\n  } else if (typeof Blob !== 'undefined' && data instanceof Blob) {\n    return encodeBlob(packet, supportsBinary, callback);\n  }\n\n  // might be an object with { base64: true, data: dataAsBase64String }\n  if (data && data.base64) {\n    return encodeBase64Object(packet, callback);\n  }\n\n  // Sending data as a utf-8 string\n  var encoded = packets[packet.type];\n\n  // data fragment is optional\n  if (undefined !== packet.data) {\n    encoded += utf8encode ? utf8.encode(String(packet.data), { strict: false }) : String(packet.data);\n  }\n\n  return callback('' + encoded);\n\n};\n\nfunction encodeBase64Object(packet, callback) {\n  // packet data is an object { base64: true, data: dataAsBase64String }\n  var message = 'b' + exports.packets[packet.type] + packet.data.data;\n  return callback(message);\n}\n\n/**\n * Encode packet helpers for binary types\n */\n\nfunction encodeArrayBuffer(packet, supportsBinary, callback) {\n  if (!supportsBinary) {\n    return exports.encodeBase64Packet(packet, callback);\n  }\n\n  var data = packet.data;\n  var contentArray = new Uint8Array(data);\n  var resultBuffer = new Uint8Array(1 + data.byteLength);\n\n  resultBuffer[0] = packets[packet.type];\n  for (var i = 0; i < contentArray.length; i++) {\n    resultBuffer[i+1] = contentArray[i];\n  }\n\n  return callback(resultBuffer.buffer);\n}\n\nfunction encodeBlobAsArrayBuffer(packet, supportsBinary, callback) {\n  if (!supportsBinary) {\n    return exports.encodeBase64Packet(packet, callback);\n  }\n\n  var fr = new FileReader();\n  fr.onload = function() {\n    exports.encodePacket({ type: packet.type, data: fr.result }, supportsBinary, true, callback);\n  };\n  return fr.readAsArrayBuffer(packet.data);\n}\n\nfunction encodeBlob(packet, supportsBinary, callback) {\n  if (!supportsBinary) {\n    return exports.encodeBase64Packet(packet, callback);\n  }\n\n  if (dontSendBlobs) {\n    return encodeBlobAsArrayBuffer(packet, supportsBinary, callback);\n  }\n\n  var length = new Uint8Array(1);\n  length[0] = packets[packet.type];\n  var blob = new Blob([length.buffer, packet.data]);\n\n  return callback(blob);\n}\n\n/**\n * Encodes a packet with binary data in a base64 string\n *\n * @param {Object} packet, has `type` and `data`\n * @return {String} base64 encoded message\n */\n\nexports.encodeBase64Packet = function(packet, callback) {\n  var message = 'b' + exports.packets[packet.type];\n  if (typeof Blob !== 'undefined' && packet.data instanceof Blob) {\n    var fr = new FileReader();\n    fr.onload = function() {\n      var b64 = fr.result.split(',')[1];\n      callback(message + b64);\n    };\n    return fr.readAsDataURL(packet.data);\n  }\n\n  var b64data;\n  try {\n    b64data = String.fromCharCode.apply(null, new Uint8Array(packet.data));\n  } catch (e) {\n    // iPhone Safari doesn't let you apply with typed arrays\n    var typed = new Uint8Array(packet.data);\n    var basic = new Array(typed.length);\n    for (var i = 0; i < typed.length; i++) {\n      basic[i] = typed[i];\n    }\n    b64data = String.fromCharCode.apply(null, basic);\n  }\n  message += btoa(b64data);\n  return callback(message);\n};\n\n/**\n * Decodes a packet. Changes format to Blob if requested.\n *\n * @return {Object} with `type` and `data` (if any)\n * @api private\n */\n\nexports.decodePacket = function (data, binaryType, utf8decode) {\n  if (data === undefined) {\n    return err;\n  }\n  // String data\n  if (typeof data === 'string') {\n    if (data.charAt(0) === 'b') {\n      return exports.decodeBase64Packet(data.substr(1), binaryType);\n    }\n\n    if (utf8decode) {\n      data = tryDecode(data);\n      if (data === false) {\n        return err;\n      }\n    }\n    var type = data.charAt(0);\n\n    if (Number(type) != type || !packetslist[type]) {\n      return err;\n    }\n\n    if (data.length > 1) {\n      return { type: packetslist[type], data: data.substring(1) };\n    } else {\n      return { type: packetslist[type] };\n    }\n  }\n\n  var asArray = new Uint8Array(data);\n  var type = asArray[0];\n  var rest = sliceBuffer(data, 1);\n  if (Blob && binaryType === 'blob') {\n    rest = new Blob([rest]);\n  }\n  return { type: packetslist[type], data: rest };\n};\n\nfunction tryDecode(data) {\n  try {\n    data = utf8.decode(data, { strict: false });\n  } catch (e) {\n    return false;\n  }\n  return data;\n}\n\n/**\n * Decodes a packet encoded in a base64 string\n *\n * @param {String} base64 encoded message\n * @return {Object} with `type` and `data` (if any)\n */\n\nexports.decodeBase64Packet = function(msg, binaryType) {\n  var type = packetslist[msg.charAt(0)];\n  if (!base64encoder) {\n    return { type: type, data: { base64: true, data: msg.substr(1) } };\n  }\n\n  var data = base64encoder.decode(msg.substr(1));\n\n  if (binaryType === 'blob' && Blob) {\n    data = new Blob([data]);\n  }\n\n  return { type: type, data: data };\n};\n\n/**\n * Encodes multiple messages (payload).\n *\n *     <length>:data\n *\n * Example:\n *\n *     11:hello world2:hi\n *\n * If any contents are binary, they will be encoded as base64 strings. Base64\n * encoded strings are marked with a b before the length specifier\n *\n * @param {Array} packets\n * @api private\n */\n\nexports.encodePayload = function (packets, supportsBinary, callback) {\n  if (typeof supportsBinary === 'function') {\n    callback = supportsBinary;\n    supportsBinary = null;\n  }\n\n  var isBinary = hasBinary(packets);\n\n  if (supportsBinary && isBinary) {\n    if (Blob && !dontSendBlobs) {\n      return exports.encodePayloadAsBlob(packets, callback);\n    }\n\n    return exports.encodePayloadAsArrayBuffer(packets, callback);\n  }\n\n  if (!packets.length) {\n    return callback('0:');\n  }\n\n  function setLengthHeader(message) {\n    return message.length + ':' + message;\n  }\n\n  function encodeOne(packet, doneCallback) {\n    exports.encodePacket(packet, !isBinary ? false : supportsBinary, false, function(message) {\n      doneCallback(null, setLengthHeader(message));\n    });\n  }\n\n  map(packets, encodeOne, function(err, results) {\n    return callback(results.join(''));\n  });\n};\n\n/**\n * Async array map using after\n */\n\nfunction map(ary, each, done) {\n  var result = new Array(ary.length);\n  var next = after(ary.length, done);\n\n  var eachWithIndex = function(i, el, cb) {\n    each(el, function(error, msg) {\n      result[i] = msg;\n      cb(error, result);\n    });\n  };\n\n  for (var i = 0; i < ary.length; i++) {\n    eachWithIndex(i, ary[i], next);\n  }\n}\n\n/*\n * Decodes data when a payload is maybe expected. Possible binary contents are\n * decoded from their base64 representation\n *\n * @param {String} data, callback method\n * @api public\n */\n\nexports.decodePayload = function (data, binaryType, callback) {\n  if (typeof data !== 'string') {\n    return exports.decodePayloadAsBinary(data, binaryType, callback);\n  }\n\n  if (typeof binaryType === 'function') {\n    callback = binaryType;\n    binaryType = null;\n  }\n\n  var packet;\n  if (data === '') {\n    // parser error - ignoring payload\n    return callback(err, 0, 1);\n  }\n\n  var length = '', n, msg;\n\n  for (var i = 0, l = data.length; i < l; i++) {\n    var chr = data.charAt(i);\n\n    if (chr !== ':') {\n      length += chr;\n      continue;\n    }\n\n    if (length === '' || (length != (n = Number(length)))) {\n      // parser error - ignoring payload\n      return callback(err, 0, 1);\n    }\n\n    msg = data.substr(i + 1, n);\n\n    if (length != msg.length) {\n      // parser error - ignoring payload\n      return callback(err, 0, 1);\n    }\n\n    if (msg.length) {\n      packet = exports.decodePacket(msg, binaryType, false);\n\n      if (err.type === packet.type && err.data === packet.data) {\n        // parser error in individual packet - ignoring payload\n        return callback(err, 0, 1);\n      }\n\n      var ret = callback(packet, i + n, l);\n      if (false === ret) return;\n    }\n\n    // advance cursor\n    i += n;\n    length = '';\n  }\n\n  if (length !== '') {\n    // parser error - ignoring payload\n    return callback(err, 0, 1);\n  }\n\n};\n\n/**\n * Encodes multiple messages (payload) as binary.\n *\n * <1 = binary, 0 = string><number from 0-9><number from 0-9>[...]<number\n * 255><data>\n *\n * Example:\n * 1 3 255 1 2 3, if the binary contents are interpreted as 8 bit integers\n *\n * @param {Array} packets\n * @return {ArrayBuffer} encoded payload\n * @api private\n */\n\nexports.encodePayloadAsArrayBuffer = function(packets, callback) {\n  if (!packets.length) {\n    return callback(new ArrayBuffer(0));\n  }\n\n  function encodeOne(packet, doneCallback) {\n    exports.encodePacket(packet, true, true, function(data) {\n      return doneCallback(null, data);\n    });\n  }\n\n  map(packets, encodeOne, function(err, encodedPackets) {\n    var totalLength = encodedPackets.reduce(function(acc, p) {\n      var len;\n      if (typeof p === 'string'){\n        len = p.length;\n      } else {\n        len = p.byteLength;\n      }\n      return acc + len.toString().length + len + 2; // string/binary identifier + separator = 2\n    }, 0);\n\n    var resultArray = new Uint8Array(totalLength);\n\n    var bufferIndex = 0;\n    encodedPackets.forEach(function(p) {\n      var isString = typeof p === 'string';\n      var ab = p;\n      if (isString) {\n        var view = new Uint8Array(p.length);\n        for (var i = 0; i < p.length; i++) {\n          view[i] = p.charCodeAt(i);\n        }\n        ab = view.buffer;\n      }\n\n      if (isString) { // not true binary\n        resultArray[bufferIndex++] = 0;\n      } else { // true binary\n        resultArray[bufferIndex++] = 1;\n      }\n\n      var lenStr = ab.byteLength.toString();\n      for (var i = 0; i < lenStr.length; i++) {\n        resultArray[bufferIndex++] = parseInt(lenStr[i]);\n      }\n      resultArray[bufferIndex++] = 255;\n\n      var view = new Uint8Array(ab);\n      for (var i = 0; i < view.length; i++) {\n        resultArray[bufferIndex++] = view[i];\n      }\n    });\n\n    return callback(resultArray.buffer);\n  });\n};\n\n/**\n * Encode as Blob\n */\n\nexports.encodePayloadAsBlob = function(packets, callback) {\n  function encodeOne(packet, doneCallback) {\n    exports.encodePacket(packet, true, true, function(encoded) {\n      var binaryIdentifier = new Uint8Array(1);\n      binaryIdentifier[0] = 1;\n      if (typeof encoded === 'string') {\n        var view = new Uint8Array(encoded.length);\n        for (var i = 0; i < encoded.length; i++) {\n          view[i] = encoded.charCodeAt(i);\n        }\n        encoded = view.buffer;\n        binaryIdentifier[0] = 0;\n      }\n\n      var len = (encoded instanceof ArrayBuffer)\n        ? encoded.byteLength\n        : encoded.size;\n\n      var lenStr = len.toString();\n      var lengthAry = new Uint8Array(lenStr.length + 1);\n      for (var i = 0; i < lenStr.length; i++) {\n        lengthAry[i] = parseInt(lenStr[i]);\n      }\n      lengthAry[lenStr.length] = 255;\n\n      if (Blob) {\n        var blob = new Blob([binaryIdentifier.buffer, lengthAry.buffer, encoded]);\n        doneCallback(null, blob);\n      }\n    });\n  }\n\n  map(packets, encodeOne, function(err, results) {\n    return callback(new Blob(results));\n  });\n};\n\n/*\n * Decodes data when a payload is maybe expected. Strings are decoded by\n * interpreting each byte as a key code for entries marked to start with 0. See\n * description of encodePayloadAsBinary\n *\n * @param {ArrayBuffer} data, callback method\n * @api public\n */\n\nexports.decodePayloadAsBinary = function (data, binaryType, callback) {\n  if (typeof binaryType === 'function') {\n    callback = binaryType;\n    binaryType = null;\n  }\n\n  var bufferTail = data;\n  var buffers = [];\n\n  while (bufferTail.byteLength > 0) {\n    var tailArray = new Uint8Array(bufferTail);\n    var isString = tailArray[0] === 0;\n    var msgLength = '';\n\n    for (var i = 1; ; i++) {\n      if (tailArray[i] === 255) break;\n\n      // 310 = char length of Number.MAX_VALUE\n      if (msgLength.length > 310) {\n        return callback(err, 0, 1);\n      }\n\n      msgLength += tailArray[i];\n    }\n\n    bufferTail = sliceBuffer(bufferTail, 2 + msgLength.length);\n    msgLength = parseInt(msgLength);\n\n    var msg = sliceBuffer(bufferTail, 0, msgLength);\n    if (isString) {\n      try {\n        msg = String.fromCharCode.apply(null, new Uint8Array(msg));\n      } catch (e) {\n        // iPhone Safari doesn't let you apply to typed arrays\n        var typed = new Uint8Array(msg);\n        msg = '';\n        for (var i = 0; i < typed.length; i++) {\n          msg += String.fromCharCode(typed[i]);\n        }\n      }\n    }\n\n    buffers.push(msg);\n    bufferTail = sliceBuffer(bufferTail, msgLength);\n  }\n\n  var total = buffers.length;\n  buffers.forEach(function(buffer, i) {\n    callback(exports.decodePacket(buffer, binaryType, true), i, total);\n  });\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-parser/lib/browser.js\n// module id = 24\n// module chunks = 0", "\n/**\n * Gets the keys for an object.\n *\n * @return {Array} keys\n * @api private\n */\n\nmodule.exports = Object.keys || function keys (obj){\n  var arr = [];\n  var has = Object.prototype.hasOwnProperty;\n\n  for (var i in obj) {\n    if (has.call(obj, i)) {\n      arr.push(i);\n    }\n  }\n  return arr;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-parser/lib/keys.js\n// module id = 25\n// module chunks = 0", "/* global Blob File */\n\n/*\n * Module requirements.\n */\n\nvar isArray = require('isarray');\n\nvar toString = Object.prototype.toString;\nvar withNativeBlob = typeof Blob === 'function' ||\n                        typeof Blob !== 'undefined' && toString.call(Blob) === '[object BlobConstructor]';\nvar withNativeFile = typeof File === 'function' ||\n                        typeof File !== 'undefined' && toString.call(File) === '[object FileConstructor]';\n\n/**\n * Module exports.\n */\n\nmodule.exports = hasBinary;\n\n/**\n * Checks for binary data.\n *\n * Supports Buffer, ArrayBuffer, Blob and File.\n *\n * @param {Object} anything\n * @api public\n */\n\nfunction hasBinary (obj) {\n  if (!obj || typeof obj !== 'object') {\n    return false;\n  }\n\n  if (isArray(obj)) {\n    for (var i = 0, l = obj.length; i < l; i++) {\n      if (hasBinary(obj[i])) {\n        return true;\n      }\n    }\n    return false;\n  }\n\n  if ((typeof Buffer === 'function' && Buffer.isBuffer && Buffer.isBuffer(obj)) ||\n    (typeof ArrayBuffer === 'function' && obj instanceof ArrayBuffer) ||\n    (withNativeBlob && obj instanceof Blob) ||\n    (withNativeFile && obj instanceof File)\n  ) {\n    return true;\n  }\n\n  // see: https://github.com/Automattic/has-binary/pull/4\n  if (obj.toJSON && typeof obj.toJSON === 'function' && arguments.length === 1) {\n    return hasBinary(obj.toJSON(), true);\n  }\n\n  for (var key in obj) {\n    if (Object.prototype.hasOwnProperty.call(obj, key) && hasBinary(obj[key])) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/has-binary2/index.js\n// module id = 26\n// module chunks = 0", "/**\n * An abstraction for slicing an arraybuffer even when\n * ArrayBuffer.prototype.slice is not supported\n *\n * @api public\n */\n\nmodule.exports = function(arraybuffer, start, end) {\n  var bytes = arraybuffer.byteLength;\n  start = start || 0;\n  end = end || bytes;\n\n  if (arraybuffer.slice) { return arraybuffer.slice(start, end); }\n\n  if (start < 0) { start += bytes; }\n  if (end < 0) { end += bytes; }\n  if (end > bytes) { end = bytes; }\n\n  if (start >= bytes || start >= end || bytes === 0) {\n    return new ArrayBuffer(0);\n  }\n\n  var abv = new Uint8Array(arraybuffer);\n  var result = new Uint8Array(end - start);\n  for (var i = start, ii = 0; i < end; i++, ii++) {\n    result[ii] = abv[i];\n  }\n  return result.buffer;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/arraybuffer.slice/index.js\n// module id = 27\n// module chunks = 0", "module.exports = after\n\nfunction after(count, callback, err_cb) {\n    var bail = false\n    err_cb = err_cb || noop\n    proxy.count = count\n\n    return (count === 0) ? callback() : proxy\n\n    function proxy(err, result) {\n        if (proxy.count <= 0) {\n            throw new Error('after called too many times')\n        }\n        --proxy.count\n\n        // after first error, rest are passed to err_cb\n        if (err) {\n            bail = true\n            callback(err)\n            // future error callbacks will go to error handler\n            callback = err_cb\n        } else if (proxy.count === 0 && !bail) {\n            callback(null, result)\n        }\n    }\n}\n\nfunction noop() {}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/after/index.js\n// module id = 28\n// module chunks = 0", "/*! https://mths.be/utf8js v2.1.2 by @mathias */\n\nvar stringFromCharCode = String.fromCharCode;\n\n// Taken from https://mths.be/punycode\nfunction ucs2decode(string) {\n\tvar output = [];\n\tvar counter = 0;\n\tvar length = string.length;\n\tvar value;\n\tvar extra;\n\twhile (counter < length) {\n\t\tvalue = string.charCodeAt(counter++);\n\t\tif (value >= 0xD800 && value <= 0xDBFF && counter < length) {\n\t\t\t// high surrogate, and there is a next character\n\t\t\textra = string.charCodeAt(counter++);\n\t\t\tif ((extra & 0xFC00) == 0xDC00) { // low surrogate\n\t\t\t\toutput.push(((value & 0x3FF) << 10) + (extra & 0x3FF) + 0x10000);\n\t\t\t} else {\n\t\t\t\t// unmatched surrogate; only append this code unit, in case the next\n\t\t\t\t// code unit is the high surrogate of a surrogate pair\n\t\t\t\toutput.push(value);\n\t\t\t\tcounter--;\n\t\t\t}\n\t\t} else {\n\t\t\toutput.push(value);\n\t\t}\n\t}\n\treturn output;\n}\n\n// Taken from https://mths.be/punycode\nfunction ucs2encode(array) {\n\tvar length = array.length;\n\tvar index = -1;\n\tvar value;\n\tvar output = '';\n\twhile (++index < length) {\n\t\tvalue = array[index];\n\t\tif (value > 0xFFFF) {\n\t\t\tvalue -= 0x10000;\n\t\t\toutput += stringFromCharCode(value >>> 10 & 0x3FF | 0xD800);\n\t\t\tvalue = 0xDC00 | value & 0x3FF;\n\t\t}\n\t\toutput += stringFromCharCode(value);\n\t}\n\treturn output;\n}\n\nfunction checkScalarValue(codePoint, strict) {\n\tif (codePoint >= 0xD800 && codePoint <= 0xDFFF) {\n\t\tif (strict) {\n\t\t\tthrow Error(\n\t\t\t\t'Lone surrogate U+' + codePoint.toString(16).toUpperCase() +\n\t\t\t\t' is not a scalar value'\n\t\t\t);\n\t\t}\n\t\treturn false;\n\t}\n\treturn true;\n}\n/*--------------------------------------------------------------------------*/\n\nfunction createByte(codePoint, shift) {\n\treturn stringFromCharCode(((codePoint >> shift) & 0x3F) | 0x80);\n}\n\nfunction encodeCodePoint(codePoint, strict) {\n\tif ((codePoint & 0xFFFFFF80) == 0) { // 1-byte sequence\n\t\treturn stringFromCharCode(codePoint);\n\t}\n\tvar symbol = '';\n\tif ((codePoint & 0xFFFFF800) == 0) { // 2-byte sequence\n\t\tsymbol = stringFromCharCode(((codePoint >> 6) & 0x1F) | 0xC0);\n\t}\n\telse if ((codePoint & 0xFFFF0000) == 0) { // 3-byte sequence\n\t\tif (!checkScalarValue(codePoint, strict)) {\n\t\t\tcodePoint = 0xFFFD;\n\t\t}\n\t\tsymbol = stringFromCharCode(((codePoint >> 12) & 0x0F) | 0xE0);\n\t\tsymbol += createByte(codePoint, 6);\n\t}\n\telse if ((codePoint & 0xFFE00000) == 0) { // 4-byte sequence\n\t\tsymbol = stringFromCharCode(((codePoint >> 18) & 0x07) | 0xF0);\n\t\tsymbol += createByte(codePoint, 12);\n\t\tsymbol += createByte(codePoint, 6);\n\t}\n\tsymbol += stringFromCharCode((codePoint & 0x3F) | 0x80);\n\treturn symbol;\n}\n\nfunction utf8encode(string, opts) {\n\topts = opts || {};\n\tvar strict = false !== opts.strict;\n\n\tvar codePoints = ucs2decode(string);\n\tvar length = codePoints.length;\n\tvar index = -1;\n\tvar codePoint;\n\tvar byteString = '';\n\twhile (++index < length) {\n\t\tcodePoint = codePoints[index];\n\t\tbyteString += encodeCodePoint(codePoint, strict);\n\t}\n\treturn byteString;\n}\n\n/*--------------------------------------------------------------------------*/\n\nfunction readContinuationByte() {\n\tif (byteIndex >= byteCount) {\n\t\tthrow Error('Invalid byte index');\n\t}\n\n\tvar continuationByte = byteArray[byteIndex] & 0xFF;\n\tbyteIndex++;\n\n\tif ((continuationByte & 0xC0) == 0x80) {\n\t\treturn continuationByte & 0x3F;\n\t}\n\n\t// If we end up here, it’s not a continuation byte\n\tthrow Error('Invalid continuation byte');\n}\n\nfunction decodeSymbol(strict) {\n\tvar byte1;\n\tvar byte2;\n\tvar byte3;\n\tvar byte4;\n\tvar codePoint;\n\n\tif (byteIndex > byteCount) {\n\t\tthrow Error('Invalid byte index');\n\t}\n\n\tif (byteIndex == byteCount) {\n\t\treturn false;\n\t}\n\n\t// Read first byte\n\tbyte1 = byteArray[byteIndex] & 0xFF;\n\tbyteIndex++;\n\n\t// 1-byte sequence (no continuation bytes)\n\tif ((byte1 & 0x80) == 0) {\n\t\treturn byte1;\n\t}\n\n\t// 2-byte sequence\n\tif ((byte1 & 0xE0) == 0xC0) {\n\t\tbyte2 = readContinuationByte();\n\t\tcodePoint = ((byte1 & 0x1F) << 6) | byte2;\n\t\tif (codePoint >= 0x80) {\n\t\t\treturn codePoint;\n\t\t} else {\n\t\t\tthrow Error('Invalid continuation byte');\n\t\t}\n\t}\n\n\t// 3-byte sequence (may include unpaired surrogates)\n\tif ((byte1 & 0xF0) == 0xE0) {\n\t\tbyte2 = readContinuationByte();\n\t\tbyte3 = readContinuationByte();\n\t\tcodePoint = ((byte1 & 0x0F) << 12) | (byte2 << 6) | byte3;\n\t\tif (codePoint >= 0x0800) {\n\t\t\treturn checkScalarValue(codePoint, strict) ? codePoint : 0xFFFD;\n\t\t} else {\n\t\t\tthrow Error('Invalid continuation byte');\n\t\t}\n\t}\n\n\t// 4-byte sequence\n\tif ((byte1 & 0xF8) == 0xF0) {\n\t\tbyte2 = readContinuationByte();\n\t\tbyte3 = readContinuationByte();\n\t\tbyte4 = readContinuationByte();\n\t\tcodePoint = ((byte1 & 0x07) << 0x12) | (byte2 << 0x0C) |\n\t\t\t(byte3 << 0x06) | byte4;\n\t\tif (codePoint >= 0x010000 && codePoint <= 0x10FFFF) {\n\t\t\treturn codePoint;\n\t\t}\n\t}\n\n\tthrow Error('Invalid UTF-8 detected');\n}\n\nvar byteArray;\nvar byteCount;\nvar byteIndex;\nfunction utf8decode(byteString, opts) {\n\topts = opts || {};\n\tvar strict = false !== opts.strict;\n\n\tbyteArray = ucs2decode(byteString);\n\tbyteCount = byteArray.length;\n\tbyteIndex = 0;\n\tvar codePoints = [];\n\tvar tmp;\n\twhile ((tmp = decodeSymbol(strict)) !== false) {\n\t\tcodePoints.push(tmp);\n\t}\n\treturn ucs2encode(codePoints);\n}\n\nmodule.exports = {\n\tversion: '2.1.2',\n\tencode: utf8encode,\n\tdecode: utf8decode\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-parser/lib/utf8.js\n// module id = 29\n// module chunks = 0", "/*\n * base64-arraybuffer\n * https://github.com/niklasvh/base64-arraybuffer\n *\n * Copyright (c) 2012 <PERSON><PERSON>\n * Licensed under the MIT license.\n */\n(function(){\n  \"use strict\";\n\n  var chars = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\";\n\n  // Use a lookup table to find the index.\n  var lookup = new Uint8Array(256);\n  for (var i = 0; i < chars.length; i++) {\n    lookup[chars.charCodeAt(i)] = i;\n  }\n\n  exports.encode = function(arraybuffer) {\n    var bytes = new Uint8Array(arraybuffer),\n    i, len = bytes.length, base64 = \"\";\n\n    for (i = 0; i < len; i+=3) {\n      base64 += chars[bytes[i] >> 2];\n      base64 += chars[((bytes[i] & 3) << 4) | (bytes[i + 1] >> 4)];\n      base64 += chars[((bytes[i + 1] & 15) << 2) | (bytes[i + 2] >> 6)];\n      base64 += chars[bytes[i + 2] & 63];\n    }\n\n    if ((len % 3) === 2) {\n      base64 = base64.substring(0, base64.length - 1) + \"=\";\n    } else if (len % 3 === 1) {\n      base64 = base64.substring(0, base64.length - 2) + \"==\";\n    }\n\n    return base64;\n  };\n\n  exports.decode =  function(base64) {\n    var bufferLength = base64.length * 0.75,\n    len = base64.length, i, p = 0,\n    encoded1, encoded2, encoded3, encoded4;\n\n    if (base64[base64.length - 1] === \"=\") {\n      bufferLength--;\n      if (base64[base64.length - 2] === \"=\") {\n        bufferLength--;\n      }\n    }\n\n    var arraybuffer = new ArrayBuffer(bufferLength),\n    bytes = new Uint8Array(arraybuffer);\n\n    for (i = 0; i < len; i+=4) {\n      encoded1 = lookup[base64.charCodeAt(i)];\n      encoded2 = lookup[base64.charCodeAt(i+1)];\n      encoded3 = lookup[base64.charCodeAt(i+2)];\n      encoded4 = lookup[base64.charCodeAt(i+3)];\n\n      bytes[p++] = (encoded1 << 2) | (encoded2 >> 4);\n      bytes[p++] = ((encoded2 & 15) << 4) | (encoded3 >> 2);\n      bytes[p++] = ((encoded3 & 3) << 6) | (encoded4 & 63);\n    }\n\n    return arraybuffer;\n  };\n})();\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/base64-arraybuffer/lib/base64-arraybuffer.js\n// module id = 30\n// module chunks = 0", "/**\r\n * Create a blob builder even when vendor prefixes exist\r\n */\r\n\r\nvar BlobBuilder = typeof BlobBuilder !== 'undefined' ? BlobBuilder :\r\n  typeof WebKitBlobBuilder !== 'undefined' ? WebKitBlobBuilder :\r\n  typeof MSBlobBuilder !== 'undefined' ? MSBlobBuilder :\r\n  typeof MozBlobBuilder !== 'undefined' ? MozBlobBuilder : \r\n  false;\r\n\r\n/**\r\n * Check if Blob constructor is supported\r\n */\r\n\r\nvar blobSupported = (function() {\r\n  try {\r\n    var a = new Blob(['hi']);\r\n    return a.size === 2;\r\n  } catch(e) {\r\n    return false;\r\n  }\r\n})();\r\n\r\n/**\r\n * Check if Blob constructor supports ArrayBufferViews\r\n * Fails in Safari 6, so we need to map to ArrayBuffers there.\r\n */\r\n\r\nvar blobSupportsArrayBufferView = blobSupported && (function() {\r\n  try {\r\n    var b = new Blob([new Uint8Array([1,2])]);\r\n    return b.size === 2;\r\n  } catch(e) {\r\n    return false;\r\n  }\r\n})();\r\n\r\n/**\r\n * Check if BlobBuilder is supported\r\n */\r\n\r\nvar blobBuilderSupported = BlobBuilder\r\n  && BlobBuilder.prototype.append\r\n  && BlobBuilder.prototype.getBlob;\r\n\r\n/**\r\n * Helper function that maps ArrayBufferViews to ArrayBuffers\r\n * Used by BlobBuilder constructor and old browsers that didn't\r\n * support it in the Blob constructor.\r\n */\r\n\r\nfunction mapArrayBufferViews(ary) {\r\n  return ary.map(function(chunk) {\r\n    if (chunk.buffer instanceof ArrayBuffer) {\r\n      var buf = chunk.buffer;\r\n\r\n      // if this is a subarray, make a copy so we only\r\n      // include the subarray region from the underlying buffer\r\n      if (chunk.byteLength !== buf.byteLength) {\r\n        var copy = new Uint8Array(chunk.byteLength);\r\n        copy.set(new Uint8Array(buf, chunk.byteOffset, chunk.byteLength));\r\n        buf = copy.buffer;\r\n      }\r\n\r\n      return buf;\r\n    }\r\n\r\n    return chunk;\r\n  });\r\n}\r\n\r\nfunction BlobBuilderConstructor(ary, options) {\r\n  options = options || {};\r\n\r\n  var bb = new BlobBuilder();\r\n  mapArrayBufferViews(ary).forEach(function(part) {\r\n    bb.append(part);\r\n  });\r\n\r\n  return (options.type) ? bb.getBlob(options.type) : bb.getBlob();\r\n};\r\n\r\nfunction BlobConstructor(ary, options) {\r\n  return new Blob(mapArrayBufferViews(ary), options || {});\r\n};\r\n\r\nif (typeof Blob !== 'undefined') {\r\n  BlobBuilderConstructor.prototype = Blob.prototype;\r\n  BlobConstructor.prototype = Blob.prototype;\r\n}\r\n\r\nmodule.exports = (function() {\r\n  if (blobSupported) {\r\n    return blobSupportsArrayBufferView ? Blob : BlobConstructor;\r\n  } else if (blobBuilderSupported) {\r\n    return BlobBuilderConstructor;\r\n  } else {\r\n    return undefined;\r\n  }\r\n})();\r\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/blob/index.js\n// module id = 31\n// module chunks = 0", "/**\r\n * Compiles a querystring\r\n * Returns string representation of the object\r\n *\r\n * @param {Object}\r\n * @api private\r\n */\r\n\r\nexports.encode = function (obj) {\r\n  var str = '';\r\n\r\n  for (var i in obj) {\r\n    if (obj.hasOwnProperty(i)) {\r\n      if (str.length) str += '&';\r\n      str += encodeURIComponent(i) + '=' + encodeURIComponent(obj[i]);\r\n    }\r\n  }\r\n\r\n  return str;\r\n};\r\n\r\n/**\r\n * Parses a simple querystring into an object\r\n *\r\n * @param {String} qs\r\n * @api private\r\n */\r\n\r\nexports.decode = function(qs){\r\n  var qry = {};\r\n  var pairs = qs.split('&');\r\n  for (var i = 0, l = pairs.length; i < l; i++) {\r\n    var pair = pairs[i].split('=');\r\n    qry[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);\r\n  }\r\n  return qry;\r\n};\r\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/parseqs/index.js\n// module id = 32\n// module chunks = 0", "\nmodule.exports = function(a, b){\n  var fn = function(){};\n  fn.prototype = b.prototype;\n  a.prototype = new fn;\n  a.prototype.constructor = a;\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/component-inherit/index.js\n// module id = 33\n// module chunks = 0", "'use strict';\n\nvar alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_'.split('')\n  , length = 64\n  , map = {}\n  , seed = 0\n  , i = 0\n  , prev;\n\n/**\n * Return a string representing the specified number.\n *\n * @param {Number} num The number to convert.\n * @returns {String} The string representation of the number.\n * @api public\n */\nfunction encode(num) {\n  var encoded = '';\n\n  do {\n    encoded = alphabet[num % length] + encoded;\n    num = Math.floor(num / length);\n  } while (num > 0);\n\n  return encoded;\n}\n\n/**\n * Return the integer value specified by the given string.\n *\n * @param {String} str The string to convert.\n * @returns {Number} The integer value represented by the string.\n * @api public\n */\nfunction decode(str) {\n  var decoded = 0;\n\n  for (i = 0; i < str.length; i++) {\n    decoded = decoded * length + map[str.charAt(i)];\n  }\n\n  return decoded;\n}\n\n/**\n * Yeast: A tiny growing id generator.\n *\n * @returns {String} A unique id.\n * @api public\n */\nfunction yeast() {\n  var now = encode(+new Date());\n\n  if (now !== prev) return seed = 0, prev = now;\n  return now +'.'+ encode(seed++);\n}\n\n//\n// Map each character to its index.\n//\nfor (; i < length; i++) map[alphabet[i]] = i;\n\n//\n// Expose the `yeast`, `encode` and `decode` functions.\n//\nyeast.encode = encode;\nyeast.decode = decode;\nmodule.exports = yeast;\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/yeast/index.js\n// module id = 34\n// module chunks = 0", "/**\n * Module requirements.\n */\n\nvar Polling = require('./polling');\nvar inherit = require('component-inherit');\n\n/**\n * Module exports.\n */\n\nmodule.exports = JSONPPolling;\n\n/**\n * Cached regular expressions.\n */\n\nvar rNewline = /\\n/g;\nvar rEscapedNewline = /\\\\n/g;\n\n/**\n * Global JSONP callbacks.\n */\n\nvar callbacks;\n\n/**\n * Noop.\n */\n\nfunction empty () { }\n\n/**\n * Until https://github.com/tc39/proposal-global is shipped.\n */\nfunction glob () {\n  return typeof self !== 'undefined' ? self\n      : typeof window !== 'undefined' ? window\n      : typeof global !== 'undefined' ? global : {};\n}\n\n/**\n * JSONP Polling constructor.\n *\n * @param {Object} opts.\n * @api public\n */\n\nfunction JSONPPolling (opts) {\n  Polling.call(this, opts);\n\n  this.query = this.query || {};\n\n  // define global callbacks array if not present\n  // we do this here (lazily) to avoid unneeded global pollution\n  if (!callbacks) {\n    // we need to consider multiple engines in the same page\n    var global = glob();\n    callbacks = global.___eio = (global.___eio || []);\n  }\n\n  // callback identifier\n  this.index = callbacks.length;\n\n  // add callback to jsonp global\n  var self = this;\n  callbacks.push(function (msg) {\n    self.onData(msg);\n  });\n\n  // append to query string\n  this.query.j = this.index;\n\n  // prevent spurious errors from being emitted when the window is unloaded\n  if (typeof addEventListener === 'function') {\n    addEventListener('beforeunload', function () {\n      if (self.script) self.script.onerror = empty;\n    }, false);\n  }\n}\n\n/**\n * Inherits from Polling.\n */\n\ninherit(JSONPPolling, Polling);\n\n/*\n * JSONP only supports binary as base64 encoded strings\n */\n\nJSONPPolling.prototype.supportsBinary = false;\n\n/**\n * Closes the socket.\n *\n * @api private\n */\n\nJSONPPolling.prototype.doClose = function () {\n  if (this.script) {\n    this.script.parentNode.removeChild(this.script);\n    this.script = null;\n  }\n\n  if (this.form) {\n    this.form.parentNode.removeChild(this.form);\n    this.form = null;\n    this.iframe = null;\n  }\n\n  Polling.prototype.doClose.call(this);\n};\n\n/**\n * Starts a poll cycle.\n *\n * @api private\n */\n\nJSONPPolling.prototype.doPoll = function () {\n  var self = this;\n  var script = document.createElement('script');\n\n  if (this.script) {\n    this.script.parentNode.removeChild(this.script);\n    this.script = null;\n  }\n\n  script.async = true;\n  script.src = this.uri();\n  script.onerror = function (e) {\n    self.onError('jsonp poll error', e);\n  };\n\n  var insertAt = document.getElementsByTagName('script')[0];\n  if (insertAt) {\n    insertAt.parentNode.insertBefore(script, insertAt);\n  } else {\n    (document.head || document.body).appendChild(script);\n  }\n  this.script = script;\n\n  var isUAgecko = 'undefined' !== typeof navigator && /gecko/i.test(navigator.userAgent);\n\n  if (isUAgecko) {\n    setTimeout(function () {\n      var iframe = document.createElement('iframe');\n      document.body.appendChild(iframe);\n      document.body.removeChild(iframe);\n    }, 100);\n  }\n};\n\n/**\n * Writes with a hidden iframe.\n *\n * @param {String} data to send\n * @param {Function} called upon flush.\n * @api private\n */\n\nJSONPPolling.prototype.doWrite = function (data, fn) {\n  var self = this;\n\n  if (!this.form) {\n    var form = document.createElement('form');\n    var area = document.createElement('textarea');\n    var id = this.iframeId = 'eio_iframe_' + this.index;\n    var iframe;\n\n    form.className = 'socketio';\n    form.style.position = 'absolute';\n    form.style.top = '-1000px';\n    form.style.left = '-1000px';\n    form.target = id;\n    form.method = 'POST';\n    form.setAttribute('accept-charset', 'utf-8');\n    area.name = 'd';\n    form.appendChild(area);\n    document.body.appendChild(form);\n\n    this.form = form;\n    this.area = area;\n  }\n\n  this.form.action = this.uri();\n\n  function complete () {\n    initIframe();\n    fn();\n  }\n\n  function initIframe () {\n    if (self.iframe) {\n      try {\n        self.form.removeChild(self.iframe);\n      } catch (e) {\n        self.onError('jsonp polling iframe removal error', e);\n      }\n    }\n\n    try {\n      // ie6 dynamic iframes with target=\"\" support (thanks Chris Lambacher)\n      var html = '<iframe src=\"javascript:0\" name=\"' + self.iframeId + '\">';\n      iframe = document.createElement(html);\n    } catch (e) {\n      iframe = document.createElement('iframe');\n      iframe.name = self.iframeId;\n      iframe.src = 'javascript:0';\n    }\n\n    iframe.id = self.iframeId;\n\n    self.form.appendChild(iframe);\n    self.iframe = iframe;\n  }\n\n  initIframe();\n\n  // escape \\n to prevent it from being converted into \\r\\n by some UAs\n  // double escaping is required for escaped new lines because unescaping of new lines can be done safely on server-side\n  data = data.replace(rEscapedNewline, '\\\\\\n');\n  this.area.value = data.replace(rNewline, '\\\\n');\n\n  try {\n    this.form.submit();\n  } catch (e) {}\n\n  if (this.iframe.attachEvent) {\n    this.iframe.onreadystatechange = function () {\n      if (self.iframe.readyState === 'complete') {\n        complete();\n      }\n    };\n  } else {\n    this.iframe.onload = complete;\n  }\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-client/lib/transports/polling-jsonp.js\n// module id = 35\n// module chunks = 0", "/**\n * Module dependencies.\n */\n\nvar Transport = require('../transport');\nvar parser = require('engine.io-parser');\nvar parseqs = require('parseqs');\nvar inherit = require('component-inherit');\nvar yeast = require('yeast');\nvar debug = require('debug')('engine.io-client:websocket');\n\nvar BrowserWebSocket, NodeWebSocket;\n\nif (typeof WebSocket !== 'undefined') {\n  BrowserWebSocket = WebSocket;\n} else if (typeof self !== 'undefined') {\n  BrowserWebSocket = self.WebSocket || self.MozWebSocket;\n}\n\nif (typeof window === 'undefined') {\n  try {\n    NodeWebSocket = require('ws');\n  } catch (e) { }\n}\n\n/**\n * Get either the `WebSocket` or `MozWebSocket` globals\n * in the browser or try to resolve WebSocket-compatible\n * interface exposed by `ws` for Node-like environment.\n */\n\nvar WebSocketImpl = BrowserWebSocket || NodeWebSocket;\n\n/**\n * Module exports.\n */\n\nmodule.exports = WS;\n\n/**\n * WebSocket transport constructor.\n *\n * @api {Object} connection options\n * @api public\n */\n\nfunction WS (opts) {\n  var forceBase64 = (opts && opts.forceBase64);\n  if (forceBase64) {\n    this.supportsBinary = false;\n  }\n  this.perMessageDeflate = opts.perMessageDeflate;\n  this.usingBrowserWebSocket = BrowserWebSocket && !opts.forceNode;\n  this.protocols = opts.protocols;\n  if (!this.usingBrowserWebSocket) {\n    WebSocketImpl = NodeWebSocket;\n  }\n  Transport.call(this, opts);\n}\n\n/**\n * Inherits from Transport.\n */\n\ninherit(WS, Transport);\n\n/**\n * Transport name.\n *\n * @api public\n */\n\nWS.prototype.name = 'websocket';\n\n/*\n * WebSockets support binary\n */\n\nWS.prototype.supportsBinary = true;\n\n/**\n * Opens socket.\n *\n * @api private\n */\n\nWS.prototype.doOpen = function () {\n  if (!this.check()) {\n    // let probe timeout\n    return;\n  }\n\n  var uri = this.uri();\n  var protocols = this.protocols;\n  var opts = {\n    agent: this.agent,\n    perMessageDeflate: this.perMessageDeflate\n  };\n\n  // SSL options for Node.js client\n  opts.pfx = this.pfx;\n  opts.key = this.key;\n  opts.passphrase = this.passphrase;\n  opts.cert = this.cert;\n  opts.ca = this.ca;\n  opts.ciphers = this.ciphers;\n  opts.rejectUnauthorized = this.rejectUnauthorized;\n  if (this.extraHeaders) {\n    opts.headers = this.extraHeaders;\n  }\n  if (this.localAddress) {\n    opts.localAddress = this.localAddress;\n  }\n\n  try {\n    this.ws =\n      this.usingBrowserWebSocket && !this.isReactNative\n        ? protocols\n          ? new WebSocketImpl(uri, protocols)\n          : new WebSocketImpl(uri)\n        : new WebSocketImpl(uri, protocols, opts);\n  } catch (err) {\n    return this.emit('error', err);\n  }\n\n  if (this.ws.binaryType === undefined) {\n    this.supportsBinary = false;\n  }\n\n  if (this.ws.supports && this.ws.supports.binary) {\n    this.supportsBinary = true;\n    this.ws.binaryType = 'nodebuffer';\n  } else {\n    this.ws.binaryType = 'arraybuffer';\n  }\n\n  this.addEventListeners();\n};\n\n/**\n * Adds event listeners to the socket\n *\n * @api private\n */\n\nWS.prototype.addEventListeners = function () {\n  var self = this;\n\n  this.ws.onopen = function () {\n    self.onOpen();\n  };\n  this.ws.onclose = function () {\n    self.onClose();\n  };\n  this.ws.onmessage = function (ev) {\n    self.onData(ev.data);\n  };\n  this.ws.onerror = function (e) {\n    self.onError('websocket error', e);\n  };\n};\n\n/**\n * Writes data to socket.\n *\n * @param {Array} array of packets.\n * @api private\n */\n\nWS.prototype.write = function (packets) {\n  var self = this;\n  this.writable = false;\n\n  // encodePacket efficient as it uses WS framing\n  // no need for encodePayload\n  var total = packets.length;\n  for (var i = 0, l = total; i < l; i++) {\n    (function (packet) {\n      parser.encodePacket(packet, self.supportsBinary, function (data) {\n        if (!self.usingBrowserWebSocket) {\n          // always create a new object (GH-437)\n          var opts = {};\n          if (packet.options) {\n            opts.compress = packet.options.compress;\n          }\n\n          if (self.perMessageDeflate) {\n            var len = 'string' === typeof data ? Buffer.byteLength(data) : data.length;\n            if (len < self.perMessageDeflate.threshold) {\n              opts.compress = false;\n            }\n          }\n        }\n\n        // Sometimes the websocket has already been closed but the browser didn't\n        // have a chance of informing us about it yet, in that case send will\n        // throw an error\n        try {\n          if (self.usingBrowserWebSocket) {\n            // TypeError is thrown when passing the second argument on Safari\n            self.ws.send(data);\n          } else {\n            self.ws.send(data, opts);\n          }\n        } catch (e) {\n          debug('websocket closed before onclose event');\n        }\n\n        --total || done();\n      });\n    })(packets[i]);\n  }\n\n  function done () {\n    self.emit('flush');\n\n    // fake drain\n    // defer to next tick to allow Socket to clear writeBuffer\n    setTimeout(function () {\n      self.writable = true;\n      self.emit('drain');\n    }, 0);\n  }\n};\n\n/**\n * Called upon close\n *\n * @api private\n */\n\nWS.prototype.onClose = function () {\n  Transport.prototype.onClose.call(this);\n};\n\n/**\n * Closes socket.\n *\n * @api private\n */\n\nWS.prototype.doClose = function () {\n  if (typeof this.ws !== 'undefined') {\n    this.ws.close();\n  }\n};\n\n/**\n * Generates uri for connection.\n *\n * @api private\n */\n\nWS.prototype.uri = function () {\n  var query = this.query || {};\n  var schema = this.secure ? 'wss' : 'ws';\n  var port = '';\n\n  // avoid port if default for schema\n  if (this.port && (('wss' === schema && Number(this.port) !== 443) ||\n    ('ws' === schema && Number(this.port) !== 80))) {\n    port = ':' + this.port;\n  }\n\n  // append timestamp to URI\n  if (this.timestampRequests) {\n    query[this.timestampParam] = yeast();\n  }\n\n  // communicate binary support capabilities\n  if (!this.supportsBinary) {\n    query.b64 = 1;\n  }\n\n  query = parseqs.encode(query);\n\n  // prepend ? to query\n  if (query.length) {\n    query = '?' + query;\n  }\n\n  var ipv6 = this.hostname.indexOf(':') !== -1;\n  return schema + '://' + (ipv6 ? '[' + this.hostname + ']' : this.hostname) + port + this.path + query;\n};\n\n/**\n * Feature detection for WebSocket.\n *\n * @return {Boolean} whether this transport is available.\n * @api public\n */\n\nWS.prototype.check = function () {\n  return !!WebSocketImpl && !('__initialize' in WebSocketImpl && this.name === WS.prototype.name);\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-client/lib/transports/websocket.js\n// module id = 36\n// module chunks = 0", "\nvar indexOf = [].indexOf;\n\nmodule.exports = function(arr, obj){\n  if (indexOf) return arr.indexOf(obj);\n  for (var i = 0; i < arr.length; ++i) {\n    if (arr[i] === obj) return i;\n  }\n  return -1;\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/indexof/index.js\n// module id = 38\n// module chunks = 0", "\n/**\n * Module dependencies.\n */\n\nvar parser = require('socket.io-parser');\nvar Emitter = require('component-emitter');\nvar toArray = require('to-array');\nvar on = require('./on');\nvar bind = require('component-bind');\nvar debug = require('debug')('socket.io-client:socket');\nvar parseqs = require('parseqs');\nvar hasBin = require('has-binary2');\n\n/**\n * Module exports.\n */\n\nmodule.exports = exports = Socket;\n\n/**\n * Internal events (blacklisted).\n * These events can't be emitted by the user.\n *\n * @api private\n */\n\nvar events = {\n  connect: 1,\n  connect_error: 1,\n  connect_timeout: 1,\n  connecting: 1,\n  disconnect: 1,\n  error: 1,\n  reconnect: 1,\n  reconnect_attempt: 1,\n  reconnect_failed: 1,\n  reconnect_error: 1,\n  reconnecting: 1,\n  ping: 1,\n  pong: 1\n};\n\n/**\n * Shortcut to `Emitter#emit`.\n */\n\nvar emit = Emitter.prototype.emit;\n\n/**\n * `Socket` constructor.\n *\n * @api public\n */\n\nfunction Socket (io, nsp, opts) {\n  this.io = io;\n  this.nsp = nsp;\n  this.json = this; // compat\n  this.ids = 0;\n  this.acks = {};\n  this.receiveBuffer = [];\n  this.sendBuffer = [];\n  this.connected = false;\n  this.disconnected = true;\n  this.flags = {};\n  if (opts && opts.query) {\n    this.query = opts.query;\n  }\n  if (this.io.autoConnect) this.open();\n}\n\n/**\n * Mix in `Emitter`.\n */\n\nEmitter(Socket.prototype);\n\n/**\n * Subscribe to open, close and packet events\n *\n * @api private\n */\n\nSocket.prototype.subEvents = function () {\n  if (this.subs) return;\n\n  var io = this.io;\n  this.subs = [\n    on(io, 'open', bind(this, 'onopen')),\n    on(io, 'packet', bind(this, 'onpacket')),\n    on(io, 'close', bind(this, 'onclose'))\n  ];\n};\n\n/**\n * \"Opens\" the socket.\n *\n * @api public\n */\n\nSocket.prototype.open =\nSocket.prototype.connect = function () {\n  if (this.connected) return this;\n\n  this.subEvents();\n  this.io.open(); // ensure open\n  if ('open' === this.io.readyState) this.onopen();\n  this.emit('connecting');\n  return this;\n};\n\n/**\n * Sends a `message` event.\n *\n * @return {Socket} self\n * @api public\n */\n\nSocket.prototype.send = function () {\n  var args = toArray(arguments);\n  args.unshift('message');\n  this.emit.apply(this, args);\n  return this;\n};\n\n/**\n * Override `emit`.\n * If the event is in `events`, it's emitted normally.\n *\n * @param {String} event name\n * @return {Socket} self\n * @api public\n */\n\nSocket.prototype.emit = function (ev) {\n  if (events.hasOwnProperty(ev)) {\n    emit.apply(this, arguments);\n    return this;\n  }\n\n  var args = toArray(arguments);\n  var packet = {\n    type: (this.flags.binary !== undefined ? this.flags.binary : hasBin(args)) ? parser.BINARY_EVENT : parser.EVENT,\n    data: args\n  };\n\n  packet.options = {};\n  packet.options.compress = !this.flags || false !== this.flags.compress;\n\n  // event ack callback\n  if ('function' === typeof args[args.length - 1]) {\n    debug('emitting packet with ack id %d', this.ids);\n    this.acks[this.ids] = args.pop();\n    packet.id = this.ids++;\n  }\n\n  if (this.connected) {\n    this.packet(packet);\n  } else {\n    this.sendBuffer.push(packet);\n  }\n\n  this.flags = {};\n\n  return this;\n};\n\n/**\n * Sends a packet.\n *\n * @param {Object} packet\n * @api private\n */\n\nSocket.prototype.packet = function (packet) {\n  packet.nsp = this.nsp;\n  this.io.packet(packet);\n};\n\n/**\n * Called upon engine `open`.\n *\n * @api private\n */\n\nSocket.prototype.onopen = function () {\n  debug('transport is open - connecting');\n\n  // write connect packet if necessary\n  if ('/' !== this.nsp) {\n    if (this.query) {\n      var query = typeof this.query === 'object' ? parseqs.encode(this.query) : this.query;\n      debug('sending connect packet with query %s', query);\n      this.packet({type: parser.CONNECT, query: query});\n    } else {\n      this.packet({type: parser.CONNECT});\n    }\n  }\n};\n\n/**\n * Called upon engine `close`.\n *\n * @param {String} reason\n * @api private\n */\n\nSocket.prototype.onclose = function (reason) {\n  debug('close (%s)', reason);\n  this.connected = false;\n  this.disconnected = true;\n  delete this.id;\n  this.emit('disconnect', reason);\n};\n\n/**\n * Called with socket packet.\n *\n * @param {Object} packet\n * @api private\n */\n\nSocket.prototype.onpacket = function (packet) {\n  var sameNamespace = packet.nsp === this.nsp;\n  var rootNamespaceError = packet.type === parser.ERROR && packet.nsp === '/';\n\n  if (!sameNamespace && !rootNamespaceError) return;\n\n  switch (packet.type) {\n    case parser.CONNECT:\n      this.onconnect();\n      break;\n\n    case parser.EVENT:\n      this.onevent(packet);\n      break;\n\n    case parser.BINARY_EVENT:\n      this.onevent(packet);\n      break;\n\n    case parser.ACK:\n      this.onack(packet);\n      break;\n\n    case parser.BINARY_ACK:\n      this.onack(packet);\n      break;\n\n    case parser.DISCONNECT:\n      this.ondisconnect();\n      break;\n\n    case parser.ERROR:\n      this.emit('error', packet.data);\n      break;\n  }\n};\n\n/**\n * Called upon a server event.\n *\n * @param {Object} packet\n * @api private\n */\n\nSocket.prototype.onevent = function (packet) {\n  var args = packet.data || [];\n  debug('emitting event %j', args);\n\n  if (null != packet.id) {\n    debug('attaching ack callback to event');\n    args.push(this.ack(packet.id));\n  }\n\n  if (this.connected) {\n    emit.apply(this, args);\n  } else {\n    this.receiveBuffer.push(args);\n  }\n};\n\n/**\n * Produces an ack callback to emit with an event.\n *\n * @api private\n */\n\nSocket.prototype.ack = function (id) {\n  var self = this;\n  var sent = false;\n  return function () {\n    // prevent double callbacks\n    if (sent) return;\n    sent = true;\n    var args = toArray(arguments);\n    debug('sending ack %j', args);\n\n    self.packet({\n      type: hasBin(args) ? parser.BINARY_ACK : parser.ACK,\n      id: id,\n      data: args\n    });\n  };\n};\n\n/**\n * Called upon a server acknowlegement.\n *\n * @param {Object} packet\n * @api private\n */\n\nSocket.prototype.onack = function (packet) {\n  var ack = this.acks[packet.id];\n  if ('function' === typeof ack) {\n    debug('calling ack %s with %j', packet.id, packet.data);\n    ack.apply(this, packet.data);\n    delete this.acks[packet.id];\n  } else {\n    debug('bad ack %s', packet.id);\n  }\n};\n\n/**\n * Called upon server connect.\n *\n * @api private\n */\n\nSocket.prototype.onconnect = function () {\n  this.connected = true;\n  this.disconnected = false;\n  this.emit('connect');\n  this.emitBuffered();\n};\n\n/**\n * Emit buffered events (received and emitted).\n *\n * @api private\n */\n\nSocket.prototype.emitBuffered = function () {\n  var i;\n  for (i = 0; i < this.receiveBuffer.length; i++) {\n    emit.apply(this, this.receiveBuffer[i]);\n  }\n  this.receiveBuffer = [];\n\n  for (i = 0; i < this.sendBuffer.length; i++) {\n    this.packet(this.sendBuffer[i]);\n  }\n  this.sendBuffer = [];\n};\n\n/**\n * Called upon server disconnect.\n *\n * @api private\n */\n\nSocket.prototype.ondisconnect = function () {\n  debug('server disconnect (%s)', this.nsp);\n  this.destroy();\n  this.onclose('io server disconnect');\n};\n\n/**\n * Called upon forced client/server side disconnections,\n * this method ensures the manager stops tracking us and\n * that reconnections don't get triggered for this.\n *\n * @api private.\n */\n\nSocket.prototype.destroy = function () {\n  if (this.subs) {\n    // clean subscriptions to avoid reconnections\n    for (var i = 0; i < this.subs.length; i++) {\n      this.subs[i].destroy();\n    }\n    this.subs = null;\n  }\n\n  this.io.destroy(this);\n};\n\n/**\n * Disconnects the socket manually.\n *\n * @return {Socket} self\n * @api public\n */\n\nSocket.prototype.close =\nSocket.prototype.disconnect = function () {\n  if (this.connected) {\n    debug('performing disconnect (%s)', this.nsp);\n    this.packet({ type: parser.DISCONNECT });\n  }\n\n  // remove socket from pool\n  this.destroy();\n\n  if (this.connected) {\n    // fire events\n    this.onclose('io client disconnect');\n  }\n  return this;\n};\n\n/**\n * Sets the compress flag.\n *\n * @param {Boolean} if `true`, compresses the sending data\n * @return {Socket} self\n * @api public\n */\n\nSocket.prototype.compress = function (compress) {\n  this.flags.compress = compress;\n  return this;\n};\n\n/**\n * Sets the binary flag\n *\n * @param {Boolean} whether the emitted data contains binary\n * @return {Socket} self\n * @api public\n */\n\nSocket.prototype.binary = function (binary) {\n  this.flags.binary = binary;\n  return this;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./lib/socket.js\n// module id = 39\n// module chunks = 0", "module.exports = toArray\n\nfunction toArray(list, index) {\n    var array = []\n\n    index = index || 0\n\n    for (var i = index || 0; i < list.length; i++) {\n        array[i - index] = list[i]\n    }\n\n    return array\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/to-array/index.js\n// module id = 40\n// module chunks = 0", "\n/**\n * Module exports.\n */\n\nmodule.exports = on;\n\n/**\n * Helper for subscriptions.\n *\n * @param {Object|EventEmitter} obj with `Emitter` mixin or `EventEmitter`\n * @param {String} event name\n * @param {Function} callback\n * @api public\n */\n\nfunction on (obj, ev, fn) {\n  obj.on(ev, fn);\n  return {\n    destroy: function () {\n      obj.removeListener(ev, fn);\n    }\n  };\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./lib/on.js\n// module id = 41\n// module chunks = 0", "/**\n * Slice reference.\n */\n\nvar slice = [].slice;\n\n/**\n * Bind `obj` to `fn`.\n *\n * @param {Object} obj\n * @param {Function|String} fn or string\n * @return {Function}\n * @api public\n */\n\nmodule.exports = function(obj, fn){\n  if ('string' == typeof fn) fn = obj[fn];\n  if ('function' != typeof fn) throw new Error('bind() requires a function');\n  var args = slice.call(arguments, 2);\n  return function(){\n    return fn.apply(obj, args.concat(slice.call(arguments)));\n  }\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/component-bind/index.js\n// module id = 42\n// module chunks = 0", "\n/**\n * Expose `Backoff`.\n */\n\nmodule.exports = Backoff;\n\n/**\n * Initialize backoff timer with `opts`.\n *\n * - `min` initial timeout in milliseconds [100]\n * - `max` max timeout [10000]\n * - `jitter` [0]\n * - `factor` [2]\n *\n * @param {Object} opts\n * @api public\n */\n\nfunction Backoff(opts) {\n  opts = opts || {};\n  this.ms = opts.min || 100;\n  this.max = opts.max || 10000;\n  this.factor = opts.factor || 2;\n  this.jitter = opts.jitter > 0 && opts.jitter <= 1 ? opts.jitter : 0;\n  this.attempts = 0;\n}\n\n/**\n * Return the backoff duration.\n *\n * @return {Number}\n * @api public\n */\n\nBackoff.prototype.duration = function(){\n  var ms = this.ms * Math.pow(this.factor, this.attempts++);\n  if (this.jitter) {\n    var rand =  Math.random();\n    var deviation = Math.floor(rand * this.jitter * ms);\n    ms = (Math.floor(rand * 10) & 1) == 0  ? ms - deviation : ms + deviation;\n  }\n  return Math.min(ms, this.max) | 0;\n};\n\n/**\n * Reset the number of attempts.\n *\n * @api public\n */\n\nBackoff.prototype.reset = function(){\n  this.attempts = 0;\n};\n\n/**\n * Set the minimum duration\n *\n * @api public\n */\n\nBackoff.prototype.setMin = function(min){\n  this.ms = min;\n};\n\n/**\n * Set the maximum duration\n *\n * @api public\n */\n\nBackoff.prototype.setMax = function(max){\n  this.max = max;\n};\n\n/**\n * Set the jitter\n *\n * @api public\n */\n\nBackoff.prototype.setJitter = function(jitter){\n  this.jitter = jitter;\n};\n\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/backo2/index.js\n// module id = 43\n// module chunks = 0"], "sourceRoot": ""}