{"name": "mongodb", "version": "3.6.3", "description": "The official MongoDB driver for Node.js", "main": "index.js", "files": ["index.js", "lib"], "repository": {"type": "git", "url": "**************:mongodb/node-mongodb-native.git"}, "keywords": ["mongodb", "driver", "official"], "peerOptionalDependencies": {"kerberos": "^1.1.0", "mongodb-client-encryption": "^1.0.0", "mongodb-extjson": "^2.1.2", "snappy": "^6.3.4", "bson-ext": "^2.0.0"}, "peerDependenciesMeta": {"kerberos": {"optional": true}, "mongodb-client-encryption": {"optional": true}, "mongodb-extjson": {"optional": true}, "snappy": {"optional": true}, "bson-ext": {"optional": true}, "aws4": {"optional": true}}, "dependencies": {"bl": "^2.2.1", "bson": "^1.1.4", "denque": "^1.4.1", "require_optional": "^1.0.1", "safe-buffer": "^5.1.2"}, "devDependencies": {"chai": "^4.1.1", "chai-subset": "^1.6.0", "chalk": "^2.4.2", "co": "4.6.0", "coveralls": "^2.11.6", "eslint": "^7.10.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-es": "^3.0.1", "eslint-plugin-prettier": "^3.1.3", "istanbul": "^0.4.5", "jsdoc": "3.5.5", "lodash.camelcase": "^4.3.0", "mocha": "5.2.0", "mocha-sinon": "^2.1.0", "mongodb-extjson": "^2.1.1", "mongodb-mock-server": "^1.0.1", "prettier": "^1.19.1", "semver": "^5.5.0", "sinon": "^4.3.0", "sinon-chai": "^3.2.0", "snappy": "^6.3.4", "standard-version": "^4.4.0", "util.promisify": "^1.0.1", "worker-farm": "^1.5.0", "wtfnode": "^0.8.0", "yargs": "^14.2.0"}, "license": "Apache-2.0", "engines": {"node": ">=4"}, "bugs": {"url": "https://github.com/mongodb/node-mongodb-native/issues"}, "scripts": {"atlas": "mocha --opts '{}' ./test/manual/atlas_connectivity.test.js", "check:kerberos": "mocha --opts '{}' -t 60000 test/manual/kerberos.test.js", "check:ldap": "mocha --opts '{}' test/manual/ldap.test.js", "check:tls": "mocha --opts '{}' test/manual/tls_support.test.js", "test": "npm run lint && mocha --recursive test/functional test/unit", "test-nolint": "mocha --recursive test/functional test/unit", "coverage": "istanbul cover mongodb-test-runner -- -t 60000 test/unit test/functional", "lint": "eslint -v && eslint lib test", "format": "npm run lint -- --fix", "bench": "node test/benchmarks/driverBench/", "generate-evergreen": "node .evergreen/generate_evergreen_tasks.js", "release": "standard-version -i HISTORY.md"}, "homepage": "https://github.com/mongodb/node-mongodb-native", "optionalDependencies": {"saslprep": "^1.0.0"}}