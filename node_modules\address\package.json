{"name": "address", "version": "2.0.3", "description": "Get current machine IP, MAC and DNS servers.", "scripts": {"lint": "eslint src test --ext .ts", "pretest": "npm run lint -- --fix && npm run prepublishOnly", "test": "egg-bin test", "preci": "npm run lint && npm run prepublishOnly && attw --pack --ignore-rules no-resolution", "ci": "egg-bin cov", "contributor": "git-contributor", "prepublishOnly": "tshy && tshy-after"}, "devDependencies": {"@arethetypeswrong/cli": "*", "@eggjs/tsconfig": "^1.3.3", "@types/mocha": "^10.0.1", "@types/node": "^20.6.3", "egg-bin": "^6.5.2", "eslint": "^8.49.0", "eslint-config-egg": "^13.0.0", "git-contributor": "^2.1.5", "mm": "^3.3.0", "runscript": "^1.5.3", "tshy": "^1.1.1", "tshy-after": "^1.0.0", "typescript": "^5.2.2"}, "repository": {"type": "git", "url": "git://github.com/node-modules/address.git"}, "keywords": ["address", "ip", "ipv4", "mac"], "engines": {"node": ">= 16.0.0"}, "author": "fengmk2 <<EMAIL>>", "license": "MIT", "files": ["dist", "src"], "type": "module", "tshy": {"exports": {"./package.json": "./package.json", "./promises": "./src/promises.ts", ".": "./src/index.ts"}}, "exports": {"./package.json": "./package.json", "./promises": {"import": {"source": "./src/promises.ts", "types": "./dist/esm/promises.d.ts", "default": "./dist/esm/promises.js"}, "require": {"source": "./src/promises.ts", "types": "./dist/commonjs/promises.d.ts", "default": "./dist/commonjs/promises.js"}}, ".": {"import": {"source": "./src/index.ts", "types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"source": "./src/index.ts", "types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}, "types": "./dist/commonjs/index.d.ts", "main": "./dist/commonjs/index.js"}