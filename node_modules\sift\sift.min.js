!function(n,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.sift=t():n.sift=t()}(window,function(){return function(n){var t={};function r(e){if(t[e])return t[e].exports;var o=t[e]={i:e,l:!1,exports:{}};return n[e].call(o.exports,o,o.exports,r),o.l=!0,o.exports}return r.m=n,r.c=t,r.d=function(n,t,e){r.o(n,t)||Object.defineProperty(n,t,{enumerable:!0,get:e})},r.r=function(n){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(n,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(n,"__esModule",{value:!0})},r.t=function(n,t){if(1&t&&(n=r(n)),8&t)return n;if(4&t&&"object"==typeof n&&n&&n.__esModule)return n;var e=Object.create(null);if(r.r(e),Object.defineProperty(e,"default",{enumerable:!0,value:n}),2&t&&"string"!=typeof n)for(var o in n)r.d(e,o,function(t){return n[t]}.bind(null,o));return e},r.n=function(n){var t=n&&n.__esModule?function(){return n.default}:function(){return n};return r.d(t,"a",t),t},r.o=function(n,t){return Object.prototype.hasOwnProperty.call(n,t)},r.p="",r(r.s=0)}([function(n,t,r){n.exports=r(1)},function(n,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n};function o(n){return"function"==typeof n}function u(n){return"[object Array]"===Object.prototype.toString.call(n)}function i(n){return n instanceof Date?n.getTime():u(n)?n.map(i):n&&"function"==typeof n.toJSON?n.toJSON():n}function f(n,t){return o(n.get)?n.get(t):n[t]}function c(n){return function(t,r){if(!u(r)||!r.length)return n(t,r);for(var e=0,o=r.length;e<o;e++)if(n(t,f(r,e)))return!0;return!1}}function l(n,t,r,e){return n.v(n.a,t,r,e)}t.default=function(n,t,r){o(t)&&(r=t,t=void 0);var e=y(n,r);function u(n,t,r){return l(e,n,t,r)}if(t)return t.filter(u);return u},t.indexOf=function(n,t,r){return d(t,y(n,r))},t.compare=b;var a={$eq:c(function(n,t){return n(t)}),$ne:function(n){return function(t,r){if(!u(r)||!r.length)return n(t,r);for(var e=0,o=r.length;e<o;e++)if(!n(t,f(r,e)))return!1;return!0}}(function(n,t){return!n(t)}),$gt:c(function(n,t){return b(i(t),n)>0}),$gte:c(function(n,t){return b(i(t),n)>=0}),$lt:c(function(n,t){return b(i(t),n)<0}),$lte:c(function(n,t){return b(i(t),n)<=0}),$mod:c(function(n,t){return t%n[0]==n[1]}),$in:function(n,t){if(!(t instanceof Array)){var r=i(t);if(r===t&&"object"===(void 0===t?"undefined":e(t)))for(u=n.length;u--;)if(String(n[u])===String(t)&&"[object Object]"!==String(t))return!0;if(void 0===r)for(u=n.length;u--;)if(null==n[u])return!0;for(u=n.length;u--;){var o=l(y(f(n,u),void 0),t,u,n);if(o&&"[object Object]"!==String(o)&&"[object Object]"!==String(t))return!0}return!!~n.indexOf(r)}for(var u=t.length;u--;)if(~n.indexOf(i(f(t,u))))return!0;return!1},$nin:function(n,t,r,e){return!a.$in(n,t,r,e)},$not:function(n,t,r,e){return!l(n,t,r,e)},$type:function(n,t){return void 0!=t&&(t instanceof n||t.constructor==n)},$all:function(n,t,r,e){return a.$and(n,t,r,e)},$size:function(n,t){return!!t&&n===t.length},$or:function(n,t,r,e){for(var o=0,u=n.length;o<u;o++)if(l(f(n,o),t,r,e))return!0;return!1},$nor:function(n,t,r,e){return!a.$or(n,t,r,e)},$and:function(n,t,r,e){for(var o=0,u=n.length;o<u;o++)if(!l(f(n,o),t,r,e))return!1;return!0},$regex:c(function(n,t){return"string"==typeof t&&n.test(t)}),$where:function(n,t,r,e){return n.call(t,t,r,e)},$elemMatch:function(n,t,r,e){return u(t)?!!~d(t,n):l(n,t,r,e)},$exists:function(n,t,r,e){return e.hasOwnProperty(r)===n}},p={$eq:function(n){return n instanceof RegExp?function(t){return"string"==typeof t&&n.test(t)}:n instanceof Function?n:u(n)&&!n.length?function(n){return u(n)&&!n.length}:null===n?function(n){return null==n}:function(t){return 0===b(i(t),i(n))}},$ne:function(n){return p.$eq(n)},$and:function(n){return n.map($)},$all:function(n){return p.$and(n)},$or:function(n){return n.map($)},$nor:function(n){return n.map($)},$not:function(n){return $(n)},$regex:function(n,t){return new RegExp(n,t.$options)},$where:function(n){return"string"==typeof n?new Function("obj","return "+n):n},$elemMatch:function(n){return $(n)},$exists:function(n){return!!n}};function d(n,t){for(var r=0;r<n.length;r++){f(n,r);if(l(t,f(n,r)))return r}return-1}function s(n,t){return{a:n,v:t}}function v(n,t){var r=[];if(function n(t,r,e,o,i){if(e===r.length||void 0==t)return void i.push([t,r[e-1],o]);var c=f(r,e);if(u(t)&&isNaN(Number(c)))for(var l=0,a=t.length;l<a;l++)n(f(t,l),r,e,t,i);else n(f(t,c),r,e+1,t,i)}(t,n.k,0,t,r),1===r.length){var e=r[0];return l(n.nv,e[0],e[1],e[2])}for(var o=n&&n.q&&void 0!==n.q.$ne,i=o,c=0;c<r.length;c++){var a=r[c],p=l(n.nv,a[0],a[1],a[2]);o?i&=p:i|=p}return i}function g(n,t,r){return{a:{k:n,nv:t,q:r},v:v}}function $(n){(n=i(n))&&function(n){return n&&n.constructor===Object}(n)||(n={$eq:n});var t=[];for(var r in n){var e=n[r];if("$options"!==r)if(a[r])p[r]&&(e=p[r](e,n)),t.push(s(i(e),a[r]));else{if(36===r.charCodeAt(0))throw new Error("Unknown operation "+r);t.push(g(r.split("."),$(e),e))}}return 1===t.length?t[0]:s(t,a.$and)}function y(n,t){var r=$(n);return t&&(r={a:r,v:function(n,r,e,o){return l(n,t(r),e,o)}}),r}function b(n,t){if(n===t)return 0;if((void 0===n?"undefined":e(n))===(void 0===t?"undefined":e(t))){if(n>t)return 1;if(n<t)return-1}}}])});