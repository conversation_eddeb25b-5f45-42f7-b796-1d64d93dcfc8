{"name": "lcoation", "version": "0.0.2", "private": true, "description": "This example is so cool.", "main": "app.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["example"], "author": "mahdislama", "license": "MIT", "dependencies": {"address": "^2.0.3", "express": "", "fs": "0.0.1-security", "has-binary2": "^2.0.0", "http": "", "https": "^1.0.0", "mongoose": "^5.8.4", "multer": "^1.4.2", "sanitize-html": "^2.4.0", "socket.io": "2.3"}, "engines": {"node": "8.9.4"}}